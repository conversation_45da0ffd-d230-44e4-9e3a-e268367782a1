// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:flutter_cache_manager/flutter_cache_manager.dart' as _i8;
import 'package:get_it/get_it.dart' as _i1;
import 'package:injectable/injectable.dart' as _i2;
import 'package:theadvance/src/core/network/ez_network.dart' as _i11;
import 'package:theadvance/src/core/routes/app_router.dart' as _i10;
import 'package:theadvance/src/core/utils/deeplink_helper.dart' as _i7;
import 'package:theadvance/src/core/utils/mappers.dart' as _i6;
import 'package:theadvance/src/core/utils/stringee_helper.dart' as _i5;
import 'package:theadvance/src/data/datasources/ez_datasources.dart' as _i9;
import 'package:theadvance/src/data/datasources/local/add_tags_image/add_tags_image_dao.dart'
    as _i86;
import 'package:theadvance/src/data/datasources/local/add_tags_image/add_tags_image_dao_impl.dart'
    as _i87;
import 'package:theadvance/src/data/datasources/local/assign_task/assign_task_dao.dart'
    as _i78;
import 'package:theadvance/src/data/datasources/local/assign_task/assign_task_dao_impl.dart'
    as _i79;
import 'package:theadvance/src/data/datasources/local/branch_chat_list/branch_chat_list_dao.dart'
    as _i60;
import 'package:theadvance/src/data/datasources/local/branch_chat_list/branch_chat_list_dao_impl.dart'
    as _i61;
import 'package:theadvance/src/data/datasources/local/branch_selection/branch_selection_dao.dart'
    as _i42;
import 'package:theadvance/src/data/datasources/local/branch_selection/branch_selection_dao_impl.dart'
    as _i43;
import 'package:theadvance/src/data/datasources/local/cache/hive/ez_cache.dart'
    as _i23;
import 'package:theadvance/src/data/datasources/local/chat/chat_dao.dart'
    as _i150;
import 'package:theadvance/src/data/datasources/local/chat/chat_dao_impl.dart'
    as _i151;
import 'package:theadvance/src/data/datasources/local/chat_list/chat_list_dao.dart'
    as _i94;
import 'package:theadvance/src/data/datasources/local/chat_list/chat_list_dao_impl.dart'
    as _i95;
import 'package:theadvance/src/data/datasources/local/chat_select_branch/chat_select_branch_dao.dart'
    as _i114;
import 'package:theadvance/src/data/datasources/local/chat_select_branch/chat_select_branch_dao_impl.dart'
    as _i115;
import 'package:theadvance/src/data/datasources/local/checkin_photo/checkin_photo_dao.dart'
    as _i28;
import 'package:theadvance/src/data/datasources/local/checkin_photo/checkin_photo_dao_impl.dart'
    as _i29;
import 'package:theadvance/src/data/datasources/local/consultation_customer/consultation_customer_dao.dart'
    as _i80;
import 'package:theadvance/src/data/datasources/local/consultation_customer/consultation_customer_dao_impl.dart'
    as _i81;
import 'package:theadvance/src/data/datasources/local/consultation_manager/consultation_manager_dao.dart'
    as _i96;
import 'package:theadvance/src/data/datasources/local/consultation_manager/consultation_manager_dao_impl.dart'
    as _i97;
import 'package:theadvance/src/data/datasources/local/create_chat_folder/create_chat_folder_dao.dart'
    as _i62;
import 'package:theadvance/src/data/datasources/local/create_chat_folder/create_chat_folder_dao_impl.dart'
    as _i63;
import 'package:theadvance/src/data/datasources/local/create_chat_group/create_chat_group_dao.dart'
    as _i104;
import 'package:theadvance/src/data/datasources/local/create_chat_group/create_chat_group_dao_impl.dart'
    as _i105;
import 'package:theadvance/src/data/datasources/local/create_customer/create_customer_dao.dart'
    as _i98;
import 'package:theadvance/src/data/datasources/local/create_customer/create_customer_dao_impl.dart'
    as _i99;
import 'package:theadvance/src/data/datasources/local/customer_booking_info/customer_booking_info_dao.dart'
    as _i100;
import 'package:theadvance/src/data/datasources/local/customer_booking_info/customer_booking_info_dao_impl.dart'
    as _i101;
import 'package:theadvance/src/data/datasources/local/customer_info_details/customer_info_details_dao.dart'
    as _i134;
import 'package:theadvance/src/data/datasources/local/customer_info_details/customer_info_details_dao_impl.dart'
    as _i135;
import 'package:theadvance/src/data/datasources/local/customer_list/customer_list_dao.dart'
    as _i52;
import 'package:theadvance/src/data/datasources/local/customer_list/customer_list_dao_impl.dart'
    as _i53;
import 'package:theadvance/src/data/datasources/local/customer_profile/customer_profile_dao.dart'
    as _i92;
import 'package:theadvance/src/data/datasources/local/customer_profile/customer_profile_dao_impl.dart'
    as _i93;
import 'package:theadvance/src/data/datasources/local/customer_record/customer_record_dao.dart'
    as _i110;
import 'package:theadvance/src/data/datasources/local/customer_record/customer_record_dao_impl.dart'
    as _i111;
import 'package:theadvance/src/data/datasources/local/customer_schedule/customer_schedule_dao.dart'
    as _i56;
import 'package:theadvance/src/data/datasources/local/customer_schedule/customer_schedule_dao_impl.dart'
    as _i57;
import 'package:theadvance/src/data/datasources/local/detail_crm_customer/detail_crm_customer_dao.dart'
    as _i58;
import 'package:theadvance/src/data/datasources/local/detail_crm_customer/detail_crm_customer_dao_impl.dart'
    as _i59;
import 'package:theadvance/src/data/datasources/local/detail_staff_evaluation_period/detail_staff_evaluation_period_dao.dart'
    as _i140;
import 'package:theadvance/src/data/datasources/local/detail_staff_evaluation_period/detail_staff_evaluation_period_dao_impl.dart'
    as _i141;
import 'package:theadvance/src/data/datasources/local/dev/dev_dao.dart' as _i84;
import 'package:theadvance/src/data/datasources/local/dev/dev_dao_impl.dart'
    as _i85;
import 'package:theadvance/src/data/datasources/local/feedback/feedback_dao.dart'
    as _i64;
import 'package:theadvance/src/data/datasources/local/feedback/feedback_dao_impl.dart'
    as _i65;
import 'package:theadvance/src/data/datasources/local/group_chat_detail/group_chat_detail_dao.dart'
    as _i26;
import 'package:theadvance/src/data/datasources/local/group_chat_detail/group_chat_detail_dao_impl.dart'
    as _i27;
import 'package:theadvance/src/data/datasources/local/hr_organization/hr_organization_dao.dart'
    as _i34;
import 'package:theadvance/src/data/datasources/local/hr_organization/hr_organization_dao_impl.dart'
    as _i35;
import 'package:theadvance/src/data/datasources/local/important_notes/important_notes_dao.dart'
    as _i120;
import 'package:theadvance/src/data/datasources/local/important_notes/important_notes_dao_impl.dart'
    as _i121;
import 'package:theadvance/src/data/datasources/local/list_customer/list_customer_dao.dart'
    as _i108;
import 'package:theadvance/src/data/datasources/local/list_customer/list_customer_dao_impl.dart'
    as _i109;
import 'package:theadvance/src/data/datasources/local/location_google/location_google_dao.dart'
    as _i24;
import 'package:theadvance/src/data/datasources/local/location_google/location_google_dao_impl.dart'
    as _i25;
import 'package:theadvance/src/data/datasources/local/medical_department_list/medical_department_list_dao.dart'
    as _i88;
import 'package:theadvance/src/data/datasources/local/medical_department_list/medical_department_list_dao_impl.dart'
    as _i89;
import 'package:theadvance/src/data/datasources/local/medical_log_detail/medical_log_detail_dao.dart'
    as _i30;
import 'package:theadvance/src/data/datasources/local/medical_log_detail/medical_log_detail_dao_impl.dart'
    as _i31;
import 'package:theadvance/src/data/datasources/local/medical_product_creation/medical_product_creation_dao.dart'
    as _i50;
import 'package:theadvance/src/data/datasources/local/medical_product_creation/medical_product_creation_dao_impl.dart'
    as _i51;
import 'package:theadvance/src/data/datasources/local/medical_service_creation/medical_service_creation_dao.dart'
    as _i19;
import 'package:theadvance/src/data/datasources/local/medical_service_creation/medical_service_creation_dao_impl.dart'
    as _i20;
import 'package:theadvance/src/data/datasources/local/medical_service_list/medical_service_list_dao.dart'
    as _i82;
import 'package:theadvance/src/data/datasources/local/medical_service_list/medical_service_list_dao_impl.dart'
    as _i83;
import 'package:theadvance/src/data/datasources/local/medical_service_log_list/medical_service_log_list_dao.dart'
    as _i54;
import 'package:theadvance/src/data/datasources/local/medical_service_log_list/medical_service_log_list_dao_impl.dart'
    as _i55;
import 'package:theadvance/src/data/datasources/local/medical_template_list/medical_template_list_dao.dart'
    as _i122;
import 'package:theadvance/src/data/datasources/local/medical_template_list/medical_template_list_dao_impl.dart'
    as _i123;
import 'package:theadvance/src/data/datasources/local/medicine_detail/medicine_detail_dao.dart'
    as _i112;
import 'package:theadvance/src/data/datasources/local/medicine_detail/medicine_detail_dao_impl.dart'
    as _i113;
import 'package:theadvance/src/data/datasources/local/note_details/note_details_dao.dart'
    as _i102;
import 'package:theadvance/src/data/datasources/local/note_details/note_details_dao_impl.dart'
    as _i103;
import 'package:theadvance/src/data/datasources/local/notification_list/notification_list_dao.dart'
    as _i136;
import 'package:theadvance/src/data/datasources/local/notification_list/notification_list_dao_impl.dart'
    as _i137;
import 'package:theadvance/src/data/datasources/local/product_confirm/product_confirm_dao.dart'
    as _i106;
import 'package:theadvance/src/data/datasources/local/product_confirm/product_confirm_dao_impl.dart'
    as _i107;
import 'package:theadvance/src/data/datasources/local/px_list/px_list_dao.dart'
    as _i38;
import 'package:theadvance/src/data/datasources/local/px_list/px_list_dao_impl.dart'
    as _i39;
import 'package:theadvance/src/data/datasources/local/px_recheck/px_recheck_dao.dart'
    as _i32;
import 'package:theadvance/src/data/datasources/local/px_recheck/px_recheck_dao_impl.dart'
    as _i33;
import 'package:theadvance/src/data/datasources/local/px_task_list/px_task_list_dao.dart'
    as _i21;
import 'package:theadvance/src/data/datasources/local/px_task_list/px_task_list_dao_impl.dart'
    as _i22;
import 'package:theadvance/src/data/datasources/local/px_unasigned/px_unasigned_dao.dart'
    as _i48;
import 'package:theadvance/src/data/datasources/local/px_unasigned/px_unasigned_dao_impl.dart'
    as _i49;
import 'package:theadvance/src/data/datasources/local/px_unasigned_update/px_unasigned_update_dao.dart'
    as _i76;
import 'package:theadvance/src/data/datasources/local/px_unasigned_update/px_unasigned_update_dao_impl.dart'
    as _i77;
import 'package:theadvance/src/data/datasources/local/schedule_details/schedule_details_dao.dart'
    as _i118;
import 'package:theadvance/src/data/datasources/local/schedule_details/schedule_details_dao_impl.dart'
    as _i119;
import 'package:theadvance/src/data/datasources/local/select_px_room/select_px_room_dao.dart'
    as _i132;
import 'package:theadvance/src/data/datasources/local/select_px_room/select_px_room_dao_impl.dart'
    as _i133;
import 'package:theadvance/src/data/datasources/local/service_and_product/service_and_product_dao.dart'
    as _i46;
import 'package:theadvance/src/data/datasources/local/service_and_product/service_and_product_dao_impl.dart'
    as _i47;
import 'package:theadvance/src/data/datasources/local/staff_evaluation_periods/staff_evaluation_periods_dao.dart'
    as _i138;
import 'package:theadvance/src/data/datasources/local/staff_evaluation_periods/staff_evaluation_periods_dao_impl.dart'
    as _i139;
import 'package:theadvance/src/data/datasources/local/story_detail/story_detail_dao.dart'
    as _i116;
import 'package:theadvance/src/data/datasources/local/story_detail/story_detail_dao_impl.dart'
    as _i117;
import 'package:theadvance/src/data/datasources/local/tag_list/tag_list_dao.dart'
    as _i12;
import 'package:theadvance/src/data/datasources/local/tag_list/tag_list_dao_impl.dart'
    as _i13;
import 'package:theadvance/src/data/datasources/local/taking_care_customer/taking_care_customer_dao.dart'
    as _i36;
import 'package:theadvance/src/data/datasources/local/taking_care_customer/taking_care_customer_dao_impl.dart'
    as _i37;
import 'package:theadvance/src/data/datasources/local/ticket_detail/ticket_detail_dao.dart'
    as _i148;
import 'package:theadvance/src/data/datasources/local/ticket_detail/ticket_detail_dao_impl.dart'
    as _i149;
import 'package:theadvance/src/data/datasources/local/user_list/user_list_dao.dart'
    as _i128;
import 'package:theadvance/src/data/datasources/local/user_list/user_list_dao_impl.dart'
    as _i129;
import 'package:theadvance/src/data/datasources/remote/api_services.dart'
    as _i18;
import 'package:theadvance/src/data/datasources/remote/chat_api_service.dart'
    as _i145;
import 'package:theadvance/src/data/datasources/remote/comment_list_api_service.dart'
    as _i15;
import 'package:theadvance/src/data/datasources/remote/create_chat_group_api_service.dart'
    as _i190;
import 'package:theadvance/src/data/datasources/remote/like_list_api_service.dart'
    as _i16;
import 'package:theadvance/src/data/datasources/remote/media_upload_api_service.dart'
    as _i144;
import 'package:theadvance/src/data/datasources/remote/note_list_api_service.dart'
    as _i189;
import 'package:theadvance/src/data/datasources/remote/rating_human_api_service.dart'
    as _i188;
import 'package:theadvance/src/data/datasources/remote/schedule_details_api_service.dart'
    as _i433;
import 'package:theadvance/src/data/datasources/remote/social_upload_api_service.dart'
    as _i146;
import 'package:theadvance/src/data/datasources/remote/sticker_social_api_service.dart'
    as _i147;
import 'package:theadvance/src/data/datasources/remote/story_list_api_service.dart'
    as _i14;
import 'package:theadvance/src/data/datasources/remote/story_person_list_api_service.dart'
    as _i17;
import 'package:theadvance/src/data/datasources/remote/tag_image_service.dart'
    as _i193;
import 'package:theadvance/src/data/datasources/remote/ticket_active_api_service.dart'
    as _i192;
import 'package:theadvance/src/data/datasources/remote/ticket_api_service.dart'
    as _i191;
import 'package:theadvance/src/data/repositories/add_tags_image_repository_impl.dart'
    as _i276;
import 'package:theadvance/src/data/repositories/assign_task_repository_impl.dart'
    as _i654;
import 'package:theadvance/src/data/repositories/branch_chat_list_repository_impl.dart'
    as _i422;
import 'package:theadvance/src/data/repositories/branch_selection_repository_impl.dart'
    as _i201;
import 'package:theadvance/src/data/repositories/chat_list_repository_impl.dart'
    as _i41;
import 'package:theadvance/src/data/repositories/chat_repository_impl.dart'
    as _i180;
import 'package:theadvance/src/data/repositories/chat_select_branch_repository_impl.dart'
    as _i337;
import 'package:theadvance/src/data/repositories/checkin_photo_repository_impl.dart'
    as _i255;
import 'package:theadvance/src/data/repositories/checkin_repository_impl.dart'
    as _i399;
import 'package:theadvance/src/data/repositories/comment_list_repository_impl.dart'
    as _i472;
import 'package:theadvance/src/data/repositories/consultation_customer_repository_impl.dart'
    as _i271;
import 'package:theadvance/src/data/repositories/consultation_manager_repository_impl.dart'
    as _i645;
import 'package:theadvance/src/data/repositories/create_chat_folder_repository_impl.dart'
    as _i127;
import 'package:theadvance/src/data/repositories/create_chat_group_repository_impl.dart'
    as _i45;
import 'package:theadvance/src/data/repositories/create_customer_repository_impl.dart'
    as _i397;
import 'package:theadvance/src/data/repositories/customer_booking_info_repository_impl.dart'
    as _i384;
import 'package:theadvance/src/data/repositories/customer_info_details_repository_impl.dart'
    as _i568;
import 'package:theadvance/src/data/repositories/customer_list_repository_impl.dart'
    as _i386;
import 'package:theadvance/src/data/repositories/customer_profile_repository_impl.dart'
    as _i407;
import 'package:theadvance/src/data/repositories/customer_record_repository_impl.dart'
    as _i327;
import 'package:theadvance/src/data/repositories/customer_repository_impl.dart'
    as _i295;
import 'package:theadvance/src/data/repositories/customer_schedule_repository_impl.dart'
    as _i519;
import 'package:theadvance/src/data/repositories/detail_crm_customer_repository_impl.dart'
    as _i374;
import 'package:theadvance/src/data/repositories/detail_staff_evaluation_period_repository_impl.dart'
    as _i207;
import 'package:theadvance/src/data/repositories/dev_repository_impl.dart'
    as _i353;
import 'package:theadvance/src/data/repositories/eform_repository_impl.dart'
    as _i491;
import 'package:theadvance/src/data/repositories/feedback_repository_impl.dart'
    as _i274;
import 'package:theadvance/src/data/repositories/food_repository_impl.dart'
    as _i199;
import 'package:theadvance/src/data/repositories/group_chat_detail_repository_impl.dart'
    as _i174;
import 'package:theadvance/src/data/repositories/helper_repository_impl.dart'
    as _i131;
import 'package:theadvance/src/data/repositories/home_repository_impl.dart'
    as _i458;
import 'package:theadvance/src/data/repositories/hr_organization_repository_impl.dart'
    as _i570;
import 'package:theadvance/src/data/repositories/important_notes_repository_impl.dart'
    as _i209;
import 'package:theadvance/src/data/repositories/kpi_employee_repository_impl.dart'
    as _i521;
import 'package:theadvance/src/data/repositories/like_list_repository_impl.dart'
    as _i197;
import 'package:theadvance/src/data/repositories/list_customer_repository_impl.dart'
    as _i429;
import 'package:theadvance/src/data/repositories/location_google_repository_impl.dart'
    as _i355;
import 'package:theadvance/src/data/repositories/media_upload_repository_impl.dart'
    as _i382;
import 'package:theadvance/src/data/repositories/medical_department_list_repository_impl.dart'
    as _i205;
import 'package:theadvance/src/data/repositories/medical_log_detail_repository_impl.dart'
    as _i512;
import 'package:theadvance/src/data/repositories/medical_product_create_repository_impl.dart'
    as _i456;
import 'package:theadvance/src/data/repositories/medical_service_create_repository_impl.dart'
    as _i702;
import 'package:theadvance/src/data/repositories/medical_service_list_repository_impl.dart'
    as _i369;
import 'package:theadvance/src/data/repositories/medical_service_log_list_repository_impl.dart'
    as _i656;
import 'package:theadvance/src/data/repositories/medical_template_list_repository_impl.dart'
    as _i678;
import 'package:theadvance/src/data/repositories/medicine_detail_repository_impl.dart'
    as _i213;
import 'package:theadvance/src/data/repositories/news_repository_impl.dart'
    as _i395;
import 'package:theadvance/src/data/repositories/note_details_repository_impl.dart'
    as _i419;
import 'package:theadvance/src/data/repositories/notification_list_repository_impl.dart'
    as _i91;
import 'package:theadvance/src/data/repositories/notification_repository_impl.dart'
    as _i489;
import 'package:theadvance/src/data/repositories/product_confirm_repository_impl.dart'
    as _i371;
import 'package:theadvance/src/data/repositories/px_list_repository_impl.dart'
    as _i349;
import 'package:theadvance/src/data/repositories/px_recheck_repository_impl.dart'
    as _i388;
import 'package:theadvance/src/data/repositories/px_task_list_repository_impl.dart'
    as _i257;
import 'package:theadvance/src/data/repositories/px_unasigned_repository_impl.dart'
    as _i446;
import 'package:theadvance/src/data/repositories/px_unasigned_update_repository_impl.dart'
    as _i553;
import 'package:theadvance/src/data/repositories/rating_human_repository_impl.dart'
    as _i261;
import 'package:theadvance/src/data/repositories/request_repository_impl.dart'
    as _i692;
import 'package:theadvance/src/data/repositories/schedule_details_repository_impl.dart'
    as _i432;
import 'package:theadvance/src/data/repositories/select_px_room_repository_impl.dart'
    as _i195;
import 'package:theadvance/src/data/repositories/service_and_product_repository_impl.dart'
    as _i503;
import 'package:theadvance/src/data/repositories/setting_repository_impl.dart'
    as _i143;
import 'package:theadvance/src/data/repositories/staff_evaluation_periods_repository_impl.dart'
    as _i444;
import 'package:theadvance/src/data/repositories/staff_repository_impl.dart'
    as _i450;
import 'package:theadvance/src/data/repositories/sticker_social_repository_impl.dart'
    as _i187;
import 'package:theadvance/src/data/repositories/story_detail_repository_impl.dart'
    as _i125;
import 'package:theadvance/src/data/repositories/story_list_repository_impl.dart'
    as _i172;
import 'package:theadvance/src/data/repositories/story_person_list_repository_impl.dart'
    as _i170;
import 'package:theadvance/src/data/repositories/tag_image_repository_impl.dart'
    as _i704;
import 'package:theadvance/src/data/repositories/tag_list_repository_impl.dart'
    as _i153;
import 'package:theadvance/src/data/repositories/taking_care_customer_repository_impl.dart'
    as _i376;
import 'package:theadvance/src/data/repositories/task_repository_impl.dart'
    as _i351;
import 'package:theadvance/src/data/repositories/ticket_active_repository_impl.dart'
    as _i203;
import 'package:theadvance/src/data/repositories/ticket_detail_repository_impl.dart'
    as _i390;
import 'package:theadvance/src/data/repositories/ticketv2_repository_impl.dart'
    as _i259;
import 'package:theadvance/src/data/repositories/user_list_repository_impl.dart'
    as _i555;
import 'package:theadvance/src/data/repositories/user_repository_impl.dart'
    as _i269;
import 'package:theadvance/src/data/repositories/user_ticket_repository_impl.dart'
    as _i211;
import 'package:theadvance/src/domain/repositories/add_tags_image_repository.dart'
    as _i275;
import 'package:theadvance/src/domain/repositories/assign_task_repository.dart'
    as _i653;
import 'package:theadvance/src/domain/repositories/branch_chat_list_repository.dart'
    as _i421;
import 'package:theadvance/src/domain/repositories/branch_selection_repository.dart'
    as _i200;
import 'package:theadvance/src/domain/repositories/chat_list_repository.dart'
    as _i40;
import 'package:theadvance/src/domain/repositories/chat_repository.dart'
    as _i179;
import 'package:theadvance/src/domain/repositories/chat_select_branch_repository.dart'
    as _i336;
import 'package:theadvance/src/domain/repositories/checkin_photo_repository.dart'
    as _i254;
import 'package:theadvance/src/domain/repositories/checkin_repository.dart'
    as _i398;
import 'package:theadvance/src/domain/repositories/comment_list_repository.dart'
    as _i471;
import 'package:theadvance/src/domain/repositories/consultation_customer_repository.dart'
    as _i270;
import 'package:theadvance/src/domain/repositories/consultation_manager_repository.dart'
    as _i644;
import 'package:theadvance/src/domain/repositories/create_chat_folder_repository.dart'
    as _i126;
import 'package:theadvance/src/domain/repositories/create_chat_group_repository.dart'
    as _i44;
import 'package:theadvance/src/domain/repositories/create_customer_repository.dart'
    as _i396;
import 'package:theadvance/src/domain/repositories/customer_booking_info_repository.dart'
    as _i383;
import 'package:theadvance/src/domain/repositories/customer_info_details_repository.dart'
    as _i567;
import 'package:theadvance/src/domain/repositories/customer_list_repository.dart'
    as _i385;
import 'package:theadvance/src/domain/repositories/customer_profile_repository.dart'
    as _i406;
import 'package:theadvance/src/domain/repositories/customer_record_repository.dart'
    as _i326;
import 'package:theadvance/src/domain/repositories/customer_repository.dart'
    as _i294;
import 'package:theadvance/src/domain/repositories/customer_schedule_repository.dart'
    as _i518;
import 'package:theadvance/src/domain/repositories/detail_crm_customer_repository.dart'
    as _i373;
import 'package:theadvance/src/domain/repositories/detail_staff_evaluation_period_repository.dart'
    as _i206;
import 'package:theadvance/src/domain/repositories/dev_repository.dart'
    as _i352;
import 'package:theadvance/src/domain/repositories/eform_repository.dart'
    as _i490;
import 'package:theadvance/src/domain/repositories/feedback_repository.dart'
    as _i273;
import 'package:theadvance/src/domain/repositories/food_repository.dart'
    as _i198;
import 'package:theadvance/src/domain/repositories/group_chat_detail_repository.dart'
    as _i173;
import 'package:theadvance/src/domain/repositories/helper_repository.dart'
    as _i130;
import 'package:theadvance/src/domain/repositories/home_repository.dart'
    as _i457;
import 'package:theadvance/src/domain/repositories/hr_organization_repository.dart'
    as _i569;
import 'package:theadvance/src/domain/repositories/important_notes_repository.dart'
    as _i208;
import 'package:theadvance/src/domain/repositories/kpi_employee_repository.dart'
    as _i520;
import 'package:theadvance/src/domain/repositories/like_list_repository.dart'
    as _i196;
import 'package:theadvance/src/domain/repositories/list_customer_repository.dart'
    as _i428;
import 'package:theadvance/src/domain/repositories/location_google_repository.dart'
    as _i354;
import 'package:theadvance/src/domain/repositories/media_upload_repository.dart'
    as _i381;
import 'package:theadvance/src/domain/repositories/medical_department_list_repository.dart'
    as _i204;
import 'package:theadvance/src/domain/repositories/medical_log_detail_repository.dart'
    as _i511;
import 'package:theadvance/src/domain/repositories/medical_product_create_repository.dart'
    as _i455;
import 'package:theadvance/src/domain/repositories/medical_service_create_repository.dart'
    as _i701;
import 'package:theadvance/src/domain/repositories/medical_service_list_repository.dart'
    as _i368;
import 'package:theadvance/src/domain/repositories/medical_service_log_list_repository.dart'
    as _i655;
import 'package:theadvance/src/domain/repositories/medical_template_list_repository.dart'
    as _i677;
import 'package:theadvance/src/domain/repositories/medicine_detail_repository.dart'
    as _i212;
import 'package:theadvance/src/domain/repositories/news_repository.dart'
    as _i394;
import 'package:theadvance/src/domain/repositories/note_details_repository.dart'
    as _i418;
import 'package:theadvance/src/domain/repositories/notification_list_repository.dart'
    as _i90;
import 'package:theadvance/src/domain/repositories/notification_repository.dart'
    as _i488;
import 'package:theadvance/src/domain/repositories/product_confirm_repository.dart'
    as _i370;
import 'package:theadvance/src/domain/repositories/px_list_repository.dart'
    as _i348;
import 'package:theadvance/src/domain/repositories/px_recheck_repository.dart'
    as _i387;
import 'package:theadvance/src/domain/repositories/px_task_list_repository.dart'
    as _i256;
import 'package:theadvance/src/domain/repositories/px_unasigned_repository.dart'
    as _i445;
import 'package:theadvance/src/domain/repositories/px_unasigned_update_repository.dart'
    as _i552;
import 'package:theadvance/src/domain/repositories/rating_human_repository.dart'
    as _i260;
import 'package:theadvance/src/domain/repositories/request_repository.dart'
    as _i691;
import 'package:theadvance/src/domain/repositories/schedule_details_repository.dart'
    as _i431;
import 'package:theadvance/src/domain/repositories/select_px_room_repository.dart'
    as _i194;
import 'package:theadvance/src/domain/repositories/service_and_product_repository.dart'
    as _i502;
import 'package:theadvance/src/domain/repositories/setting_repository.dart'
    as _i142;
import 'package:theadvance/src/domain/repositories/staff_evaluation_periods_repository.dart'
    as _i443;
import 'package:theadvance/src/domain/repositories/staff_repository.dart'
    as _i449;
import 'package:theadvance/src/domain/repositories/sticker_social_repository.dart'
    as _i186;
import 'package:theadvance/src/domain/repositories/story_detail_repository.dart'
    as _i124;
import 'package:theadvance/src/domain/repositories/story_list_repository.dart'
    as _i171;
import 'package:theadvance/src/domain/repositories/story_person_list_repository.dart'
    as _i169;
import 'package:theadvance/src/domain/repositories/tag_image_repository.dart'
    as _i703;
import 'package:theadvance/src/domain/repositories/tag_list_repository.dart'
    as _i152;
import 'package:theadvance/src/domain/repositories/taking_care_customer_repository.dart'
    as _i375;
import 'package:theadvance/src/domain/repositories/task_repository.dart'
    as _i350;
import 'package:theadvance/src/domain/repositories/ticket_active_repository.dart'
    as _i202;
import 'package:theadvance/src/domain/repositories/ticket_detail_repository.dart'
    as _i389;
import 'package:theadvance/src/domain/repositories/ticketv2_repository.dart'
    as _i258;
import 'package:theadvance/src/domain/repositories/tracking_repository.dart'
    as _i67;
import 'package:theadvance/src/domain/repositories/user_list_repository.dart'
    as _i554;
import 'package:theadvance/src/domain/repositories/user_repository.dart'
    as _i268;
import 'package:theadvance/src/domain/repositories/user_ticker_repository.dart'
    as _i210;
import 'package:theadvance/src/domain/usecases/add_tags_image/create_image_tag_add_tags_image_usecase.dart'
    as _i622;
import 'package:theadvance/src/domain/usecases/add_tags_image/create_merge_image_add_tags_image_usecase.dart'
    as _i623;
import 'package:theadvance/src/domain/usecases/add_tags_image/get_add_tags_image_usecase.dart'
    as _i629;
import 'package:theadvance/src/domain/usecases/add_tags_image/get_image_list_add_tags_image_usecase.dart'
    as _i630;
import 'package:theadvance/src/domain/usecases/add_tags_image/get_room_list_add_tags_image_usecase.dart'
    as _i621;
import 'package:theadvance/src/domain/usecases/add_tags_image/get_saved_add_tags_image_usecase.dart'
    as _i625;
import 'package:theadvance/src/domain/usecases/add_tags_image/get_tag_list_add_tags_image_usecase.dart'
    as _i627;
import 'package:theadvance/src/domain/usecases/add_tags_image/remove_add_tags_image_usecase.dart'
    as _i626;
import 'package:theadvance/src/domain/usecases/add_tags_image/save_add_tags_image_usecase.dart'
    as _i624;
import 'package:theadvance/src/domain/usecases/add_tags_image/user_search_add_tags_image_usecase.dart'
    as _i628;
import 'package:theadvance/src/domain/usecases/assign_task/create_assign_task_usecase.dart'
    as _i964;
import 'package:theadvance/src/domain/usecases/assign_task/delete_assign_task_usecase.dart'
    as _i961;
import 'package:theadvance/src/domain/usecases/assign_task/get_assign_task_usecase.dart'
    as _i966;
import 'package:theadvance/src/domain/usecases/assign_task/get_saved_assign_task_usecase.dart'
    as _i963;
import 'package:theadvance/src/domain/usecases/assign_task/get_staff_assign_task_usecase.dart'
    as _i968;
import 'package:theadvance/src/domain/usecases/assign_task/remove_assign_task_usecase.dart'
    as _i967;
import 'package:theadvance/src/domain/usecases/assign_task/save_assign_task_usecase.dart'
    as _i965;
import 'package:theadvance/src/domain/usecases/assign_task/update_assign_task_usecase.dart'
    as _i962;
import 'package:theadvance/src/domain/usecases/branch_chat_list/get_branch_chat_list_usecase.dart'
    as _i687;
import 'package:theadvance/src/domain/usecases/branch_chat_list/get_saved_branch_chat_list_usecase.dart'
    as _i688;
import 'package:theadvance/src/domain/usecases/branch_chat_list/remove_branch_chat_list_usecase.dart'
    as _i689;
import 'package:theadvance/src/domain/usecases/branch_chat_list/save_branch_chat_list_usecase.dart'
    as _i690;
import 'package:theadvance/src/domain/usecases/branch_selection/bed_change_branch_selection_usecase.dart'
    as _i637;
import 'package:theadvance/src/domain/usecases/branch_selection/bed_select_branch_selection_usecase.dart'
    as _i635;
import 'package:theadvance/src/domain/usecases/branch_selection/employee_get_branch_selection_usecase.dart'
    as _i632;
import 'package:theadvance/src/domain/usecases/branch_selection/estimate_time_get_branch_selection_usecase.dart'
    as _i638;
import 'package:theadvance/src/domain/usecases/branch_selection/get_bed_branch_selection_usecase.dart'
    as _i640;
import 'package:theadvance/src/domain/usecases/branch_selection/get_branch_selection_usecase.dart'
    as _i641;
import 'package:theadvance/src/domain/usecases/branch_selection/get_floor_branch_selection_usecase.dart'
    as _i636;
import 'package:theadvance/src/domain/usecases/branch_selection/get_room_branch_selection_usecase.dart'
    as _i642;
import 'package:theadvance/src/domain/usecases/branch_selection/get_saved_branch_selection_usecase.dart'
    as _i639;
import 'package:theadvance/src/domain/usecases/branch_selection/remove_branch_selection_usecase.dart'
    as _i633;
import 'package:theadvance/src/domain/usecases/branch_selection/save_branch_selection_usecase.dart'
    as _i634;
import 'package:theadvance/src/domain/usecases/chat/conversation_details_update_chat_usecase.dart'
    as _i235;
import 'package:theadvance/src/domain/usecases/chat/get_chat_usecase.dart'
    as _i232;
import 'package:theadvance/src/domain/usecases/chat/get_conversation_by_id_usecase.dart'
    as _i228;
import 'package:theadvance/src/domain/usecases/chat/get_conversation_chat_usecase.dart'
    as _i219;
import 'package:theadvance/src/domain/usecases/chat/get_pin_list_chat_usecase.dart'
    as _i220;
import 'package:theadvance/src/domain/usecases/chat/get_saved_chat_usecase.dart'
    as _i214;
import 'package:theadvance/src/domain/usecases/chat/get_user_seen_chat_usecase.dart'
    as _i216;
import 'package:theadvance/src/domain/usecases/chat/get_user_sticker_chat_usecase.dart'
    as _i223;
import 'package:theadvance/src/domain/usecases/chat/message_edit_chat_usecase.dart'
    as _i215;
import 'package:theadvance/src/domain/usecases/chat/message_remove_chat_usecase.dart'
    as _i233;
import 'package:theadvance/src/domain/usecases/chat/pin_message_chat_usecase.dart'
    as _i230;
import 'package:theadvance/src/domain/usecases/chat/react_chat_usecase.dart'
    as _i227;
import 'package:theadvance/src/domain/usecases/chat/remove_chat_usecase.dart'
    as _i234;
import 'package:theadvance/src/domain/usecases/chat/reply_bot_message_chat_usecase.dart'
    as _i226;
import 'package:theadvance/src/domain/usecases/chat/save_chat_usecase.dart'
    as _i221;
import 'package:theadvance/src/domain/usecases/chat/search_chat_usecase.dart'
    as _i218;
import 'package:theadvance/src/domain/usecases/chat/send_chat_usecase.dart'
    as _i229;
import 'package:theadvance/src/domain/usecases/chat/transcribe_chat_usecase.dart'
    as _i217;
import 'package:theadvance/src/domain/usecases/chat/unpin_message_chat_usecase.dart'
    as _i225;
import 'package:theadvance/src/domain/usecases/chat/update_poll_chat_usecase.dart'
    as _i231;
import 'package:theadvance/src/domain/usecases/chat/upload_file_chat_usecase.dart'
    as _i224;
import 'package:theadvance/src/domain/usecases/chat/vote_poll_chat_usecase.dart'
    as _i222;
import 'package:theadvance/src/domain/usecases/chat_list/get_chat_list_usecase.dart'
    as _i165;
import 'package:theadvance/src/domain/usecases/chat_list/get_conversation_by_invite_id_chat_list_usecase.dart'
    as _i162;
import 'package:theadvance/src/domain/usecases/chat_list/get_recent_contacts_chat_list_usecase.dart'
    as _i163;
import 'package:theadvance/src/domain/usecases/chat_list/get_saved_chat_list_usecase.dart'
    as _i157;
import 'package:theadvance/src/domain/usecases/chat_list/get_total_unread_chat_list_usecase.dart'
    as _i156;
import 'package:theadvance/src/domain/usecases/chat_list/get_unread_conversations_chat_list_usecase.dart'
    as _i164;
import 'package:theadvance/src/domain/usecases/chat_list/join_group_chat_list_usecase.dart'
    as _i155;
import 'package:theadvance/src/domain/usecases/chat_list/mark_as_read_chat_list_usecase.dart'
    as _i160;
import 'package:theadvance/src/domain/usecases/chat_list/pin_conversation_chat_list_usecase.dart'
    as _i158;
import 'package:theadvance/src/domain/usecases/chat_list/remove_chat_list_usecase.dart'
    as _i167;
import 'package:theadvance/src/domain/usecases/chat_list/save_chat_list_usecase.dart'
    as _i168;
import 'package:theadvance/src/domain/usecases/chat_list/search_chat_list_usecase.dart'
    as _i159;
import 'package:theadvance/src/domain/usecases/chat_list/search_message_chat_list_usecase.dart'
    as _i161;
import 'package:theadvance/src/domain/usecases/chat_list/sort_folder_chat_list_usecase.dart'
    as _i154;
import 'package:theadvance/src/domain/usecases/chat_list/update_pin_conversation_chat_list_usecase.dart'
    as _i166;
import 'package:theadvance/src/domain/usecases/chat_select_branch/get_chat_select_branch_usecase.dart'
    as _i481;
import 'package:theadvance/src/domain/usecases/chat_select_branch/get_saved_chat_select_branch_usecase.dart'
    as _i478;
import 'package:theadvance/src/domain/usecases/chat_select_branch/remove_chat_select_branch_usecase.dart'
    as _i479;
import 'package:theadvance/src/domain/usecases/chat_select_branch/save_chat_select_branch_usecase.dart'
    as _i480;
import 'package:theadvance/src/domain/usecases/checkin/cache_checkin_hour_get_usecase.dart'
    as _i798;
import 'package:theadvance/src/domain/usecases/checkin/cache_checkin_hour_save_usecase.dart'
    as _i796;
import 'package:theadvance/src/domain/usecases/checkin/cache_checkout_hour_get_usecase.dart'
    as _i797;
import 'package:theadvance/src/domain/usecases/checkin/cache_checkout_hour_save_usecase.dart'
    as _i795;
import 'package:theadvance/src/domain/usecases/checkin/get_branches_usecase.dart'
    as _i858;
import 'package:theadvance/src/domain/usecases/checkin/get_checkin_types_usecase.dart'
    as _i859;
import 'package:theadvance/src/domain/usecases/checkin/get_choices_usecase.dart'
    as _i856;
import 'package:theadvance/src/domain/usecases/checkin/get_monthly_history_checkin_usecase.dart'
    as _i860;
import 'package:theadvance/src/domain/usecases/checkin/request_update_history_checkin_usecase.dart'
    as _i857;
import 'package:theadvance/src/domain/usecases/checkin_photo/get_checkin_photo_usecase.dart'
    as _i680;
import 'package:theadvance/src/domain/usecases/checkin_photo/get_saved_checkin_photo_usecase.dart'
    as _i681;
import 'package:theadvance/src/domain/usecases/checkin_photo/remove_checkin_photo_usecase.dart'
    as _i683;
import 'package:theadvance/src/domain/usecases/checkin_photo/save_checkin_photo_usecase.dart'
    as _i682;
import 'package:theadvance/src/domain/usecases/comment/comment_upload_file_usecase.dart'
    as _i850;
import 'package:theadvance/src/domain/usecases/comment/delete_comment_usecase.dart'
    as _i849;
import 'package:theadvance/src/domain/usecases/comment/get_comment_list_usecase.dart'
    as _i847;
import 'package:theadvance/src/domain/usecases/comment/post_comment_usecase.dart'
    as _i848;
import 'package:theadvance/src/domain/usecases/comment/update_comment_usecase.dart'
    as _i679;
import 'package:theadvance/src/domain/usecases/consultation_customer/complete_consultation_customer_usecase.dart'
    as _i320;
import 'package:theadvance/src/domain/usecases/consultation_customer/create_treatment_detail_usecase.dart'
    as _i304;
import 'package:theadvance/src/domain/usecases/consultation_customer/create_treatment_om_detail_usecase.dart'
    as _i296;
import 'package:theadvance/src/domain/usecases/consultation_customer/delete_result_of_fit_usecase.dart'
    as _i297;
import 'package:theadvance/src/domain/usecases/consultation_customer/edit_service_consultation_customer_usecase.dart'
    as _i321;
import 'package:theadvance/src/domain/usecases/consultation_customer/get_action_consultation_customer_usecase.dart'
    as _i317;
import 'package:theadvance/src/domain/usecases/consultation_customer/get_consultation_customer_usecase.dart'
    as _i313;
import 'package:theadvance/src/domain/usecases/consultation_customer/get_consultation_ndtv_usecase.dart'
    as _i299;
import 'package:theadvance/src/domain/usecases/consultation_customer/get_consultation_ttbd_usecase.dart'
    as _i319;
import 'package:theadvance/src/domain/usecases/consultation_customer/get_fit_customer_info_usecase.dart'
    as _i305;
import 'package:theadvance/src/domain/usecases/consultation_customer/get_result_list_of_fit_usecase.dart'
    as _i300;
import 'package:theadvance/src/domain/usecases/consultation_customer/get_result_of_fit_usecase.dart'
    as _i310;
import 'package:theadvance/src/domain/usecases/consultation_customer/get_saved_consultation_customer_usecase.dart'
    as _i311;
import 'package:theadvance/src/domain/usecases/consultation_customer/get_service_consultation_customer_usecase.dart'
    as _i308;
import 'package:theadvance/src/domain/usecases/consultation_customer/get_service_inside_ticket_usecase.dart'
    as _i298;
import 'package:theadvance/src/domain/usecases/consultation_customer/get_service_om_consultation_customer_usecase.dart'
    as _i309;
import 'package:theadvance/src/domain/usecases/consultation_customer/get_service_usage_consultation_customer_usecase.dart'
    as _i307;
import 'package:theadvance/src/domain/usecases/consultation_customer/get_skin_customer_info_usecase.dart'
    as _i301;
import 'package:theadvance/src/domain/usecases/consultation_customer/get_treatment_detail_usecase.dart'
    as _i324;
import 'package:theadvance/src/domain/usecases/consultation_customer/get_treatment_note_usecase.dart'
    as _i306;
import 'package:theadvance/src/domain/usecases/consultation_customer/product_load_consultation_customer_usecase.dart'
    as _i303;
import 'package:theadvance/src/domain/usecases/consultation_customer/remove_consultation_customer_usecase.dart'
    as _i302;
import 'package:theadvance/src/domain/usecases/consultation_customer/remove_service_consultation_customer_usecase.dart'
    as _i323;
import 'package:theadvance/src/domain/usecases/consultation_customer/save_consultation_customer_usecase.dart'
    as _i315;
import 'package:theadvance/src/domain/usecases/consultation_customer/update_fit_customer_info_usecase.dart'
    as _i314;
import 'package:theadvance/src/domain/usecases/consultation_customer/update_result_of_fit_usecase.dart'
    as _i318;
import 'package:theadvance/src/domain/usecases/consultation_customer/update_skin_customer_info_usecase.dart'
    as _i312;
import 'package:theadvance/src/domain/usecases/consultation_customer/update_treatment_detail_usecase.dart'
    as _i322;
import 'package:theadvance/src/domain/usecases/consultation_customer/update_treatment_note_usecase.dart'
    as _i316;
import 'package:theadvance/src/domain/usecases/consultation_manager/assign_update_usecase.dart'
    as _i925;
import 'package:theadvance/src/domain/usecases/consultation_manager/bed_assign_consultation_manager_usecase.dart'
    as _i926;
import 'package:theadvance/src/domain/usecases/consultation_manager/bed_fetch_consultation_manager_usecase.dart'
    as _i928;
import 'package:theadvance/src/domain/usecases/consultation_manager/delete_service_assign_manager_usecase.dart'
    as _i924;
import 'package:theadvance/src/domain/usecases/consultation_manager/delete_service_customer_usecase.dart'
    as _i929;
import 'package:theadvance/src/domain/usecases/consultation_manager/get_consultation_manager_usecase.dart'
    as _i921;
import 'package:theadvance/src/domain/usecases/consultation_manager/get_customer_consultation_manager_usecase.dart'
    as _i930;
import 'package:theadvance/src/domain/usecases/consultation_manager/get_saved_consultation_manager_usecase.dart'
    as _i927;
import 'package:theadvance/src/domain/usecases/consultation_manager/list_fetch_by_staff_consultation_manager_usecase.dart'
    as _i919;
import 'package:theadvance/src/domain/usecases/consultation_manager/remove_consultation_manager_usecase.dart'
    as _i922;
import 'package:theadvance/src/domain/usecases/consultation_manager/room_fetch_consultation_manager_usecase.dart'
    as _i920;
import 'package:theadvance/src/domain/usecases/consultation_manager/save_consultation_manager_usecase.dart'
    as _i923;
import 'package:theadvance/src/domain/usecases/create_chat_folder/conversation_load_create_chat_folder_usecase.dart'
    as _i333;
import 'package:theadvance/src/domain/usecases/create_chat_folder/get_create_chat_folder_usecase.dart'
    as _i331;
import 'package:theadvance/src/domain/usecases/create_chat_folder/get_saved_create_chat_folder_usecase.dart'
    as _i329;
import 'package:theadvance/src/domain/usecases/create_chat_folder/load_create_chat_folder_usecase.dart'
    as _i330;
import 'package:theadvance/src/domain/usecases/create_chat_folder/remove_create_chat_folder_usecase.dart'
    as _i328;
import 'package:theadvance/src/domain/usecases/create_chat_folder/remove_folder_create_chat_folder_usecase.dart'
    as _i335;
import 'package:theadvance/src/domain/usecases/create_chat_folder/save_create_chat_folder_usecase.dart'
    as _i332;
import 'package:theadvance/src/domain/usecases/create_chat_folder/update_create_chat_folder_usecase.dart'
    as _i334;
import 'package:theadvance/src/domain/usecases/create_chat_group/get_create_chat_group_usecase.dart'
    as _i184;
import 'package:theadvance/src/domain/usecases/create_chat_group/get_saved_create_chat_group_usecase.dart'
    as _i181;
import 'package:theadvance/src/domain/usecases/create_chat_group/remove_create_chat_group_usecase.dart'
    as _i185;
import 'package:theadvance/src/domain/usecases/create_chat_group/save_create_chat_group_usecase.dart'
    as _i182;
import 'package:theadvance/src/domain/usecases/create_chat_group/user_load_create_chat_group_usecase.dart'
    as _i183;
import 'package:theadvance/src/domain/usecases/create_customer/customer_search_create_customer_usecase.dart'
    as _i585;
import 'package:theadvance/src/domain/usecases/create_customer/get_create_customer_usecase.dart'
    as _i581;
import 'package:theadvance/src/domain/usecases/create_customer/get_district_create_customer_usecase.dart'
    as _i579;
import 'package:theadvance/src/domain/usecases/create_customer/get_job_create_customer_usecase.dart'
    as _i576;
import 'package:theadvance/src/domain/usecases/create_customer/get_province_create_customer_usecase.dart'
    as _i575;
import 'package:theadvance/src/domain/usecases/create_customer/get_saved_create_customer_usecase.dart'
    as _i582;
import 'package:theadvance/src/domain/usecases/create_customer/get_ward_create_customer_usecase.dart'
    as _i580;
import 'package:theadvance/src/domain/usecases/create_customer/remove_create_customer_usecase.dart'
    as _i577;
import 'package:theadvance/src/domain/usecases/create_customer/save_create_customer_usecase.dart'
    as _i578;
import 'package:theadvance/src/domain/usecases/create_customer/survey_load_create_customer_usecase.dart'
    as _i584;
import 'package:theadvance/src/domain/usecases/create_customer/update_create_customer_usecase.dart'
    as _i583;
import 'package:theadvance/src/domain/usecases/customer/checkin_customer_usecase.dart'
    as _i831;
import 'package:theadvance/src/domain/usecases/customer/get_customer_info_by_qr_usecase.dart'
    as _i508;
import 'package:theadvance/src/domain/usecases/customer/get_customer_info_usecase.dart'
    as _i509;
import 'package:theadvance/src/domain/usecases/customer/get_customer_room_code_usecase.dart'
    as _i510;
import 'package:theadvance/src/domain/usecases/customer/get_room_list_customer_usecase.dart'
    as _i832;
import 'package:theadvance/src/domain/usecases/customer/print_customer_usecase.dart'
    as _i834;
import 'package:theadvance/src/domain/usecases/customer/save_customer_room_code_usecase.dart'
    as _i833;
import 'package:theadvance/src/domain/usecases/customer_booking_info/booked_services_fetch_customer_booking_info_usecase.dart'
    as _i662;
import 'package:theadvance/src/domain/usecases/customer_booking_info/get_customer_booking_info_usecase.dart'
    as _i661;
import 'package:theadvance/src/domain/usecases/customer_booking_info/get_saved_customer_booking_info_usecase.dart'
    as _i663;
import 'package:theadvance/src/domain/usecases/customer_booking_info/remove_customer_booking_info_usecase.dart'
    as _i658;
import 'package:theadvance/src/domain/usecases/customer_booking_info/save_customer_booking_info_usecase.dart'
    as _i660;
import 'package:theadvance/src/domain/usecases/customer_booking_info/service_details_load_customer_booking_info_usecase.dart'
    as _i659;
import 'package:theadvance/src/domain/usecases/customer_booking_info/suggest_services_fetch_customer_booking_info_usecase.dart'
    as _i657;
import 'package:theadvance/src/domain/usecases/customer_booking_info/used_service_fetch_customer_booking_info_usecase.dart'
    as _i664;
import 'package:theadvance/src/domain/usecases/customer_info_details/checkout_customer_info_details_usecase.dart'
    as _i697;
import 'package:theadvance/src/domain/usecases/customer_info_details/get_customer_info_details_usecase.dart'
    as _i696;
import 'package:theadvance/src/domain/usecases/customer_info_details/get_saved_customer_info_details_usecase.dart'
    as _i698;
import 'package:theadvance/src/domain/usecases/customer_info_details/remove_customer_info_details_usecase.dart'
    as _i699;
import 'package:theadvance/src/domain/usecases/customer_info_details/save_customer_info_details_usecase.dart'
    as _i695;
import 'package:theadvance/src/domain/usecases/customer_list/get_customer_list_usecase.dart'
    as _i755;
import 'package:theadvance/src/domain/usecases/customer_list/get_customer_relationship_list_usecase.dart'
    as _i758;
import 'package:theadvance/src/domain/usecases/customer_list/get_saved_customer_list_usecase.dart'
    as _i756;
import 'package:theadvance/src/domain/usecases/customer_list/remove_customer_list_usecase.dart'
    as _i757;
import 'package:theadvance/src/domain/usecases/customer_list/save_customer_list_usecase.dart'
    as _i754;
import 'package:theadvance/src/domain/usecases/customer_profile/create_consultation_customer_profile_usecase.dart'
    as _i707;
import 'package:theadvance/src/domain/usecases/customer_profile/get_consultation_history_customer_profile_usecase.dart'
    as _i710;
import 'package:theadvance/src/domain/usecases/customer_profile/get_customer_profile_usecase.dart'
    as _i711;
import 'package:theadvance/src/domain/usecases/customer_profile/get_saved_customer_profile_usecase.dart'
    as _i712;
import 'package:theadvance/src/domain/usecases/customer_profile/remove_customer_profile_usecase.dart'
    as _i709;
import 'package:theadvance/src/domain/usecases/customer_profile/save_customer_profile_usecase.dart'
    as _i708;
import 'package:theadvance/src/domain/usecases/customer_profile/update_consultation_customer_profile_usecase.dart'
    as _i713;
import 'package:theadvance/src/domain/usecases/customer_record/get_customer_record_usecase.dart'
    as _i453;
import 'package:theadvance/src/domain/usecases/customer_record/get_saved_customer_record_usecase.dart'
    as _i452;
import 'package:theadvance/src/domain/usecases/customer_record/remove_customer_record_usecase.dart'
    as _i451;
import 'package:theadvance/src/domain/usecases/customer_record/save_customer_record_usecase.dart'
    as _i454;
import 'package:theadvance/src/domain/usecases/customer_schedule/get_customer_schedule_usecase.dart'
    as _i729;
import 'package:theadvance/src/domain/usecases/customer_schedule/get_saved_customer_schedule_usecase.dart'
    as _i727;
import 'package:theadvance/src/domain/usecases/customer_schedule/remove_customer_schedule_usecase.dart'
    as _i728;
import 'package:theadvance/src/domain/usecases/customer_schedule/save_customer_schedule_usecase.dart'
    as _i726;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/advice_fetch_detail_crm_customer_usecase.dart'
    as _i535;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/advice_type_fetch_detail_crm_customer_usecase.dart'
    as _i547;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/advice_update_detail_crm_customer_usecase.dart'
    as _i546;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/book_detail_crm_customer_usecase.dart'
    as _i544;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/booking_detail_load_detail_crm_customer_usecase.dart'
    as _i548;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/booking_load_detail_crm_customer_usecase.dart'
    as _i551;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/booking_log_fetch_detail_crm_customer_usecase.dart'
    as _i543;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/branch_load_detail_crm_customer_usecase.dart'
    as _i541;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/call_log_fetch_detail_crm_customer_usecase.dart'
    as _i540;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/get_detail_crm_customer_usecase.dart'
    as _i536;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/get_saved_detail_crm_customer_usecase.dart'
    as _i542;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/message_log_fetch_detail_crm_customer_usecase.dart'
    as _i549;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/number_booking_load_detail_crm_customer_usecase.dart'
    as _i550;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/promotion_load_detail_crm_customer_usecase.dart'
    as _i533;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/remove_detail_crm_customer_usecase.dart'
    as _i539;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/room_load_detail_crm_customer_usecase.dart'
    as _i532;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/save_detail_crm_customer_usecase.dart'
    as _i537;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/service_fetch_detail_crm_customer_usecase.dart'
    as _i534;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/service_load_detail_crm_customer_usecase.dart'
    as _i538;
import 'package:theadvance/src/domain/usecases/detail_crm_customer/time_load_detail_crm_customer_usecase.dart'
    as _i545;
import 'package:theadvance/src/domain/usecases/detail_staff_evaluation_period/employee_fetch_detail_staff_evaluation_period_usecase.dart'
    as _i483;
import 'package:theadvance/src/domain/usecases/detail_staff_evaluation_period/get_detail_staff_evaluation_period_usecase.dart'
    as _i486;
import 'package:theadvance/src/domain/usecases/detail_staff_evaluation_period/get_saved_detail_staff_evaluation_period_usecase.dart'
    as _i485;
import 'package:theadvance/src/domain/usecases/detail_staff_evaluation_period/remove_detail_staff_evaluation_period_usecase.dart'
    as _i484;
import 'package:theadvance/src/domain/usecases/detail_staff_evaluation_period/save_detail_staff_evaluation_period_usecase.dart'
    as _i482;
import 'package:theadvance/src/domain/usecases/dev/get_dev_usecase.dart'
    as _i731;
import 'package:theadvance/src/domain/usecases/dev/get_saved_dev_usecase.dart'
    as _i730;
import 'package:theadvance/src/domain/usecases/dev/mini_app_dev_usecase.dart'
    as _i733;
import 'package:theadvance/src/domain/usecases/dev/remove_dev_usecase.dart'
    as _i734;
import 'package:theadvance/src/domain/usecases/dev/save_dev_usecase.dart'
    as _i732;
import 'package:theadvance/src/domain/usecases/eform/approving_eform_usecase.dart'
    as _i604;
import 'package:theadvance/src/domain/usecases/eform/approving_otp_eform_usecase.dart'
    as _i601;
import 'package:theadvance/src/domain/usecases/eform/approving_signal_eform_usecase.dart'
    as _i600;
import 'package:theadvance/src/domain/usecases/eform/create_eform_usecase.dart'
    as _i599;
import 'package:theadvance/src/domain/usecases/eform/get_detail_eform_usecase.dart'
    as _i605;
import 'package:theadvance/src/domain/usecases/eform/get_eform_request_type.dart'
    as _i602;
import 'package:theadvance/src/domain/usecases/eform/get_eform_usecase.dart'
    as _i606;
import 'package:theadvance/src/domain/usecases/eform/reject_eform_usecase.dart'
    as _i603;
import 'package:theadvance/src/domain/usecases/feedback/get_feedback_usecase.dart'
    as _i743;
import 'package:theadvance/src/domain/usecases/feedback/get_saved_feedback_usecase.dart'
    as _i744;
import 'package:theadvance/src/domain/usecases/feedback/remove_feedback_usecase.dart'
    as _i745;
import 'package:theadvance/src/domain/usecases/feedback/save_feedback_usecase.dart'
    as _i742;
import 'package:theadvance/src/domain/usecases/fonts/cache_text_scale_get_usecase.dart'
    as _i246;
import 'package:theadvance/src/domain/usecases/fonts/cache_text_scale_remove_usecase.dart'
    as _i236;
import 'package:theadvance/src/domain/usecases/fonts/cache_text_scale_save_usecase.dart'
    as _i237;
import 'package:theadvance/src/domain/usecases/food/order_food_create_report_usecase.dart'
    as _i530;
import 'package:theadvance/src/domain/usecases/food/order_food_created_usecase.dart'
    as _i529;
import 'package:theadvance/src/domain/usecases/food/order_food_delete_usecase.dart'
    as _i527;
import 'package:theadvance/src/domain/usecases/food/order_food_get_usecase.dart'
    as _i528;
import 'package:theadvance/src/domain/usecases/food/order_food_upload_usecase.dart'
    as _i325;
import 'package:theadvance/src/domain/usecases/food/set_default_address_food_usecase.dart'
    as _i531;
import 'package:theadvance/src/domain/usecases/general/cache_quick_action_get_usecase.dart'
    as _i787;
import 'package:theadvance/src/domain/usecases/general/cache_quick_action_remove_usecase.dart'
    as _i786;
import 'package:theadvance/src/domain/usecases/general/cache_quick_action_save_usecase.dart'
    as _i788;
import 'package:theadvance/src/domain/usecases/group_chat_detail/avatar_upload_group_chat_detail_usecase.dart'
    as _i278;
import 'package:theadvance/src/domain/usecases/group_chat_detail/change_owner_group_chat_detail_usecase.dart'
    as _i285;
import 'package:theadvance/src/domain/usecases/group_chat_detail/delete_group_usecase.dart'
    as _i281;
import 'package:theadvance/src/domain/usecases/group_chat_detail/file_load_group_chat_detail_usecase.dart'
    as _i289;
import 'package:theadvance/src/domain/usecases/group_chat_detail/get_group_chat_detail_usecase.dart'
    as _i284;
import 'package:theadvance/src/domain/usecases/group_chat_detail/get_rule_by_role_group_chat_detail_usecase.dart'
    as _i290;
import 'package:theadvance/src/domain/usecases/group_chat_detail/get_saved_group_chat_detail_usecase.dart'
    as _i292;
import 'package:theadvance/src/domain/usecases/group_chat_detail/get_user_exception_group_chat_detail_usecase.dart'
    as _i277;
import 'package:theadvance/src/domain/usecases/group_chat_detail/get_user_rules_group_chat_detail_usecase.dart'
    as _i288;
import 'package:theadvance/src/domain/usecases/group_chat_detail/link_load_group_chat_detail_usecase.dart'
    as _i286;
import 'package:theadvance/src/domain/usecases/group_chat_detail/media_load_group_chat_detail_usecase.dart'
    as _i282;
import 'package:theadvance/src/domain/usecases/group_chat_detail/member_info_load_group_chat_detail_usecase.dart'
    as _i280;
import 'package:theadvance/src/domain/usecases/group_chat_detail/remove_group_chat_detail_usecase.dart'
    as _i279;
import 'package:theadvance/src/domain/usecases/group_chat_detail/save_group_chat_detail_usecase.dart'
    as _i293;
import 'package:theadvance/src/domain/usecases/group_chat_detail/update_admin_rule_group_chat_detail_usecase.dart'
    as _i287;
import 'package:theadvance/src/domain/usecases/group_chat_detail/update_group_chat_detail_usecase.dart'
    as _i291;
import 'package:theadvance/src/domain/usecases/group_chat_detail/update_member_rule_group_chat_detail_usecase.dart'
    as _i283;
import 'package:theadvance/src/domain/usecases/helper/delete_latitude_usecase.dart'
    as _i241;
import 'package:theadvance/src/domain/usecases/helper/delete_longitude_usecase.dart'
    as _i240;
import 'package:theadvance/src/domain/usecases/helper/get_access_token_usecase.dart'
    as _i250;
import 'package:theadvance/src/domain/usecases/helper/get_app_version_usecase.dart'
    as _i248;
import 'package:theadvance/src/domain/usecases/helper/get_device_info_usecase.dart'
    as _i253;
import 'package:theadvance/src/domain/usecases/helper/get_firebase_token_usecase.dart'
    as _i251;
import 'package:theadvance/src/domain/usecases/helper/get_key_appsflyer_usecase.dart'
    as _i242;
import 'package:theadvance/src/domain/usecases/helper/get_latitude_usecase.dart'
    as _i249;
import 'package:theadvance/src/domain/usecases/helper/get_longitude_usecase.dart'
    as _i252;
import 'package:theadvance/src/domain/usecases/helper/get_phone_usecase.dart'
    as _i247;
import 'package:theadvance/src/domain/usecases/helper/save_app_version_usecase.dart'
    as _i243;
import 'package:theadvance/src/domain/usecases/helper/save_device_info_usecase.dart'
    as _i238;
import 'package:theadvance/src/domain/usecases/helper/save_key_appflyer_usecase.dart'
    as _i245;
import 'package:theadvance/src/domain/usecases/helper/save_latitude_usecase.dart'
    as _i239;
import 'package:theadvance/src/domain/usecases/helper/save_longitude_usecase.dart'
    as _i244;
import 'package:theadvance/src/domain/usecases/history_checkin/fetch_history_checkin_usecase.dart'
    as _i487;
import 'package:theadvance/src/domain/usecases/home/<USER>'
    as _i829;
import 'package:theadvance/src/domain/usecases/home/<USER>'
    as _i556;
import 'package:theadvance/src/domain/usecases/home/<USER>'
    as _i557;
import 'package:theadvance/src/domain/usecases/home/<USER>'
    as _i830;
import 'package:theadvance/src/domain/usecases/hr_organization/get_hr_organization_usecase.dart'
    as _i618;
import 'package:theadvance/src/domain/usecases/hr_organization/get_saved_hr_organization_usecase.dart'
    as _i619;
import 'package:theadvance/src/domain/usecases/hr_organization/remove_hr_organization_usecase.dart'
    as _i620;
import 'package:theadvance/src/domain/usecases/hr_organization/save_hr_organization_usecase.dart'
    as _i617;
import 'package:theadvance/src/domain/usecases/important_notes/get_important_notes_usecase.dart'
    as _i500;
import 'package:theadvance/src/domain/usecases/important_notes/get_note_category_important_notes_usecase.dart'
    as _i499;
import 'package:theadvance/src/domain/usecases/important_notes/get_saved_important_notes_usecase.dart'
    as _i497;
import 'package:theadvance/src/domain/usecases/important_notes/remove_important_notes_usecase.dart'
    as _i501;
import 'package:theadvance/src/domain/usecases/important_notes/save_important_notes_usecase.dart'
    as _i498;
import 'package:theadvance/src/domain/usecases/kpi_employee/get_kpi_employee_detail_usecase.dart'
    as _i705;
import 'package:theadvance/src/domain/usecases/kpi_employee/get_kpi_employee_usecase.dart'
    as _i706;
import 'package:theadvance/src/domain/usecases/like_list/get_like_list_usecase.dart'
    as _i393;
import 'package:theadvance/src/domain/usecases/like_list/post_like_comment_usecase.dart'
    as _i392;
import 'package:theadvance/src/domain/usecases/like_list/post_like_story_usecase.dart'
    as _i391;
import 'package:theadvance/src/domain/usecases/list_customer/get_list_customer_usecase.dart'
    as _i871;
import 'package:theadvance/src/domain/usecases/list_customer/get_saved_list_customer_usecase.dart'
    as _i870;
import 'package:theadvance/src/domain/usecases/list_customer/get_status_list_customer_usecase.dart'
    as _i869;
import 'package:theadvance/src/domain/usecases/list_customer/remove_list_customer_usecase.dart'
    as _i873;
import 'package:theadvance/src/domain/usecases/list_customer/save_list_customer_usecase.dart'
    as _i872;
import 'package:theadvance/src/domain/usecases/list_customer/search_list_customer_usecase.dart'
    as _i874;
import 'package:theadvance/src/domain/usecases/location_google/get_address_current_usecase.dart'
    as _i615;
import 'package:theadvance/src/domain/usecases/location_google/get_address_near_usecase.dart'
    as _i613;
import 'package:theadvance/src/domain/usecases/location_google/get_address_search_usecase.dart'
    as _i616;
import 'package:theadvance/src/domain/usecases/location_google/get_location_google_usecase.dart'
    as _i614;
import 'package:theadvance/src/domain/usecases/login/socket_access_token_get_login_usecase.dart'
    as _i592;
import 'package:theadvance/src/domain/usecases/media/upload_avatar_usecase.dart'
    as _i667;
import 'package:theadvance/src/domain/usecases/media/upload_background_usecase.dart'
    as _i668;
import 'package:theadvance/src/domain/usecases/media/upload_checkin_image_usecase.dart'
    as _i666;
import 'package:theadvance/src/domain/usecases/media/upload_feedback_usecase.dart'
    as _i665;
import 'package:theadvance/src/domain/usecases/media/upload_kyc_usecase.dart'
    as _i669;
import 'package:theadvance/src/domain/usecases/medical_department_list/get_medical_department_list_usecase.dart'
    as _i378;
import 'package:theadvance/src/domain/usecases/medical_department_list/get_saved_medical_department_list_usecase.dart'
    as _i377;
import 'package:theadvance/src/domain/usecases/medical_department_list/remove_medical_department_list_usecase.dart'
    as _i380;
import 'package:theadvance/src/domain/usecases/medical_department_list/save_medical_department_list_usecase.dart'
    as _i379;
import 'package:theadvance/src/domain/usecases/medical_log_detail/create_log_medical_detail_usecase.dart'
    as _i891;
import 'package:theadvance/src/domain/usecases/medical_log_detail/doctor_list_get_medical_log_detail_usecase.dart'
    as _i881;
import 'package:theadvance/src/domain/usecases/medical_log_detail/dosage_list_get_medical_log_detail_usecase.dart'
    as _i883;
import 'package:theadvance/src/domain/usecases/medical_log_detail/get_medical_log_detail_usecase.dart'
    as _i893;
import 'package:theadvance/src/domain/usecases/medical_log_detail/get_origin_status_medical_log_detail_usecase.dart'
    as _i889;
import 'package:theadvance/src/domain/usecases/medical_log_detail/get_post_sai_medical_log_detail_usecase.dart'
    as _i894;
import 'package:theadvance/src/domain/usecases/medical_log_detail/get_saved_medical_log_detail_usecase.dart'
    as _i890;
import 'package:theadvance/src/domain/usecases/medical_log_detail/get_skin_machine_medical_log_detail_usecase.dart'
    as _i892;
import 'package:theadvance/src/domain/usecases/medical_log_detail/get_tattoo_color_medical_log_detail_usecase.dart'
    as _i882;
import 'package:theadvance/src/domain/usecases/medical_log_detail/get_tattoo_time_medical_log_detail_usecase.dart'
    as _i887;
import 'package:theadvance/src/domain/usecases/medical_log_detail/ha_point_list_get_medical_log_detail_usecase.dart'
    as _i880;
import 'package:theadvance/src/domain/usecases/medical_log_detail/khacnho_list_get_medical_log_detail_usecase.dart'
    as _i888;
import 'package:theadvance/src/domain/usecases/medical_log_detail/medicine_list_get_medical_log_detail_usecase.dart'
    as _i884;
import 'package:theadvance/src/domain/usecases/medical_log_detail/remove_medical_log_detail_usecase.dart'
    as _i886;
import 'package:theadvance/src/domain/usecases/medical_log_detail/save_medical_log_detail_usecase.dart'
    as _i895;
import 'package:theadvance/src/domain/usecases/medical_log_detail/update_log_medical_detail_usecase.dart'
    as _i885;
import 'package:theadvance/src/domain/usecases/medical_product_create/get_saved_medical_product_create_usecase.dart'
    as _i514;
import 'package:theadvance/src/domain/usecases/medical_product_create/medical_product_create_usecase.dart'
    as _i517;
import 'package:theadvance/src/domain/usecases/medical_product_create/products_medical_product_create_usecase.dart'
    as _i513;
import 'package:theadvance/src/domain/usecases/medical_product_create/remove_medical_product_create_usecase.dart'
    as _i515;
import 'package:theadvance/src/domain/usecases/medical_product_create/save_medical_product_create_usecase.dart'
    as _i516;
import 'package:theadvance/src/domain/usecases/medical_service_create/get_saved_medical_service_create_usecase.dart'
    as _i739;
import 'package:theadvance/src/domain/usecases/medical_service_create/medical_service_create_usecase.dart'
    as _i741;
import 'package:theadvance/src/domain/usecases/medical_service_create/methods_medical_service_create_usecase.dart'
    as _i738;
import 'package:theadvance/src/domain/usecases/medical_service_create/remove_medical_service_create_usecase.dart'
    as _i736;
import 'package:theadvance/src/domain/usecases/medical_service_create/save_medical_service_create_usecase.dart'
    as _i740;
import 'package:theadvance/src/domain/usecases/medical_service_create/services_medical_service_create_usecase.dart'
    as _i737;
import 'package:theadvance/src/domain/usecases/medical_service_list/get_medical_service_list_usecase.dart'
    as _i476;
import 'package:theadvance/src/domain/usecases/medical_service_list/get_saved_medical_service_list_usecase.dart'
    as _i473;
import 'package:theadvance/src/domain/usecases/medical_service_list/remove_medical_service_list_usecase.dart'
    as _i475;
import 'package:theadvance/src/domain/usecases/medical_service_list/save_medical_service_list_usecase.dart'
    as _i474;
import 'package:theadvance/src/domain/usecases/medical_service_log_list/get_medical_service_log_list_usecase.dart'
    as _i825;
import 'package:theadvance/src/domain/usecases/medical_service_log_list/get_saved_medical_service_log_list_usecase.dart'
    as _i826;
import 'package:theadvance/src/domain/usecases/medical_service_log_list/remove_medical_service_log_list_usecase.dart'
    as _i824;
import 'package:theadvance/src/domain/usecases/medical_service_log_list/save_medical_service_log_list_usecase.dart'
    as _i823;
import 'package:theadvance/src/domain/usecases/medical_template_list/get_medical_template_list_usecase.dart'
    as _i877;
import 'package:theadvance/src/domain/usecases/medical_template_list/get_saved_medical_template_list_usecase.dart'
    as _i878;
import 'package:theadvance/src/domain/usecases/medical_template_list/medical_template_detail_get_medical_template_list_usecase.dart'
    as _i876;
import 'package:theadvance/src/domain/usecases/medical_template_list/remove_medical_template_list_usecase.dart'
    as _i875;
import 'package:theadvance/src/domain/usecases/medical_template_list/save_medical_template_list_usecase.dart'
    as _i879;
import 'package:theadvance/src/domain/usecases/medicine_detail/create_medicine_detail_usecase.dart'
    as _i721;
import 'package:theadvance/src/domain/usecases/medicine_detail/get_medicine_detail_usecase.dart'
    as _i724;
import 'package:theadvance/src/domain/usecases/medicine_detail/get_saved_medicine_detail_usecase.dart'
    as _i723;
import 'package:theadvance/src/domain/usecases/medicine_detail/get_unit_medicine_detail_usecase.dart'
    as _i720;
import 'package:theadvance/src/domain/usecases/medicine_detail/remove_medicine_detail_usecase.dart'
    as _i719;
import 'package:theadvance/src/domain/usecases/medicine_detail/save_medicine_detail_usecase.dart'
    as _i725;
import 'package:theadvance/src/domain/usecases/medicine_detail/update_medicine_detail_usecase.dart'
    as _i722;
import 'package:theadvance/src/domain/usecases/news/get_detail_news_usecase.dart'
    as _i595;
import 'package:theadvance/src/domain/usecases/news/get_news_usecase.dart'
    as _i593;
import 'package:theadvance/src/domain/usecases/news/search_news_usecase.dart'
    as _i594;
import 'package:theadvance/src/domain/usecases/note_details/create_note_details_usecase.dart'
    as _i748;
import 'package:theadvance/src/domain/usecases/note_details/get_note_details_usecase.dart'
    as _i752;
import 'package:theadvance/src/domain/usecases/note_details/get_saved_note_details_usecase.dart'
    as _i751;
import 'package:theadvance/src/domain/usecases/note_details/remove_note_details_usecase.dart'
    as _i750;
import 'package:theadvance/src/domain/usecases/note_details/save_note_details_usecase.dart'
    as _i753;
import 'package:theadvance/src/domain/usecases/note_details/update_note_details_usecase.dart'
    as _i749;
import 'package:theadvance/src/domain/usecases/notification/get_detail_notification_usecase.dart'
    as _i648;
import 'package:theadvance/src/domain/usecases/notification/get_navigation_info_usecase.dart'
    as _i649;
import 'package:theadvance/src/domain/usecases/notification/get_notifications_usecase.dart'
    as _i651;
import 'package:theadvance/src/domain/usecases/notification/post_read_notifcation_usecase.dart'
    as _i647;
import 'package:theadvance/src/domain/usecases/notification/read_all_notification_usecase.dart'
    as _i652;
import 'package:theadvance/src/domain/usecases/notification/search_notifications_usecase.dart'
    as _i650;
import 'package:theadvance/src/domain/usecases/notification_list/delete_notification_usecase.dart'
    as _i265;
import 'package:theadvance/src/domain/usecases/notification_list/get_notification_list_usecase.dart'
    as _i263;
import 'package:theadvance/src/domain/usecases/notification_list/get_saved_notification_list_usecase.dart'
    as _i267;
import 'package:theadvance/src/domain/usecases/notification_list/put_read_all_social_usecase.dart'
    as _i264;
import 'package:theadvance/src/domain/usecases/notification_list/remove_notification_list_usecase.dart'
    as _i262;
import 'package:theadvance/src/domain/usecases/notification_list/save_notification_list_usecase.dart'
    as _i266;
import 'package:theadvance/src/domain/usecases/product_confirm/approval_product_detail_confirm_usecase.dart'
    as _i589;
import 'package:theadvance/src/domain/usecases/product_confirm/get_product_confirm_branch_usecase.dart'
    as _i590;
import 'package:theadvance/src/domain/usecases/product_confirm/get_product_confirm_usecase.dart'
    as _i587;
import 'package:theadvance/src/domain/usecases/product_confirm/get_product_detail_confirm_usecase.dart'
    as _i588;
import 'package:theadvance/src/domain/usecases/product_confirm/reject_product_detail_confirm_usecase.dart'
    as _i591;
import 'package:theadvance/src/domain/usecases/province/get_province_usecase.dart'
    as _i643;
import 'package:theadvance/src/domain/usecases/px_list/get_px_list_usecase.dart'
    as _i820;
import 'package:theadvance/src/domain/usecases/px_list/get_saved_px_list_usecase.dart'
    as _i819;
import 'package:theadvance/src/domain/usecases/px_list/remove_px_list_usecase.dart'
    as _i822;
import 'package:theadvance/src/domain/usecases/px_list/save_px_list_usecase.dart'
    as _i821;
import 'package:theadvance/src/domain/usecases/px_recheck/assign_px_recheck_update_usecase.dart'
    as _i865;
import 'package:theadvance/src/domain/usecases/px_recheck/assigns_fetch_px_recheck_usecase.dart'
    as _i899;
import 'package:theadvance/src/domain/usecases/px_recheck/note_finish_px_recheck_usecase.dart'
    as _i897;
import 'package:theadvance/src/domain/usecases/px_recheck/work_status_update_px_recheck_usecase.dart'
    as _i898;
import 'package:theadvance/src/domain/usecases/px_task_list/get_px_task_list_usecase.dart'
    as _i525;
import 'package:theadvance/src/domain/usecases/px_task_list/get_saved_px_task_list_usecase.dart'
    as _i523;
import 'package:theadvance/src/domain/usecases/px_task_list/remove_px_task_list_usecase.dart'
    as _i522;
import 'package:theadvance/src/domain/usecases/px_task_list/save_px_task_list_usecase.dart'
    as _i524;
import 'package:theadvance/src/domain/usecases/px_unasigned/get_px_customer_list_usecase.dart'
    as _i760;
import 'package:theadvance/src/domain/usecases/px_unasigned/get_saved_px_unasigned_usecase.dart'
    as _i762;
import 'package:theadvance/src/domain/usecases/px_unasigned/remove_px_unasigned_usecase.dart'
    as _i759;
import 'package:theadvance/src/domain/usecases/px_unasigned/save_px_unasigned_usecase.dart'
    as _i761;
import 'package:theadvance/src/domain/usecases/px_unasigned_update/assign_px_unasigned_update_usecase.dart'
    as _i934;
import 'package:theadvance/src/domain/usecases/px_unasigned_update/employees_fetch_px_unasigned_update_usecase.dart'
    as _i933;
import 'package:theadvance/src/domain/usecases/px_unasigned_update/employees_in_room_usecase.dart'
    as _i932;
import 'package:theadvance/src/domain/usecases/px_unasigned_update/get_px_unasigned_update_usecase.dart'
    as _i936;
import 'package:theadvance/src/domain/usecases/px_unasigned_update/get_saved_px_unasigned_update_usecase.dart'
    as _i939;
import 'package:theadvance/src/domain/usecases/px_unasigned_update/remove_px_unasigned_update_usecase.dart'
    as _i938;
import 'package:theadvance/src/domain/usecases/px_unasigned_update/save_px_unasigned_update_usecase.dart'
    as _i937;
import 'package:theadvance/src/domain/usecases/px_unasigned_update/works_fetch_px_unasigned_update_usecase.dart'
    as _i935;
import 'package:theadvance/src/domain/usecases/rating_human/get_question_detail_usecase.dart'
    as _i715;
import 'package:theadvance/src/domain/usecases/rating_human/get_rating_human_usecase.dart'
    as _i717;
import 'package:theadvance/src/domain/usecases/rating_human/save_question_usecase.dart'
    as _i716;
import 'package:theadvance/src/domain/usecases/rating_human/submit_question_usecase.dart'
    as _i714;
import 'package:theadvance/src/domain/usecases/request/get_list_support_request_usecase.dart'
    as _i945;
import 'package:theadvance/src/domain/usecases/request/send_support_request_usecase.dart'
    as _i946;
import 'package:theadvance/src/domain/usecases/schedule_details/get_saved_schedule_details_usecase.dart'
    as _i573;
import 'package:theadvance/src/domain/usecases/schedule_details/get_schedule_details_usecase.dart'
    as _i574;
import 'package:theadvance/src/domain/usecases/schedule_details/remove_schedule_details_usecase.dart'
    as _i571;
import 'package:theadvance/src/domain/usecases/schedule_details/save_schedule_details_usecase.dart'
    as _i572;
import 'package:theadvance/src/domain/usecases/select_px_room/get_saved_select_px_room_usecase.dart'
    as _i426;
import 'package:theadvance/src/domain/usecases/select_px_room/get_select_px_room_usecase.dart'
    as _i423;
import 'package:theadvance/src/domain/usecases/select_px_room/remove_select_px_room_usecase.dart'
    as _i424;
import 'package:theadvance/src/domain/usecases/select_px_room/room_change_select_px_room_usecase.dart'
    as _i425;
import 'package:theadvance/src/domain/usecases/select_px_room/save_select_px_room_usecase.dart'
    as _i427;
import 'package:theadvance/src/domain/usecases/service_and_product/get_category_service_and_product_usecase.dart'
    as _i913;
import 'package:theadvance/src/domain/usecases/service_and_product/get_saved_service_and_product_usecase.dart'
    as _i912;
import 'package:theadvance/src/domain/usecases/service_and_product/get_service_and_product_actions_usecase.dart'
    as _i906;
import 'package:theadvance/src/domain/usecases/service_and_product/get_service_and_product_usecase.dart'
    as _i907;
import 'package:theadvance/src/domain/usecases/service_and_product/products_get_service_and_product_usecase.dart'
    as _i910;
import 'package:theadvance/src/domain/usecases/service_and_product/remove_service_and_product_usecase.dart'
    as _i911;
import 'package:theadvance/src/domain/usecases/service_and_product/save_service_and_product_usecase.dart'
    as _i908;
import 'package:theadvance/src/domain/usecases/service_and_product/services_get_service_and_product_usecase.dart'
    as _i909;
import 'package:theadvance/src/domain/usecases/service_detail/doctor_fetch_service_detail_usecase.dart'
    as _i670;
import 'package:theadvance/src/domain/usecases/service_detail/employee_fetch_service_detail_usecase.dart'
    as _i671;
import 'package:theadvance/src/domain/usecases/setting/get_font_option_usecase.dart'
    as _i416;
import 'package:theadvance/src/domain/usecases/setting/get_language_option_usecase.dart'
    as _i411;
import 'package:theadvance/src/domain/usecases/setting/get_theme_option_usecase.dart'
    as _i412;
import 'package:theadvance/src/domain/usecases/setting/is_dark_mode_usecase.dart'
    as _i415;
import 'package:theadvance/src/domain/usecases/setting/is_light_mode_usecase.dart'
    as _i417;
import 'package:theadvance/src/domain/usecases/setting/save_font_option_usecase.dart'
    as _i410;
import 'package:theadvance/src/domain/usecases/setting/save_language_option_usecase.dart'
    as _i413;
import 'package:theadvance/src/domain/usecases/setting/save_theme_option_usecase.dart'
    as _i414;
import 'package:theadvance/src/domain/usecases/staff/get_department_usecase.dart'
    as _i596;
import 'package:theadvance/src/domain/usecases/staff/get_function_room_usecase.dart'
    as _i597;
import 'package:theadvance/src/domain/usecases/staff/get_staff_usecase.dart'
    as _i598;
import 'package:theadvance/src/domain/usecases/staff_evaluation_periods/get_saved_staff_evaluation_periods_usecase.dart'
    as _i506;
import 'package:theadvance/src/domain/usecases/staff_evaluation_periods/get_staff_evaluation_periods_usecase.dart'
    as _i507;
import 'package:theadvance/src/domain/usecases/staff_evaluation_periods/remove_staff_evaluation_periods_usecase.dart'
    as _i504;
import 'package:theadvance/src/domain/usecases/staff_evaluation_periods/save_staff_evaluation_periods_usecase.dart'
    as _i505;
import 'package:theadvance/src/domain/usecases/sticker_social/create_sticker_usecase.dart'
    as _i344;
import 'package:theadvance/src/domain/usecases/sticker_social/get_sticker_only_set_usecase.dart'
    as _i339;
import 'package:theadvance/src/domain/usecases/sticker_social/get_sticker_recent_usecase.dart'
    as _i340;
import 'package:theadvance/src/domain/usecases/sticker_social/get_sticker_set_usecase.dart'
    as _i343;
import 'package:theadvance/src/domain/usecases/sticker_social/get_sticker_usecase.dart'
    as _i346;
import 'package:theadvance/src/domain/usecases/sticker_social/remove_sticker_set_usecase.dart'
    as _i347;
import 'package:theadvance/src/domain/usecases/sticker_social/remove_sticker_usecase.dart'
    as _i341;
import 'package:theadvance/src/domain/usecases/sticker_social/update_sticker_recent_usecase.dart'
    as _i342;
import 'package:theadvance/src/domain/usecases/sticker_social/update_sticker_set_usecase.dart'
    as _i338;
import 'package:theadvance/src/domain/usecases/sticker_social/upload_sticker_usecase.dart'
    as _i345;
import 'package:theadvance/src/domain/usecases/story_detail/get_saved_story_detail_usecase.dart'
    as _i176;
import 'package:theadvance/src/domain/usecases/story_detail/get_story_detail_usecase.dart'
    as _i177;
import 'package:theadvance/src/domain/usecases/story_detail/remove_story_detail_usecase.dart'
    as _i175;
import 'package:theadvance/src/domain/usecases/story_detail/save_story_detail_usecase.dart'
    as _i178;
import 'package:theadvance/src/domain/usecases/story_list/delete_story_usecase.dart'
    as _i469;
import 'package:theadvance/src/domain/usecases/story_list/delete_story_vote_usecase.dart'
    as _i468;
import 'package:theadvance/src/domain/usecases/story_list/get_emoji_list_usecase.dart'
    as _i459;
import 'package:theadvance/src/domain/usecases/story_list/get_story_list_usecase.dart'
    as _i460;
import 'package:theadvance/src/domain/usecases/story_list/get_story_rule_usecase.dart'
    as _i470;
import 'package:theadvance/src/domain/usecases/story_list/get_story_search_usecase.dart'
    as _i461;
import 'package:theadvance/src/domain/usecases/story_list/get_total_notification_usecase.dart'
    as _i464;
import 'package:theadvance/src/domain/usecases/story_list/get_vote_users_usecase.dart'
    as _i467;
import 'package:theadvance/src/domain/usecases/story_list/post_story_usecase.dart'
    as _i462;
import 'package:theadvance/src/domain/usecases/story_list/put_story_vote_usecase.dart'
    as _i463;
import 'package:theadvance/src/domain/usecases/story_list/social_upload_file_usecase.dart'
    as _i466;
import 'package:theadvance/src/domain/usecases/story_list/update_story_usecase.dart'
    as _i465;
import 'package:theadvance/src/domain/usecases/story_person_list/get_story_person_list_usecase.dart'
    as _i408;
import 'package:theadvance/src/domain/usecases/story_person_list/get_story_person_list_user_usecase.dart'
    as _i409;
import 'package:theadvance/src/domain/usecases/tag_image/delete_image_by_combo_usecase.dart'
    as _i839;
import 'package:theadvance/src/domain/usecases/tag_image/delete_tag_by_combo_usecase.dart'
    as _i838;
import 'package:theadvance/src/domain/usecases/tag_image/get_combo_tag_usecase.dart'
    as _i840;
import 'package:theadvance/src/domain/usecases/tag_image/get_image_by_combo_tag_usecase.dart'
    as _i841;
import 'package:theadvance/src/domain/usecases/tag_list/get_tag_list_usecase.dart'
    as _i272;
import 'package:theadvance/src/domain/usecases/taking_care_customer/bot_type_load_taking_care_customer_usecase.dart'
    as _i816;
import 'package:theadvance/src/domain/usecases/taking_care_customer/check_employee_in_room_taking_care_customer_usecase.dart'
    as _i812;
import 'package:theadvance/src/domain/usecases/taking_care_customer/create_support_taking_care_customer_usecase.dart'
    as _i807;
import 'package:theadvance/src/domain/usecases/taking_care_customer/create_treatment_details_taking_care_customer_usecase.dart'
    as _i811;
import 'package:theadvance/src/domain/usecases/taking_care_customer/finish_task_taking_care_customer_usecase.dart'
    as _i805;
import 'package:theadvance/src/domain/usecases/taking_care_customer/get_saved_taking_care_customer_usecase.dart'
    as _i806;
import 'package:theadvance/src/domain/usecases/taking_care_customer/get_section_taking_care_customer_usecase.dart'
    as _i808;
import 'package:theadvance/src/domain/usecases/taking_care_customer/get_taking_care_customer_usecase.dart'
    as _i814;
import 'package:theadvance/src/domain/usecases/taking_care_customer/get_treatment_photo_taking_care_customer_usecase.dart'
    as _i815;
import 'package:theadvance/src/domain/usecases/taking_care_customer/noti_bot_type_load_taking_care_customer_usecase.dart'
    as _i803;
import 'package:theadvance/src/domain/usecases/taking_care_customer/remove_image_taking_care_customer_usecase.dart'
    as _i804;
import 'package:theadvance/src/domain/usecases/taking_care_customer/remove_taking_care_customer_usecase.dart'
    as _i809;
import 'package:theadvance/src/domain/usecases/taking_care_customer/save_taking_care_customer_usecase.dart'
    as _i810;
import 'package:theadvance/src/domain/usecases/taking_care_customer/update_service_detail_taking_care_customer_usecase.dart'
    as _i813;
import 'package:theadvance/src/domain/usecases/taking_care_customer/upload_images_taking_care_customer_usecase.dart'
    as _i818;
import 'package:theadvance/src/domain/usecases/taking_care_customer/upload_record_taking_care_customer_usecase.dart'
    as _i817;
import 'package:theadvance/src/domain/usecases/task/creating_task_usecase.dart'
    as _i400;
import 'package:theadvance/src/domain/usecases/task/get_detail_job_scheduler_usecase.dart'
    as _i405;
import 'package:theadvance/src/domain/usecases/task/get_general_job_scheduler_usecase.dart'
    as _i404;
import 'package:theadvance/src/domain/usecases/task/get_job_scheduler_usecase.dart'
    as _i402;
import 'package:theadvance/src/domain/usecases/task/get_repeat_task_usecase.dart'
    as _i401;
import 'package:theadvance/src/domain/usecases/task/submit_job_scheduler_usecase.dart'
    as _i403;
import 'package:theadvance/src/domain/usecases/ticket_active/complete_ticket_usecase.dart'
    as _i609;
import 'package:theadvance/src/domain/usecases/ticket_active/create_ticket_active_usecase.dart'
    as _i611;
import 'package:theadvance/src/domain/usecases/ticket_active/get_ticket_active_usecase.dart'
    as _i610;
import 'package:theadvance/src/domain/usecases/ticket_detail/confirm_ticket_usecase.dart'
    as _i565;
import 'package:theadvance/src/domain/usecases/ticket_detail/get_saved_ticket_detail_usecase.dart'
    as _i562;
import 'package:theadvance/src/domain/usecases/ticket_detail/get_ticket_detail_usecase.dart'
    as _i560;
import 'package:theadvance/src/domain/usecases/ticket_detail/recept_ticket_usecase.dart'
    as _i563;
import 'package:theadvance/src/domain/usecases/ticket_detail/remove_ticket_detail_usecase.dart'
    as _i566;
import 'package:theadvance/src/domain/usecases/ticket_detail/save_ticket_detail_usecase.dart'
    as _i559;
import 'package:theadvance/src/domain/usecases/ticket_detail/ticket_detail_reason_usecase.dart'
    as _i561;
import 'package:theadvance/src/domain/usecases/ticket_detail/ticket_rework_usecase.dart'
    as _i564;
import 'package:theadvance/src/domain/usecases/ticketv2/create_ticket_usecase.dart'
    as _i436;
import 'package:theadvance/src/domain/usecases/ticketv2/get_my_ticket_usecase.dart'
    as _i439;
import 'package:theadvance/src/domain/usecases/ticketv2/get_ticket_all_group_usecase.dart'
    as _i435;
import 'package:theadvance/src/domain/usecases/ticketv2/get_ticket_all_type_usecase.dart'
    as _i441;
import 'package:theadvance/src/domain/usecases/ticketv2/get_ticket_created_type_usecase.dart'
    as _i434;
import 'package:theadvance/src/domain/usecases/ticketv2/get_ticket_group_type_usecase.dart'
    as _i442;
import 'package:theadvance/src/domain/usecases/ticketv2/get_ticket_ticketv2_usecase.dart'
    as _i437;
import 'package:theadvance/src/domain/usecases/ticketv2/ticket_upload_file_usecase.dart'
    as _i438;
import 'package:theadvance/src/domain/usecases/ticketv2/update_ticket_usecase.dart'
    as _i440;
import 'package:theadvance/src/domain/usecases/tracking/send_event_use_case.dart'
    as _i72;
import 'package:theadvance/src/domain/usecases/tracking/track_banner_use_case.dart'
    as _i70;
import 'package:theadvance/src/domain/usecases/tracking/track_login_use_case.dart'
    as _i66;
import 'package:theadvance/src/domain/usecases/tracking/track_notification_use_case.dart'
    as _i75;
import 'package:theadvance/src/domain/usecases/tracking/track_open_app_use_case.dart'
    as _i71;
import 'package:theadvance/src/domain/usecases/tracking/track_open_landing_page_use_case.dart'
    as _i69;
import 'package:theadvance/src/domain/usecases/tracking/track_popup_use_case.dart'
    as _i73;
import 'package:theadvance/src/domain/usecases/tracking/track_register_use_case.dart'
    as _i74;
import 'package:theadvance/src/domain/usecases/tracking/track_valid_install_use_case.dart'
    as _i68;
import 'package:theadvance/src/domain/usecases/universal_qr_scan/universal_qr_scan_usecase.dart'
    as _i372;
import 'package:theadvance/src/domain/usecases/user/cache_login_stringee_get_usecase.dart'
    as _i792;
import 'package:theadvance/src/domain/usecases/user/cache_login_stringee_remove_usecase.dart'
    as _i789;
import 'package:theadvance/src/domain/usecases/user/cache_login_stringee_save_usecase.dart'
    as _i794;
import 'package:theadvance/src/domain/usecases/user/cache_user_usecase.dart'
    as _i793;
import 'package:theadvance/src/domain/usecases/user/change_password_usecase.dart'
    as _i783;
import 'package:theadvance/src/domain/usecases/user/check_employee_usecase.dart'
    as _i766;
import 'package:theadvance/src/domain/usecases/user/check_permission_user_usecase.dart'
    as _i675;
import 'package:theadvance/src/domain/usecases/user/check_phone_usecase.dart'
    as _i768;
import 'package:theadvance/src/domain/usecases/user/checkin_permission_check_user_usecase.dart'
    as _i673;
import 'package:theadvance/src/domain/usecases/user/checkin_usecase.dart'
    as _i367;
import 'package:theadvance/src/domain/usecases/user/clear_cache_usecase.dart'
    as _i363;
import 'package:theadvance/src/domain/usecases/user/confirm_otp_usecase.dart'
    as _i778;
import 'package:theadvance/src/domain/usecases/user/delete_home_menu_cache_usecase.dart'
    as _i366;
import 'package:theadvance/src/domain/usecases/user/delete_profile_usecase.dart'
    as _i364;
import 'package:theadvance/src/domain/usecases/user/delete_token_usecase.dart'
    as _i362;
import 'package:theadvance/src/domain/usecases/user/get_configuration_usecase.dart'
    as _i777;
import 'package:theadvance/src/domain/usecases/user/get_enable_online_logger_usecase.dart'
    as _i773;
import 'package:theadvance/src/domain/usecases/user/get_otp_usecase.dart'
    as _i767;
import 'package:theadvance/src/domain/usecases/user/get_profile_usecase.dart'
    as _i774;
import 'package:theadvance/src/domain/usecases/user/get_profiles_usecase.dart'
    as _i772;
import 'package:theadvance/src/domain/usecases/user/get_user_info_usecase.dart'
    as _i785;
import 'package:theadvance/src/domain/usecases/user/has_token_usecase.dart'
    as _i791;
import 'package:theadvance/src/domain/usecases/user/has_user_data_usecase.dart'
    as _i360;
import 'package:theadvance/src/domain/usecases/user/is_show_boarding.dart'
    as _i790;
import 'package:theadvance/src/domain/usecases/user/login_social_usecase.dart'
    as _i780;
import 'package:theadvance/src/domain/usecases/user/login_usecase.dart'
    as _i769;
import 'package:theadvance/src/domain/usecases/user/logout_usecase.dart'
    as _i770;
import 'package:theadvance/src/domain/usecases/user/persist_token_usecase.dart'
    as _i361;
import 'package:theadvance/src/domain/usecases/user/reset_password_usecase.dart'
    as _i775;
import 'package:theadvance/src/domain/usecases/user/save_account_usecase.dart'
    as _i365;
import 'package:theadvance/src/domain/usecases/user/save_enable_online_logger_usecase.dart'
    as _i358;
import 'package:theadvance/src/domain/usecases/user/save_navigation_usecase.dart'
    as _i357;
import 'package:theadvance/src/domain/usecases/user/save_profile_usecase.dart'
    as _i356;
import 'package:theadvance/src/domain/usecases/user/send_kyc_photos_setting_usecase.dart'
    as _i672;
import 'package:theadvance/src/domain/usecases/user/show_onboarding_usecase.dart'
    as _i359;
import 'package:theadvance/src/domain/usecases/user/stringee_token_fetch_user_usecase.dart'
    as _i674;
import 'package:theadvance/src/domain/usecases/user/submit_feedback_usecase.dart'
    as _i781;
import 'package:theadvance/src/domain/usecases/user/update_bio_usecase.dart'
    as _i779;
import 'package:theadvance/src/domain/usecases/user/update_profile_usecase.dart'
    as _i765;
import 'package:theadvance/src/domain/usecases/user/upload_audios_usecase.dart'
    as _i782;
import 'package:theadvance/src/domain/usecases/user/upload_files_usecase.dart'
    as _i776;
import 'package:theadvance/src/domain/usecases/user/upload_images_usecase.dart'
    as _i771;
import 'package:theadvance/src/domain/usecases/user/upload_usecase.dart'
    as _i784;
import 'package:theadvance/src/domain/usecases/user/user_deletion_usecase.dart'
    as _i676;
import 'package:theadvance/src/domain/usecases/user_list/get_saved_user_list_usecase.dart'
    as _i843;
import 'package:theadvance/src/domain/usecases/user_list/get_user_list_usecase.dart'
    as _i844;
import 'package:theadvance/src/domain/usecases/user_list/remove_user_list_usecase.dart'
    as _i845;
import 'package:theadvance/src/domain/usecases/user_list/save_user_list_usecase.dart'
    as _i846;
import 'package:theadvance/src/domain/usecases/user_ticket/get_dropdown_list_status.dart'
    as _i447;
import 'package:theadvance/src/domain/usecases/user_ticket/get_user_ticket.dart'
    as _i448;
import 'package:theadvance/src/module/register_module.dart' as _i1001;
import 'package:theadvance/src/presentation/_blocs/authentication/authentication_bloc.dart'
    as _i956;
import 'package:theadvance/src/presentation/_blocs/collaborator_user/collaborator_user_bloc.dart'
    as _i801;
import 'package:theadvance/src/presentation/_blocs/event_authentication_bloc/event_authentication_bloc.dart'
    as _i3;
import 'package:theadvance/src/presentation/_blocs/general_bloc/general_bloc.dart'
    as _i978;
import 'package:theadvance/src/presentation/action_attendance/bloc/action_attendance_bloc.dart'
    as _i985;
import 'package:theadvance/src/presentation/add_tags_image/bloc/add_tags_image_bloc.dart'
    as _i975;
import 'package:theadvance/src/presentation/assign_task/bloc/assign_task_bloc.dart'
    as _i998;
import 'package:theadvance/src/presentation/bed_selection/bloc/bed_selection_bloc.dart'
    as _i863;
import 'package:theadvance/src/presentation/branch_chat_list/bloc/branch_chat_list_bloc.dart'
    as _i905;
import 'package:theadvance/src/presentation/branch_selection/bloc/branch_selection_bloc.dart'
    as _i853;
import 'package:theadvance/src/presentation/chat/bloc/chat_bloc.dart' as _i430;
import 'package:theadvance/src/presentation/chat_list/bloc/chat_list_bloc.dart'
    as _i735;
import 'package:theadvance/src/presentation/chat_select_branch/bloc/chat_select_branch_bloc.dart'
    as _i526;
import 'package:theadvance/src/presentation/checkin_photo/bloc/checkin_photo_bloc.dart'
    as _i954;
import 'package:theadvance/src/presentation/collaborator/checkin_reminder/bloc/checkin_reminder_bloc.dart'
    as _i828;
import 'package:theadvance/src/presentation/collaborator/confirm_otp/bloc/confirm_otp_bloc.dart'
    as _i868;
import 'package:theadvance/src/presentation/collaborator/create_support_request/bloc/create_support_requests_bloc.dart'
    as _i988;
import 'package:theadvance/src/presentation/collaborator/creating_eform/bloc/creating_eform_bloc.dart'
    as _i851;
import 'package:theadvance/src/presentation/collaborator/creating_task/bloc/creating_task_bloc.dart'
    as _i864;
import 'package:theadvance/src/presentation/collaborator/creating_task/selecting_office/bloc/selecting_office_bloc.dart'
    as _i855;
import 'package:theadvance/src/presentation/collaborator/creating_task/selecting_staff/bloc/selecting_staff_bloc.dart'
    as _i917;
import 'package:theadvance/src/presentation/collaborator/creating_task/sheet/bloc/repeat_task_bloc.dart'
    as _i420;
import 'package:theadvance/src/presentation/collaborator/customer/customer/bloc/customer_bloc.dart'
    as _i958;
import 'package:theadvance/src/presentation/collaborator/customer/function_room/bloc/function_room_bloc.dart'
    as _i918;
import 'package:theadvance/src/presentation/collaborator/customer/info_customer/bloc/info_customer_bloc.dart'
    as _i586;
import 'package:theadvance/src/presentation/collaborator/detail_eform/approval_otp/bloc/approving_otp_bloc.dart'
    as _i959;
import 'package:theadvance/src/presentation/collaborator/detail_eform/detail_eform/bloc/detail_eform_bloc.dart'
    as _i612;
import 'package:theadvance/src/presentation/collaborator/detail_eform/detail_eform/sheet/refuse_bottom_sheet/bloc/refuse_bloc.dart'
    as _i866;
import 'package:theadvance/src/presentation/collaborator/detail_eform/detail_eform/sheet/type_approving_bottom_sheet/bloc/type_approving_bloc.dart'
    as _i914;
import 'package:theadvance/src/presentation/collaborator/detail_job_scheduler/bloc/detail_job_scheduler_bloc.dart'
    as _i493;
import 'package:theadvance/src/presentation/collaborator/detail_job_scheduler/sheet/bloc/feedback_bloc.dart'
    as _i981;
import 'package:theadvance/src/presentation/collaborator/detail_news/bloc/detail_news_bloc.dart'
    as _i631;
import 'package:theadvance/src/presentation/collaborator/detail_notification/bloc/detail_notification_bloc.dart'
    as _i944;
import 'package:theadvance/src/presentation/collaborator/edit_home_menu/bloc/edit_home_menu_bloc.dart'
    as _i947;
import 'package:theadvance/src/presentation/collaborator/eform/bloc/eform_bloc.dart'
    as _i747;
import 'package:theadvance/src/presentation/collaborator/eform/sheet/eform_category_bottom_sheet/bloc/eform_category_bloc.dart'
    as _i896;
import 'package:theadvance/src/presentation/collaborator/history_checkin/bloc/history_checkin_bloc.dart'
    as _i996;
import 'package:theadvance/src/presentation/collaborator/home/<USER>/home_bloc.dart'
    as _i940;
import 'package:theadvance/src/presentation/collaborator/home_find/bloc/home_find_bloc.dart'
    as _i685;
import 'package:theadvance/src/presentation/collaborator/job_scheduler/bloc/job_scheduler_bloc.dart'
    as _i900;
import 'package:theadvance/src/presentation/collaborator/language/bloc/language_bloc.dart'
    as _i4;
import 'package:theadvance/src/presentation/collaborator/login/bloc/login_bloc.dart'
    as _i970;
import 'package:theadvance/src/presentation/collaborator/monthly_history_checkin/bloc/monthly_history_checkin_bloc.dart'
    as _i971;
import 'package:theadvance/src/presentation/collaborator/more/bloc/more_bloc.dart'
    as _i993;
import 'package:theadvance/src/presentation/collaborator/news/bloc/news_bloc.dart'
    as _i607;
import 'package:theadvance/src/presentation/collaborator/notifications/bloc/notifications_bloc.dart'
    as _i867;
import 'package:theadvance/src/presentation/collaborator/order_food/bloc/order_food_bloc.dart'
    as _i608;
import 'package:theadvance/src/presentation/collaborator/order_food/bloc/order_food_service_bloc.dart'
    as _i943;
import 'package:theadvance/src/presentation/collaborator/profile/bloc/profile_bloc.dart'
    as _i852;
import 'package:theadvance/src/presentation/collaborator/service_and_product/bloc/service_and_product_bloc.dart'
    as _i999;
import 'package:theadvance/src/presentation/collaborator/set_password/bloc/set_password_bloc.dart'
    as _i953;
import 'package:theadvance/src/presentation/collaborator/setting/bloc/setting_bloc.dart'
    as _i990;
import 'package:theadvance/src/presentation/collaborator/support_requests/bloc/support_requests_bloc.dart'
    as _i997;
import 'package:theadvance/src/presentation/collaborator/tabbar/bloc/tabbar_bloc.dart'
    as _i693;
import 'package:theadvance/src/presentation/comment_list/bloc/comment_list_bloc.dart'
    as _i901;
import 'package:theadvance/src/presentation/consultation_customer/bloc/consultation_customer_bloc.dart'
    as _i991;
import 'package:theadvance/src/presentation/consultation_customer/bloc/service_detail_bloc.dart'
    as _i984;
import 'package:theadvance/src/presentation/consultation_history/bloc/consultation_history_bloc.dart'
    as _i972;
import 'package:theadvance/src/presentation/consultation_history_detail/bloc/consultation_history_detail_bloc.dart'
    as _i977;
import 'package:theadvance/src/presentation/consultation_manager/bloc/consultation_manager_bloc.dart'
    as _i1000;
import 'package:theadvance/src/presentation/create_chat_folder/bloc/create_chat_folder_bloc.dart'
    as _i558;
import 'package:theadvance/src/presentation/create_chat_group/bloc/create_chat_group_bloc.dart'
    as _i477;
import 'package:theadvance/src/presentation/create_customer/bloc/create_customer_bloc.dart'
    as _i951;
import 'package:theadvance/src/presentation/customer_booking_info/bloc/customer_booking_info_bloc.dart'
    as _i974;
import 'package:theadvance/src/presentation/customer_info_details/bloc/customer_info_details_bloc.dart'
    as _i969;
import 'package:theadvance/src/presentation/customer_list/bloc/customer_list_bloc.dart'
    as _i950;
import 'package:theadvance/src/presentation/customer_profile/bloc/customer_profile_bloc.dart'
    as _i973;
import 'package:theadvance/src/presentation/customer_record/bloc/customer_record_bloc.dart'
    as _i700;
import 'package:theadvance/src/presentation/customer_schedule/bloc/customer_schedule_bloc.dart'
    as _i957;
import 'package:theadvance/src/presentation/detail_crm_customer/bloc/detail_crm_customer_bloc.dart'
    as _i980;
import 'package:theadvance/src/presentation/detail_staff_evaluation_period/bloc/detail_staff_evaluation_period_bloc.dart'
    as _i684;
import 'package:theadvance/src/presentation/dev/bloc/dev_bloc.dart' as _i799;
import 'package:theadvance/src/presentation/group_chat_detail/bloc/group_chat_detail_bloc.dart'
    as _i827;
import 'package:theadvance/src/presentation/hr_organization/bloc/hr_organization_bloc.dart'
    as _i718;
import 'package:theadvance/src/presentation/important_notes/bloc/important_notes_bloc.dart'
    as _i694;
import 'package:theadvance/src/presentation/kpi_employee/bloc/kpi_employee_bloc.dart'
    as _i861;
import 'package:theadvance/src/presentation/like_list/bloc/like_list_bloc.dart'
    as _i763;
import 'package:theadvance/src/presentation/list_customer/bloc/list_customer_bloc.dart'
    as _i902;
import 'package:theadvance/src/presentation/medical_department_list/bloc/medical_department_list_bloc.dart'
    as _i646;
import 'package:theadvance/src/presentation/medical_log_detail/bloc/medical_log_detail_bloc.dart'
    as _i989;
import 'package:theadvance/src/presentation/medical_product_creation/bloc/medical_product_creation_bloc.dart'
    as _i983;
import 'package:theadvance/src/presentation/medical_service_creation/bloc/medical_service_creation_bloc.dart'
    as _i992;
import 'package:theadvance/src/presentation/medical_service_list/bloc/medical_service_list_bloc.dart'
    as _i492;
import 'package:theadvance/src/presentation/medical_service_log_list/bloc/medical_service_log_list_bloc.dart'
    as _i948;
import 'package:theadvance/src/presentation/medical_template_list/bloc/medical_template_list_bloc.dart'
    as _i915;
import 'package:theadvance/src/presentation/medicine_detail/bloc/medicine_detail_bloc.dart'
    as _i941;
import 'package:theadvance/src/presentation/note_details/bloc/note_details_bloc.dart'
    as _i836;
import 'package:theadvance/src/presentation/notification_list/bloc/notification_list_bloc.dart'
    as _i496;
import 'package:theadvance/src/presentation/product_confirm/bloc/product_confirm_bloc.dart'
    as _i916;
import 'package:theadvance/src/presentation/product_confirm/bloc/product_detail_confirm_bloc.dart'
    as _i800;
import 'package:theadvance/src/presentation/px_list/bloc/px_list_bloc.dart'
    as _i995;
import 'package:theadvance/src/presentation/px_recheck/bloc/px_recheck_bloc.dart'
    as _i994;
import 'package:theadvance/src/presentation/px_task_list/bloc/px_task_list_bloc.dart'
    as _i986;
import 'package:theadvance/src/presentation/px_unasigned/bloc/px_unasigned_bloc.dart'
    as _i979;
import 'package:theadvance/src/presentation/px_unasigned_update/bloc/px_unasigned_update_bloc.dart'
    as _i982;
import 'package:theadvance/src/presentation/rating_human/bloc/rating_human_bloc.dart'
    as _i976;
import 'package:theadvance/src/presentation/schedule_details/bloc/schedule_details_bloc.dart'
    as _i802;
import 'package:theadvance/src/presentation/select_px_room/bloc/select_px_room_bloc.dart'
    as _i931;
import 'package:theadvance/src/presentation/settings/fonts/fonts_bloc.dart'
    as _i494;
import 'package:theadvance/src/presentation/settings/multi_language/multi_language_bloc.dart'
    as _i746;
import 'package:theadvance/src/presentation/settings/theme/theme_bloc.dart'
    as _i686;
import 'package:theadvance/src/presentation/staff_evaluation_periods/bloc/staff_evaluation_periods_bloc.dart'
    as _i854;
import 'package:theadvance/src/presentation/story_detail/bloc/story_detail_bloc.dart'
    as _i842;
import 'package:theadvance/src/presentation/story_list/bloc/sticker_bloc.dart'
    as _i835;
import 'package:theadvance/src/presentation/story_list/bloc/story_list_bloc.dart'
    as _i903;
import 'package:theadvance/src/presentation/story_list/bloc/story_write_bloc.dart'
    as _i955;
import 'package:theadvance/src/presentation/story_person_list/bloc/story_person_list_bloc.dart'
    as _i495;
import 'package:theadvance/src/presentation/tag_image/bloc/tag_image_bloc.dart'
    as _i987;
import 'package:theadvance/src/presentation/taking_care_customer/bloc/taking_care_customer_bloc.dart'
    as _i837;
import 'package:theadvance/src/presentation/ticket/bloc/ticket_bloc.dart'
    as _i942;
import 'package:theadvance/src/presentation/ticket_detail/bloc/ticket_active_bloc.dart'
    as _i764;
import 'package:theadvance/src/presentation/ticket_detail/bloc/ticket_detail_bloc.dart'
    as _i952;
import 'package:theadvance/src/presentation/user_list/bloc/user_list_bloc.dart'
    as _i862;
import 'package:theadvance/src/presentation/user_ticket/bloc/drop_down_status_bloc.dart'
    as _i904;
import 'package:theadvance/src/presentation/user_ticket/bloc/user_ticket_bloc.dart'
    as _i949;
import 'package:theadvance/src/presentation/widgets/user_profile/bloc/user_profile_bloc.dart'
    as _i960;

extension GetItInjectableX on _i1.GetIt {
  // initializes the registration of main-scope dependencies inside of GetIt
  _i1.GetIt init({
    String? environment,
    _i2.EnvironmentFilter? environmentFilter,
  }) {
    final gh = _i2.GetItHelper(this, environment, environmentFilter);
    final registerModule = _$RegisterModule();
    gh.factory<_i3.EventAuthenticationBloc>(
      () => _i3.EventAuthenticationBloc(),
    );
    gh.factory<_i4.LanguageBloc>(() => _i4.LanguageBloc());
    gh.lazySingleton<_i5.StringeeHelper>(() => _i5.StringeeHelper());
    gh.lazySingleton<_i6.Mapper>(() => _i6.Mapper());
    gh.lazySingleton<_i7.DeeplinkHelper>(() => _i7.DeeplinkHelper());
    gh.lazySingleton<_i8.DefaultCacheManager>(
      () => registerModule.cacheManager,
    );
    gh.lazySingleton<_i9.EZCache>(() => registerModule.collaboratorCache);
    gh.lazySingleton<_i10.AppRouter>(() => registerModule.appRouter);
    gh.factory<_i11.Dio>(
      () => registerModule.collaboratorStaticApiDio,
      instanceName: 'StaticApiDio',
    );
    gh.factory<_i11.Dio>(
      () => registerModule.collaboratorApiSocialDio,
      instanceName: 'ApiSocialDio',
    );
    gh.factory<_i11.Dio>(
      () => registerModule.collaboratorApiDio,
      instanceName: 'ApiDio',
    );
    gh.factory<String>(
      () => registerModule.collaboratorStaticApiBaseUrl,
      instanceName: 'StaticApiBaseUrl',
    );
    gh.factory<String>(
      () => registerModule.collaboratorApiSocialBaseUrl,
      instanceName: 'ApiSocialBaseUrl',
    );
    gh.factory<String>(
      () => registerModule.googleMapApiBaseUrl,
      instanceName: 'ApiGoogleMapBaseUrl',
    );
    gh.lazySingleton<_i12.TagListDao>(() => _i13.TagListDaoImpl());
    gh.factory<_i11.Dio>(
      () => registerModule.aIApiDio,
      instanceName: 'ApiAiDio',
    );
    gh.factory<_i11.Dio>(
      () => registerModule.googleMapApiDio,
      instanceName: 'ApiGoogleMapDio',
    );
    gh.factory<_i11.Dio>(
      () => registerModule.customerStaticApiDio,
      instanceName: 'StaticCustomerApiDio',
    );
    gh.factory<String>(
      () => registerModule.aIApiBaseUrl,
      instanceName: 'ApiAiBaseUrl',
    );
    gh.factory<String>(
      () => registerModule.customerStaticApiBaseUrl,
      instanceName: 'StaticCustomerApiBaseUrl',
    );
    gh.lazySingleton<_i14.StoryListApiService>(
      () => registerModule.storyListApiService(
        gh<_i11.Dio>(instanceName: 'ApiSocialDio'),
        gh<String>(instanceName: 'ApiSocialBaseUrl'),
      ),
    );
    gh.lazySingleton<_i15.CommentListApiService>(
      () => registerModule.commentListApiService(
        gh<_i11.Dio>(instanceName: 'ApiSocialDio'),
        gh<String>(instanceName: 'ApiSocialBaseUrl'),
      ),
    );
    gh.lazySingleton<_i16.LikeListApiService>(
      () => registerModule.likeListApiService(
        gh<_i11.Dio>(instanceName: 'ApiSocialDio'),
        gh<String>(instanceName: 'ApiSocialBaseUrl'),
      ),
    );
    gh.lazySingleton<_i17.StoryPersonListApiService>(
      () => registerModule.storyPersonListApiService(
        gh<_i11.Dio>(instanceName: 'ApiSocialDio'),
        gh<String>(instanceName: 'ApiSocialBaseUrl'),
      ),
    );
    gh.lazySingleton<_i18.TagListApiService>(
      () => registerModule.tagListApiService(
        gh<_i11.Dio>(instanceName: 'ApiSocialDio'),
        gh<String>(instanceName: 'ApiSocialBaseUrl'),
      ),
    );
    gh.lazySingleton<_i18.StoryDetailApiService>(
      () => registerModule.storyDetailApiService(
        gh<_i11.Dio>(instanceName: 'ApiSocialDio'),
        gh<String>(instanceName: 'ApiSocialBaseUrl'),
      ),
    );
    gh.lazySingleton<_i18.NotificationListApiService>(
      () => registerModule.notificationListApiService(
        gh<_i11.Dio>(instanceName: 'ApiSocialDio'),
        gh<String>(instanceName: 'ApiSocialBaseUrl'),
      ),
    );
    gh.factory<String>(
      () => registerModule.collaboratorApiBaseUrl,
      instanceName: 'ApiBaseUrl',
    );
    gh.lazySingleton<_i19.MedicalServiceCreationDao>(
      () => _i20.MedicalServiceCreationDaoImpl(gh<_i9.EZCache>()),
    );
    gh.lazySingleton<_i21.PxTaskListDao>(
      () => _i22.PxTaskListDaoImpl(gh<_i23.EZCache>()),
    );
    gh.lazySingleton<_i24.LocationGoogleDao>(
      () => _i25.LocationGoogleDaoImpl(gh<_i23.EZCache>()),
    );
    gh.lazySingleton<_i26.GroupChatDetailDao>(
      () => _i27.GroupChatDetailDaoImpl(gh<_i23.EZCache>()),
    );
    gh.lazySingleton<_i28.CheckinPhotoDao>(
      () => _i29.CheckinPhotoDaoImpl(gh<_i23.EZCache>()),
    );
    gh.lazySingleton<_i30.MedicalLogDetailDao>(
      () => _i31.MedicalLogDetailDaoImpl(gh<_i9.EZCache>()),
    );
    gh.lazySingleton<_i32.PxRecheckDao>(
      () => _i33.PxRecheckDaoImpl(gh<_i23.EZCache>()),
    );
    gh.lazySingleton<_i34.HrOrganizationDao>(
      () => _i35.HrOrganizationDaoImpl(gh<_i23.EZCache>()),
    );
    gh.lazySingleton<_i36.TakingCareCustomerDao>(
      () => _i37.TakingCareCustomerDaoImpl(gh<_i23.EZCache>()),
    );
    gh.lazySingleton<_i38.PxListDao>(
      () => _i39.PxListDaoImpl(gh<_i23.EZCache>()),
    );
    gh.lazySingleton<_i40.ChatListRepository>(
      () => _i41.ChatListRepositoryImpl(gh<_i23.EZCache>()),
    );
    gh.lazySingleton<_i42.BranchSelectionDao>(
      () => _i43.BranchSelectionDaoImpl(gh<_i9.EZCache>()),
    );
    gh.lazySingleton<_i44.CreateChatGroupRepository>(
      () => _i45.CreateChatGroupRepositoryImpl(gh<_i23.EZCache>()),
    );
    gh.lazySingleton<_i46.ServiceAndProductDao>(
      () => _i47.ServiceAndProductDaoImpl(gh<_i9.EZCache>()),
    );
    gh.lazySingleton<_i48.PxUnasignedDao>(
      () => _i49.PxUnasignedDaoImpl(gh<_i23.EZCache>()),
    );
    gh.lazySingleton<_i50.MedicalProductCreationDao>(
      () => _i51.MedicalProductCreationDaoImpl(gh<_i9.EZCache>()),
    );
    gh.lazySingleton<_i52.CustomerListDao>(
      () => _i53.CustomerListDaoImpl(gh<_i23.EZCache>()),
    );
    gh.lazySingleton<_i54.MedicalServiceLogListDao>(
      () => _i55.MedicalServiceLogListDaoImpl(gh<_i9.EZCache>()),
    );
    gh.lazySingleton<_i56.CustomerScheduleDao>(
      () => _i57.CustomerScheduleDaoImpl(gh<_i9.EZCache>()),
    );
    gh.lazySingleton<_i58.DetailCrmCustomerDao>(
      () => _i59.DetailCrmCustomerDaoImpl(gh<_i23.EZCache>()),
    );
    gh.lazySingleton<_i60.BranchChatListDao>(
      () => _i61.BranchChatListDaoImpl(gh<_i23.EZCache>()),
    );
    gh.lazySingleton<_i62.CreateChatFolderDao>(
      () => _i63.CreateChatFolderDaoImpl(gh<_i23.EZCache>()),
    );
    gh.lazySingleton<_i64.FeedbackDao>(
      () => _i65.FeedbackDaoImpl(gh<_i23.EZCache>()),
    );
    gh.lazySingleton<_i66.TrackLoginUseCase>(
      () => _i66.TrackLoginUseCase(gh<_i67.TrackingRepository>()),
    );
    gh.lazySingleton<_i68.TrackValidInstallUseCase>(
      () => _i68.TrackValidInstallUseCase(gh<_i67.TrackingRepository>()),
    );
    gh.lazySingleton<_i69.TrackOpenLandingPageUseCase>(
      () => _i69.TrackOpenLandingPageUseCase(gh<_i67.TrackingRepository>()),
    );
    gh.lazySingleton<_i70.TrackBannerUseCase>(
      () => _i70.TrackBannerUseCase(gh<_i67.TrackingRepository>()),
    );
    gh.lazySingleton<_i71.TrackOpenAppUseCase>(
      () => _i71.TrackOpenAppUseCase(gh<_i67.TrackingRepository>()),
    );
    gh.lazySingleton<_i72.SendEventUseCase>(
      () => _i72.SendEventUseCase(gh<_i67.TrackingRepository>()),
    );
    gh.lazySingleton<_i73.TrackPopupUseCase>(
      () => _i73.TrackPopupUseCase(gh<_i67.TrackingRepository>()),
    );
    gh.lazySingleton<_i74.TrackRegisterUseCase>(
      () => _i74.TrackRegisterUseCase(gh<_i67.TrackingRepository>()),
    );
    gh.lazySingleton<_i75.TrackNotificationUseCase>(
      () => _i75.TrackNotificationUseCase(gh<_i67.TrackingRepository>()),
    );
    gh.lazySingleton<_i76.PxUnasignedUpdateDao>(
      () => _i77.PxUnasignedUpdateDaoImpl(gh<_i23.EZCache>()),
    );
    gh.lazySingleton<_i78.AssignTaskDao>(
      () => _i79.AssignTaskDaoImpl(gh<_i9.EZCache>()),
    );
    gh.lazySingleton<_i80.ConsultationCustomerDao>(
      () => _i81.ConsultationCustomerDaoImpl(gh<_i23.EZCache>()),
    );
    gh.lazySingleton<_i82.MedicalServiceListDao>(
      () => _i83.MedicalServiceListDaoImpl(gh<_i9.EZCache>()),
    );
    gh.lazySingleton<_i84.DevDao>(() => _i85.DevDaoImpl(gh<_i23.EZCache>()));
    gh.lazySingleton<_i86.AddTagsImageDao>(
      () => _i87.AddTagsImageDaoImpl(gh<_i23.EZCache>()),
    );
    gh.lazySingleton<_i9.StaticApiService>(
      () => registerModule.collaboratorStaticApiService(
        gh<_i11.Dio>(instanceName: 'StaticApiDio'),
        gh<String>(instanceName: 'StaticApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i88.MedicalDepartmentListDao>(
      () => _i89.MedicalDepartmentListDaoImpl(gh<_i9.EZCache>()),
    );
    gh.lazySingleton<_i90.NotificationListRepository>(
      () => _i91.NotificationListRepositoryImpl(
        gh<_i18.NotificationListApiService>(),
        gh<_i23.EZCache>(),
      ),
    );
    gh.lazySingleton<_i92.CustomerProfileDao>(
      () => _i93.CustomerProfileDaoImpl(gh<_i9.EZCache>()),
    );
    gh.lazySingleton<_i94.ChatListDao>(
      () => _i95.ChatListDaoImpl(gh<_i23.EZCache>()),
    );
    gh.lazySingleton<_i96.ConsultationManagerDao>(
      () => _i97.ConsultationManagerDaoImpl(gh<_i23.EZCache>()),
    );
    gh.lazySingleton<_i98.CreateCustomerDao>(
      () => _i99.CreateCustomerDaoImpl(gh<_i23.EZCache>()),
    );
    gh.lazySingleton<_i100.CustomerBookingInfoDao>(
      () => _i101.CustomerBookingInfoDaoImpl(gh<_i23.EZCache>()),
    );
    gh.lazySingleton<_i102.NoteDetailsDao>(
      () => _i103.NoteDetailsDaoImpl(gh<_i9.EZCache>()),
    );
    gh.lazySingleton<_i104.CreateChatGroupDao>(
      () => _i105.CreateChatGroupDaoImpl(gh<_i23.EZCache>()),
    );
    gh.lazySingleton<_i106.ProductConfirmDao>(
      () => _i107.ProductConfirmDaoImpl(gh<_i23.EZCache>()),
    );
    gh.lazySingleton<_i108.ListCustomerDao>(
      () => _i109.ListCustomerDaoImpl(gh<_i9.EZCache>()),
    );
    gh.lazySingleton<_i110.CustomerRecordDao>(
      () => _i111.CustomerRecordDaoImpl(gh<_i23.EZCache>()),
    );
    gh.lazySingleton<_i112.MedicineDetailDao>(
      () => _i113.MedicineDetailDaoImpl(gh<_i9.EZCache>()),
    );
    gh.lazySingleton<_i114.ChatSelectBranchDao>(
      () => _i115.ChatSelectBranchDaoImpl(gh<_i23.EZCache>()),
    );
    gh.lazySingleton<_i116.StoryDetailDao>(
      () => _i117.StoryDetailDaoImpl(gh<_i23.EZCache>()),
    );
    gh.lazySingleton<_i118.ScheduleDetailsDao>(
      () => _i119.ScheduleDetailsDaoImpl(gh<_i9.EZCache>()),
    );
    gh.lazySingleton<_i120.ImportantNotesDao>(
      () => _i121.ImportantNotesDaoImpl(gh<_i9.EZCache>()),
    );
    gh.lazySingleton<_i122.MedicalTemplateListDao>(
      () => _i123.MedicalTemplateListDaoImpl(gh<_i9.EZCache>()),
    );
    gh.lazySingleton<_i124.StoryDetailRepository>(
      () => _i125.StoryDetailRepositoryImpl(
        gh<_i18.StoryDetailApiService>(),
        gh<_i23.EZCache>(),
      ),
    );
    gh.lazySingleton<_i126.CreateChatFolderRepository>(
      () => _i127.CreateChatFolderRepositoryImpl(gh<_i23.EZCache>()),
    );
    gh.lazySingleton<_i128.UserListDao>(
      () => _i129.UserListDaoImpl(gh<_i23.EZCache>()),
    );
    gh.lazySingleton<_i130.HelperRepository>(
      () => _i131.HelperRepositoryImpl(gh<_i9.EZCache>()),
    );
    gh.lazySingleton<_i132.SelectPxRoomDao>(
      () => _i133.SelectPxRoomDaoImpl(gh<_i23.EZCache>()),
    );
    gh.lazySingleton<_i134.CustomerInfoDetailsDao>(
      () => _i135.CustomerInfoDetailsDaoImpl(gh<_i9.EZCache>()),
    );
    gh.lazySingleton<_i136.NotificationListDao>(
      () => _i137.NotificationListDaoImpl(gh<_i23.EZCache>()),
    );
    gh.lazySingleton<_i138.StaffEvaluationPeriodsDao>(
      () => _i139.StaffEvaluationPeriodsDaoImpl(gh<_i23.EZCache>()),
    );
    gh.lazySingleton<_i140.DetailStaffEvaluationPeriodDao>(
      () => _i141.DetailStaffEvaluationPeriodDaoImpl(gh<_i23.EZCache>()),
    );
    gh.lazySingleton<_i142.SettingRepository>(
      () => _i143.SettingRepositoryImpl(cache: gh<_i9.EZCache>()),
    );
    gh.lazySingleton<_i144.MediaUploadApiService>(
      () => registerModule.recordApiService(
        gh<_i11.Dio>(instanceName: 'StaticCustomerApiDio'),
        gh<String>(instanceName: 'StaticCustomerApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i145.ChatApiService>(
      () => registerModule.chatApiService(
        gh<_i11.Dio>(instanceName: 'StaticCustomerApiDio'),
        gh<String>(instanceName: 'StaticCustomerApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i146.SocialApiService>(
      () => registerModule.socialApiService(
        gh<_i11.Dio>(instanceName: 'StaticCustomerApiDio'),
        gh<String>(instanceName: 'StaticCustomerApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i18.GroupChatDetailApiService>(
      () => registerModule.groupChatDetailApiService(
        gh<_i11.Dio>(instanceName: 'StaticCustomerApiDio'),
        gh<String>(instanceName: 'StaticCustomerApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i147.StickerSocialApiService>(
      () => registerModule.stickerSocialApiService(
        gh<_i11.Dio>(instanceName: 'StaticCustomerApiDio'),
        gh<String>(instanceName: 'StaticCustomerApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i148.TicketDetailDao>(
      () => _i149.TicketDetailDaoImpl(gh<_i23.EZCache>()),
    );
    gh.lazySingleton<_i150.ChatDao>(
      () => _i151.ChatDaoImpl(gh<_i23.EZCache>()),
    );
    gh.lazySingleton<_i152.TagListRepository>(
      () => _i153.TagListRepositoryImpl(gh<_i18.TagListApiService>()),
    );
    gh.factory<_i154.SortFolderChatListUseCase>(
      () => _i154.SortFolderChatListUseCase(gh<_i40.ChatListRepository>()),
    );
    gh.factory<_i155.JoinGroupChatListUseCase>(
      () => _i155.JoinGroupChatListUseCase(gh<_i40.ChatListRepository>()),
    );
    gh.factory<_i156.GetTotalUnreadChatListUseCase>(
      () => _i156.GetTotalUnreadChatListUseCase(gh<_i40.ChatListRepository>()),
    );
    gh.factory<_i157.GetSavedChatListUseCase>(
      () => _i157.GetSavedChatListUseCase(gh<_i40.ChatListRepository>()),
    );
    gh.factory<_i158.PinConversationChatListUseCase>(
      () => _i158.PinConversationChatListUseCase(gh<_i40.ChatListRepository>()),
    );
    gh.factory<_i159.SearchChatListUseCase>(
      () => _i159.SearchChatListUseCase(gh<_i40.ChatListRepository>()),
    );
    gh.factory<_i160.MarkAsReadChatListUseCase>(
      () => _i160.MarkAsReadChatListUseCase(gh<_i40.ChatListRepository>()),
    );
    gh.factory<_i161.SearchMessageChatListUseCase>(
      () => _i161.SearchMessageChatListUseCase(gh<_i40.ChatListRepository>()),
    );
    gh.factory<_i162.GetConversationByInviteIdChatListUseCase>(
      () => _i162.GetConversationByInviteIdChatListUseCase(
        gh<_i40.ChatListRepository>(),
      ),
    );
    gh.factory<_i163.GetRecentContactsChatListUseCase>(
      () =>
          _i163.GetRecentContactsChatListUseCase(gh<_i40.ChatListRepository>()),
    );
    gh.factory<_i164.GetUnreadConversationsChatListUseCase>(
      () => _i164.GetUnreadConversationsChatListUseCase(
        gh<_i40.ChatListRepository>(),
      ),
    );
    gh.factory<_i165.GetChatListUseCase>(
      () => _i165.GetChatListUseCase(gh<_i40.ChatListRepository>()),
    );
    gh.factory<_i166.UpdatePinConversationChatListUseCase>(
      () => _i166.UpdatePinConversationChatListUseCase(
        gh<_i40.ChatListRepository>(),
      ),
    );
    gh.factory<_i167.RemoveChatListUseCase>(
      () => _i167.RemoveChatListUseCase(gh<_i40.ChatListRepository>()),
    );
    gh.factory<_i168.SaveChatListUseCase>(
      () => _i168.SaveChatListUseCase(gh<_i40.ChatListRepository>()),
    );
    gh.lazySingleton<_i169.StoryPersonListRepository>(
      () => _i170.StoryPersonListRepositoryImpl(
        gh<_i17.StoryPersonListApiService>(),
      ),
    );
    gh.lazySingleton<_i171.StoryListRepository>(
      () => _i172.StoryListRepositoryImpl(
        gh<_i14.StoryListApiService>(),
        gh<_i146.SocialApiService>(),
      ),
    );
    gh.lazySingleton<_i18.LocationGoogleApiService>(
      () => registerModule.locationGoogleApiService(
        gh<_i11.Dio>(instanceName: 'ApiGoogleMapDio'),
        gh<String>(instanceName: 'ApiGoogleMapBaseUrl'),
      ),
    );
    gh.lazySingleton<_i173.GroupChatDetailRepository>(
      () => _i174.GroupChatDetailRepositoryImpl(
        gh<_i18.GroupChatDetailApiService>(),
        gh<_i23.EZCache>(),
      ),
    );
    gh.factory<_i175.RemoveStoryDetailUseCase>(
      () => _i175.RemoveStoryDetailUseCase(gh<_i124.StoryDetailRepository>()),
    );
    gh.factory<_i176.GetSavedStoryDetailUseCase>(
      () => _i176.GetSavedStoryDetailUseCase(gh<_i124.StoryDetailRepository>()),
    );
    gh.factory<_i177.GetStoryDetailUseCase>(
      () => _i177.GetStoryDetailUseCase(gh<_i124.StoryDetailRepository>()),
    );
    gh.factory<_i178.SaveStoryDetailUseCase>(
      () => _i178.SaveStoryDetailUseCase(gh<_i124.StoryDetailRepository>()),
    );
    gh.lazySingleton<_i179.ChatRepository>(
      () => _i180.ChatRepositoryImpl(
        gh<_i23.EZCache>(),
        gh<_i145.ChatApiService>(),
      ),
    );
    gh.factory<_i181.GetSavedCreateChatGroupUseCase>(
      () => _i181.GetSavedCreateChatGroupUseCase(
        gh<_i44.CreateChatGroupRepository>(),
      ),
    );
    gh.factory<_i182.SaveCreateChatGroupUseCase>(
      () => _i182.SaveCreateChatGroupUseCase(
        gh<_i44.CreateChatGroupRepository>(),
      ),
    );
    gh.factory<_i183.UserLoadCreateChatGroupUseCase>(
      () => _i183.UserLoadCreateChatGroupUseCase(
        gh<_i44.CreateChatGroupRepository>(),
      ),
    );
    gh.factory<_i184.GetCreateChatGroupUseCase>(
      () =>
          _i184.GetCreateChatGroupUseCase(gh<_i44.CreateChatGroupRepository>()),
    );
    gh.factory<_i185.RemoveCreateChatGroupUseCase>(
      () => _i185.RemoveCreateChatGroupUseCase(
        gh<_i44.CreateChatGroupRepository>(),
      ),
    );
    gh.lazySingleton<_i186.StickerSocailRepository>(
      () => _i187.StickerSocialRepositoryImpl(
        gh<_i147.StickerSocialApiService>(),
        gh<_i146.SocialApiService>(),
      ),
    );
    gh.lazySingleton<_i9.EformApiService>(
      () => registerModule.eformApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i9.UserApiService>(
      () => registerModule.collaboratorUserApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i9.StaffApiService>(
      () => registerModule.staffApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i9.HomeApiService>(
      () => registerModule.collaboratorHomeApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i9.CustomerApiService>(
      () => registerModule.collaboratorCustomerApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i9.CheckinApiService>(
      () => registerModule.checkinApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i9.NewsApiService>(
      () => registerModule.collaboratornewsApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i9.NotificationApiService>(
      () => registerModule.collaboratorNotificationApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i9.TaskApiService>(
      () => registerModule.taskApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i9.RequestApiService>(
      () => registerModule.collaboratorRequestApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i9.TrackingApiService>(
      () => registerModule.trackingApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i9.BranchSelectionApiService>(
      () => registerModule.branchSelectionApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i18.ImportantNotesApiService>(
      () => registerModule.importantNotesApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i9.CustomerProfileApiService>(
      () => registerModule.customerProfileApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i9.CustomerInfoDetailsApiService>(
      () => registerModule.customerInfoDetailsApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i18.NoteDetailsApiService>(
      () => registerModule.noteDetailsApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i9.MedicalDepartmentListApiService>(
      () => registerModule.medicalDepartmentListApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i9.ListCustomerApiService>(
      () => registerModule.listCustomerApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i18.ServiceAndProductApiService>(
      () => registerModule.serviceAndProductApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i18.MedicalServiceLogListApiService>(
      () => registerModule.medicalServiceLogListApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i18.MedicalLogDetailApiService>(
      () => registerModule.medicalLogDetailApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i18.MedicineDetailApiService>(
      () => registerModule.medicineDetailApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i18.MedicalTemplateListApiService>(
      () => registerModule.medicalTemplateListApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i18.MedicalProductCreationApiService>(
      () => registerModule.medicalProductCreationApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i18.MedicalServiceCreationApiService>(
      () => registerModule.medicalServiceCreationApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i9.FoodApiService>(
      () => registerModule.foodApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i18.ScheduleDetailsApiService>(
      () => registerModule.scheduleDetailsApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i18.CustomerScheduleApiService>(
      () => registerModule.customerScheduleApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i18.CustomerBookingInfoApiService>(
      () => registerModule.customerBookingInfoApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i18.AssignTaskApiService>(
      () => registerModule.assignTaskApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i18.ChatSelectBranchApiService>(
      () => registerModule.chatSelectBranchApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i18.BranchChatListApiService>(
      () => registerModule.branchChatListApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i18.PxListApiService>(
      () => registerModule.pxListApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i18.PxUnasignedApiService>(
      () => registerModule.pxUnasignedApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i18.PxTaskListApiService>(
      () => registerModule.pxTaskListApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i18.PxUnasignedUpdateApiService>(
      () => registerModule.pxUnasignedUpdateApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i18.TakingCareCustomerApiService>(
      () => registerModule.takingCareCustomerApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i18.PxRecheckApiService>(
      () => registerModule.pxRecheckApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i18.CreateCustomerApiService>(
      () => registerModule.createCustomerApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i18.SelectPxRoomApiService>(
      () => registerModule.selectPxRoomApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i18.CustomerListApiService>(
      () => registerModule.customerListApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i18.ConsultationManagerApiService>(
      () => registerModule.consultationManagerApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i18.ConsultationCustomerApiService>(
      () => registerModule.consultationCustomerApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i18.StaffEvaluationPeriodsApiService>(
      () => registerModule.staffEvaluationPeriodsApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i18.DetailStaffEvaluationPeriodApiService>(
      () => registerModule.detailStaffEvaluationPeriodApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i188.RatingHumanApiService>(
      () => registerModule.ratingHumanApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i189.NoteListApiService>(
      () => registerModule.noteListApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i18.DetailCrmCustomerApiService>(
      () => registerModule.detailCrmCustomerApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i18.HrOrganizationApiService>(
      () => registerModule.hrOrganizationApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i18.CustomerRecordApiService>(
      () => registerModule.customerRecordApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i18.CheckinPhotoApiService>(
      () => registerModule.checkinPhotoApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i18.FeedbackApiService>(
      () => registerModule.feedbackApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i190.CreateChatGroupApiService>(
      () => registerModule.createChatGroupApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i18.UserListApiService>(
      () => registerModule.userListApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i18.CreateChatFolderApiService>(
      () => registerModule.createChatFolderApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i191.TicketApiService>(
      () => registerModule.ticketApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i18.TicketDetailApiService>(
      () => registerModule.ticketDetailApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i192.TicketActiveApiService>(
      () => registerModule.ticketActiveApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i18.DevApiService>(
      () => registerModule.devApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i18.KpiEmployeeApiService>(
      () => registerModule.kpiEmployeeApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i18.ProductConfirmApiService>(
      () => registerModule.productConfirmApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i193.TagImageApiService>(
      () => registerModule.tagImageApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i18.AddTagsImageApiService>(
      () => registerModule.addTagsImageApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.factory<_i18.MedicalServiceListApiService>(
      () => registerModule.medicalServiceListApiService(
        gh<_i11.Dio>(instanceName: 'ApiDio'),
        gh<String>(instanceName: 'ApiBaseUrl'),
      ),
    );
    gh.lazySingleton<_i194.SelectPxRoomRepository>(
      () => _i195.SelectPxRoomRepositoryImpl(
        gh<_i18.SelectPxRoomApiService>(),
        gh<_i23.EZCache>(),
      ),
    );
    gh.lazySingleton<_i196.LikeListRepository>(
      () => _i197.LikeListRepositoryImpl(gh<_i16.LikeListApiService>()),
    );
    gh.lazySingleton<_i198.FoodRepository>(
      () => _i199.FoodRepositoryImpl(
        gh<_i9.FoodApiService>(),
        gh<_i144.MediaUploadApiService>(),
      ),
    );
    gh.lazySingleton<_i200.BranchSelectionRepository>(
      () => _i201.BranchSelectionRepositoryImpl(
        gh<_i9.BranchSelectionApiService>(),
        gh<_i9.EZCache>(),
      ),
    );
    gh.lazySingleton<_i202.TicketActiveRepository>(
      () =>
          _i203.TicketActiveRepositoryImpl(gh<_i192.TicketActiveApiService>()),
    );
    gh.lazySingleton<_i204.MedicalDepartmentListRepository>(
      () => _i205.MedicalDepartmentListRepositoryImpl(
        gh<_i9.MedicalDepartmentListApiService>(),
        gh<_i9.EZCache>(),
      ),
    );
    gh.lazySingleton<_i206.DetailStaffEvaluationPeriodRepository>(
      () => _i207.DetailStaffEvaluationPeriodRepositoryImpl(
        gh<_i18.DetailStaffEvaluationPeriodApiService>(),
        gh<_i23.EZCache>(),
      ),
    );
    gh.lazySingleton<_i208.ImportantNotesRepository>(
      () => _i209.ImportantNotesRepositoryImpl(
        gh<_i18.ImportantNotesApiService>(),
        gh<_i9.EZCache>(),
      ),
    );
    gh.lazySingleton<_i210.UserTickerRepository>(
      () => _i211.StaffEvaluationPeriodsRepositoryImpl(
        gh<_i189.NoteListApiService>(),
      ),
    );
    gh.lazySingleton<_i212.MedicineDetailRepository>(
      () => _i213.MedicineDetailRepositoryImpl(
        gh<_i18.MedicineDetailApiService>(),
        gh<_i9.EZCache>(),
      ),
    );
    gh.factory<_i214.GetSavedChatUseCase>(
      () => _i214.GetSavedChatUseCase(gh<_i179.ChatRepository>()),
    );
    gh.factory<_i215.MessageEditChatUseCase>(
      () => _i215.MessageEditChatUseCase(gh<_i179.ChatRepository>()),
    );
    gh.factory<_i216.GetUserSeenChatUseCase>(
      () => _i216.GetUserSeenChatUseCase(gh<_i179.ChatRepository>()),
    );
    gh.factory<_i217.TranscribeChatUseCase>(
      () => _i217.TranscribeChatUseCase(gh<_i179.ChatRepository>()),
    );
    gh.factory<_i218.SearchChatUseCase>(
      () => _i218.SearchChatUseCase(gh<_i179.ChatRepository>()),
    );
    gh.factory<_i219.GetConversationChatUseCase>(
      () => _i219.GetConversationChatUseCase(gh<_i179.ChatRepository>()),
    );
    gh.factory<_i220.GetPinListChatUseCase>(
      () => _i220.GetPinListChatUseCase(gh<_i179.ChatRepository>()),
    );
    gh.factory<_i221.SaveChatUseCase>(
      () => _i221.SaveChatUseCase(gh<_i179.ChatRepository>()),
    );
    gh.factory<_i222.VotePollChatUseCase>(
      () => _i222.VotePollChatUseCase(gh<_i179.ChatRepository>()),
    );
    gh.factory<_i223.GetUserStickerChatUseCase>(
      () => _i223.GetUserStickerChatUseCase(gh<_i179.ChatRepository>()),
    );
    gh.factory<_i224.UploadFileChatUseCase>(
      () => _i224.UploadFileChatUseCase(gh<_i179.ChatRepository>()),
    );
    gh.factory<_i225.UnpinMessageChatUseCase>(
      () => _i225.UnpinMessageChatUseCase(gh<_i179.ChatRepository>()),
    );
    gh.factory<_i226.ReplyBotMessageChatUseCase>(
      () => _i226.ReplyBotMessageChatUseCase(gh<_i179.ChatRepository>()),
    );
    gh.factory<_i227.ReactChatUseCase>(
      () => _i227.ReactChatUseCase(gh<_i179.ChatRepository>()),
    );
    gh.factory<_i228.GetConversationByIdUseCase>(
      () => _i228.GetConversationByIdUseCase(gh<_i179.ChatRepository>()),
    );
    gh.factory<_i229.SendChatUseCase>(
      () => _i229.SendChatUseCase(gh<_i179.ChatRepository>()),
    );
    gh.factory<_i230.PinMessageChatUseCase>(
      () => _i230.PinMessageChatUseCase(gh<_i179.ChatRepository>()),
    );
    gh.factory<_i231.UpdatePollChatUseCase>(
      () => _i231.UpdatePollChatUseCase(gh<_i179.ChatRepository>()),
    );
    gh.factory<_i232.GetChatUseCase>(
      () => _i232.GetChatUseCase(gh<_i179.ChatRepository>()),
    );
    gh.factory<_i233.MessageRemoveChatUseCase>(
      () => _i233.MessageRemoveChatUseCase(gh<_i179.ChatRepository>()),
    );
    gh.factory<_i234.RemoveChatUseCase>(
      () => _i234.RemoveChatUseCase(gh<_i179.ChatRepository>()),
    );
    gh.factory<_i235.ConversationDetailsUpdateChatUseCase>(
      () => _i235.ConversationDetailsUpdateChatUseCase(
        gh<_i179.ChatRepository>(),
      ),
    );
    gh.factory<_i236.CacheTextScaleRemoveUseCase>(
      () => _i236.CacheTextScaleRemoveUseCase(gh<_i130.HelperRepository>()),
    );
    gh.factory<_i237.CacheTextScaleSaveUseCase>(
      () => _i237.CacheTextScaleSaveUseCase(gh<_i130.HelperRepository>()),
    );
    gh.factory<_i238.SaveDeviceInfoUseCase>(
      () => _i238.SaveDeviceInfoUseCase(gh<_i130.HelperRepository>()),
    );
    gh.factory<_i239.SaveLatitudeUseCase>(
      () => _i239.SaveLatitudeUseCase(gh<_i130.HelperRepository>()),
    );
    gh.factory<_i240.DeleteLongitudeUseCase>(
      () => _i240.DeleteLongitudeUseCase(gh<_i130.HelperRepository>()),
    );
    gh.factory<_i241.DeleteLatitudeUseCase>(
      () => _i241.DeleteLatitudeUseCase(gh<_i130.HelperRepository>()),
    );
    gh.factory<_i242.GetKeyAppsFlyerUseCase>(
      () => _i242.GetKeyAppsFlyerUseCase(gh<_i130.HelperRepository>()),
    );
    gh.factory<_i243.SaveAppVersionUseCase>(
      () => _i243.SaveAppVersionUseCase(gh<_i130.HelperRepository>()),
    );
    gh.factory<_i244.SaveLongitudeUseCase>(
      () => _i244.SaveLongitudeUseCase(gh<_i130.HelperRepository>()),
    );
    gh.factory<_i245.SaveKeyAppflyerUseCase>(
      () => _i245.SaveKeyAppflyerUseCase(gh<_i130.HelperRepository>()),
    );
    gh.lazySingleton<_i246.CacheTextScaleGetUseCase>(
      () => _i246.CacheTextScaleGetUseCase(gh<_i130.HelperRepository>()),
    );
    gh.lazySingleton<_i247.GetPhoneUseCase>(
      () => _i247.GetPhoneUseCase(gh<_i130.HelperRepository>()),
    );
    gh.lazySingleton<_i248.GetAppVersionUseCase>(
      () => _i248.GetAppVersionUseCase(gh<_i130.HelperRepository>()),
    );
    gh.lazySingleton<_i249.GetLatitudeUseCase>(
      () => _i249.GetLatitudeUseCase(gh<_i130.HelperRepository>()),
    );
    gh.lazySingleton<_i250.GetAccessTokenUseCase>(
      () => _i250.GetAccessTokenUseCase(gh<_i130.HelperRepository>()),
    );
    gh.lazySingleton<_i251.GetFirebaseTokenUseCase>(
      () => _i251.GetFirebaseTokenUseCase(gh<_i130.HelperRepository>()),
    );
    gh.lazySingleton<_i252.GetLongitudeUseCase>(
      () => _i252.GetLongitudeUseCase(gh<_i130.HelperRepository>()),
    );
    gh.lazySingleton<_i253.GetDeviceInfoUseCase>(
      () => _i253.GetDeviceInfoUseCase(gh<_i130.HelperRepository>()),
    );
    gh.lazySingleton<_i254.CheckinPhotoRepository>(
      () => _i255.CheckinPhotoRepositoryImpl(
        gh<_i18.CheckinPhotoApiService>(),
        gh<_i23.EZCache>(),
      ),
    );
    gh.lazySingleton<_i256.PxTaskListRepository>(
      () => _i257.PxTaskListRepositoryImpl(
        gh<_i18.PxTaskListApiService>(),
        gh<_i23.EZCache>(),
      ),
    );
    gh.lazySingleton<_i258.TicketRepository>(
      () => _i259.TicketRepositoryImpl(
        gh<_i191.TicketApiService>(),
        gh<_i144.MediaUploadApiService>(),
      ),
    );
    gh.lazySingleton<_i260.RatingHumanRepository>(
      () => _i261.RatingHumanRepositoryImpl(gh<_i188.RatingHumanApiService>()),
    );
    gh.factory<_i262.RemoveNotificationListUseCase>(
      () => _i262.RemoveNotificationListUseCase(
        gh<_i90.NotificationListRepository>(),
      ),
    );
    gh.factory<_i263.GetNotificationListUseCase>(
      () => _i263.GetNotificationListUseCase(
        gh<_i90.NotificationListRepository>(),
      ),
    );
    gh.factory<_i264.PutReadAllSocialUseCase>(
      () =>
          _i264.PutReadAllSocialUseCase(gh<_i90.NotificationListRepository>()),
    );
    gh.factory<_i265.DeleteNotificationSocialUseCase>(
      () => _i265.DeleteNotificationSocialUseCase(
        gh<_i90.NotificationListRepository>(),
      ),
    );
    gh.factory<_i266.SaveNotificationListUseCase>(
      () => _i266.SaveNotificationListUseCase(
        gh<_i90.NotificationListRepository>(),
      ),
    );
    gh.factory<_i267.GetSavedNotificationListUseCase>(
      () => _i267.GetSavedNotificationListUseCase(
        gh<_i90.NotificationListRepository>(),
      ),
    );
    gh.lazySingleton<_i268.UserRepository>(
      () => _i269.UserRepositoryImpl(
        gh<_i9.UserApiService>(),
        gh<_i9.StaticApiService>(),
        gh<_i9.EZCache>(),
      ),
    );
    gh.lazySingleton<_i270.ConsultationCustomerRepository>(
      () => _i271.ConsultationCustomerRepositoryImpl(
        gh<_i18.ConsultationCustomerApiService>(),
        gh<_i23.EZCache>(),
      ),
    );
    gh.factory<_i272.GetTagListUseCase>(
      () => _i272.GetTagListUseCase(gh<_i152.TagListRepository>()),
    );
    gh.lazySingleton<_i273.FeedbackRepository>(
      () => _i274.FeedbackRepositoryImpl(
        gh<_i18.FeedbackApiService>(),
        gh<_i23.EZCache>(),
      ),
    );
    gh.lazySingleton<_i275.AddTagsImageRepository>(
      () => _i276.AddTagsImageRepositoryImpl(
        gh<_i18.AddTagsImageApiService>(),
        gh<_i23.EZCache>(),
      ),
    );
    gh.factory<_i277.GetUserExceptionGroupChatDetailUseCase>(
      () => _i277.GetUserExceptionGroupChatDetailUseCase(
        gh<_i173.GroupChatDetailRepository>(),
      ),
    );
    gh.factory<_i278.AvatarUploadGroupChatDetailUseCase>(
      () => _i278.AvatarUploadGroupChatDetailUseCase(
        gh<_i173.GroupChatDetailRepository>(),
      ),
    );
    gh.factory<_i279.RemoveGroupChatDetailUseCase>(
      () => _i279.RemoveGroupChatDetailUseCase(
        gh<_i173.GroupChatDetailRepository>(),
      ),
    );
    gh.factory<_i280.MemberInfoLoadGroupChatDetailUseCase>(
      () => _i280.MemberInfoLoadGroupChatDetailUseCase(
        gh<_i173.GroupChatDetailRepository>(),
      ),
    );
    gh.factory<_i281.DeleteGroupUseCase>(
      () => _i281.DeleteGroupUseCase(gh<_i173.GroupChatDetailRepository>()),
    );
    gh.factory<_i282.MediaLoadGroupChatDetailUseCase>(
      () => _i282.MediaLoadGroupChatDetailUseCase(
        gh<_i173.GroupChatDetailRepository>(),
      ),
    );
    gh.factory<_i283.UpdateMemberRuleGroupChatDetailUseCase>(
      () => _i283.UpdateMemberRuleGroupChatDetailUseCase(
        gh<_i173.GroupChatDetailRepository>(),
      ),
    );
    gh.factory<_i284.GetGroupChatDetailUseCase>(
      () => _i284.GetGroupChatDetailUseCase(
        gh<_i173.GroupChatDetailRepository>(),
      ),
    );
    gh.factory<_i285.ChangeOwnerGroupChatDetailUseCase>(
      () => _i285.ChangeOwnerGroupChatDetailUseCase(
        gh<_i173.GroupChatDetailRepository>(),
      ),
    );
    gh.factory<_i286.LinkLoadGroupChatDetailUseCase>(
      () => _i286.LinkLoadGroupChatDetailUseCase(
        gh<_i173.GroupChatDetailRepository>(),
      ),
    );
    gh.factory<_i287.UpdateAdminRuleGroupChatDetailUseCase>(
      () => _i287.UpdateAdminRuleGroupChatDetailUseCase(
        gh<_i173.GroupChatDetailRepository>(),
      ),
    );
    gh.factory<_i288.GetUserRulesGroupChatDetailUseCase>(
      () => _i288.GetUserRulesGroupChatDetailUseCase(
        gh<_i173.GroupChatDetailRepository>(),
      ),
    );
    gh.factory<_i289.FileLoadGroupChatDetailUseCase>(
      () => _i289.FileLoadGroupChatDetailUseCase(
        gh<_i173.GroupChatDetailRepository>(),
      ),
    );
    gh.factory<_i290.GetRuleByRoleGroupChatDetailUseCase>(
      () => _i290.GetRuleByRoleGroupChatDetailUseCase(
        gh<_i173.GroupChatDetailRepository>(),
      ),
    );
    gh.factory<_i291.UpdateGroupChatDetailUseCase>(
      () => _i291.UpdateGroupChatDetailUseCase(
        gh<_i173.GroupChatDetailRepository>(),
      ),
    );
    gh.factory<_i292.GetSavedGroupChatDetailUseCase>(
      () => _i292.GetSavedGroupChatDetailUseCase(
        gh<_i173.GroupChatDetailRepository>(),
      ),
    );
    gh.factory<_i293.SaveGroupChatDetailUseCase>(
      () => _i293.SaveGroupChatDetailUseCase(
        gh<_i173.GroupChatDetailRepository>(),
      ),
    );
    gh.lazySingleton<_i294.CustomerRepository>(
      () => _i295.CustomerRepositoryImpl(
        gh<_i9.CustomerApiService>(),
        gh<_i9.EZCache>(),
      ),
    );
    gh.factory<_i296.CreateTreatmentOMDetailUseCase>(
      () => _i296.CreateTreatmentOMDetailUseCase(
        gh<_i270.ConsultationCustomerRepository>(),
      ),
    );
    gh.factory<_i297.DeleteResultOfFitUseCase>(
      () => _i297.DeleteResultOfFitUseCase(
        gh<_i270.ConsultationCustomerRepository>(),
      ),
    );
    gh.factory<_i298.GetServiceInsideTicketUseCase>(
      () => _i298.GetServiceInsideTicketUseCase(
        gh<_i270.ConsultationCustomerRepository>(),
      ),
    );
    gh.factory<_i299.GetConsultationNDTVUseCase>(
      () => _i299.GetConsultationNDTVUseCase(
        gh<_i270.ConsultationCustomerRepository>(),
      ),
    );
    gh.factory<_i300.GetResultListOfFitUseCase>(
      () => _i300.GetResultListOfFitUseCase(
        gh<_i270.ConsultationCustomerRepository>(),
      ),
    );
    gh.factory<_i301.GetSkinCustomerInfoUseCase>(
      () => _i301.GetSkinCustomerInfoUseCase(
        gh<_i270.ConsultationCustomerRepository>(),
      ),
    );
    gh.factory<_i302.RemoveConsultationCustomerUseCase>(
      () => _i302.RemoveConsultationCustomerUseCase(
        gh<_i270.ConsultationCustomerRepository>(),
      ),
    );
    gh.factory<_i303.ProductLoadConsultationCustomerUseCase>(
      () => _i303.ProductLoadConsultationCustomerUseCase(
        gh<_i270.ConsultationCustomerRepository>(),
      ),
    );
    gh.factory<_i304.CreateTreatmentDetailUseCase>(
      () => _i304.CreateTreatmentDetailUseCase(
        gh<_i270.ConsultationCustomerRepository>(),
      ),
    );
    gh.factory<_i305.GetFitCustomerInfoUseCase>(
      () => _i305.GetFitCustomerInfoUseCase(
        gh<_i270.ConsultationCustomerRepository>(),
      ),
    );
    gh.factory<_i306.GetTreatmentNoteUseCase>(
      () => _i306.GetTreatmentNoteUseCase(
        gh<_i270.ConsultationCustomerRepository>(),
      ),
    );
    gh.factory<_i307.GetServiceUsageConsultationCustomerUseCase>(
      () => _i307.GetServiceUsageConsultationCustomerUseCase(
        gh<_i270.ConsultationCustomerRepository>(),
      ),
    );
    gh.factory<_i308.GetServiceConsultationCustomerUseCase>(
      () => _i308.GetServiceConsultationCustomerUseCase(
        gh<_i270.ConsultationCustomerRepository>(),
      ),
    );
    gh.factory<_i309.GetTreatmentOMDetailUseCase>(
      () => _i309.GetTreatmentOMDetailUseCase(
        gh<_i270.ConsultationCustomerRepository>(),
      ),
    );
    gh.factory<_i310.GetResultOfFitUseCase>(
      () => _i310.GetResultOfFitUseCase(
        gh<_i270.ConsultationCustomerRepository>(),
      ),
    );
    gh.factory<_i311.GetSavedConsultationCustomerUseCase>(
      () => _i311.GetSavedConsultationCustomerUseCase(
        gh<_i270.ConsultationCustomerRepository>(),
      ),
    );
    gh.factory<_i312.UpdateSkinCustomerInfoUseCase>(
      () => _i312.UpdateSkinCustomerInfoUseCase(
        gh<_i270.ConsultationCustomerRepository>(),
      ),
    );
    gh.factory<_i313.GetConsultationCustomerUseCase>(
      () => _i313.GetConsultationCustomerUseCase(
        gh<_i270.ConsultationCustomerRepository>(),
      ),
    );
    gh.factory<_i314.UpdateFitCustomerInfoUseCase>(
      () => _i314.UpdateFitCustomerInfoUseCase(
        gh<_i270.ConsultationCustomerRepository>(),
      ),
    );
    gh.factory<_i315.SaveConsultationCustomerUseCase>(
      () => _i315.SaveConsultationCustomerUseCase(
        gh<_i270.ConsultationCustomerRepository>(),
      ),
    );
    gh.factory<_i316.UpdateTreatmentNoteUseCase>(
      () => _i316.UpdateTreatmentNoteUseCase(
        gh<_i270.ConsultationCustomerRepository>(),
      ),
    );
    gh.factory<_i317.GetActionConsultationCustomerUseCase>(
      () => _i317.GetActionConsultationCustomerUseCase(
        gh<_i270.ConsultationCustomerRepository>(),
      ),
    );
    gh.factory<_i318.UpdateResultOfFitUseCase>(
      () => _i318.UpdateResultOfFitUseCase(
        gh<_i270.ConsultationCustomerRepository>(),
      ),
    );
    gh.factory<_i319.UpdateConsultationTTBDUseCase>(
      () => _i319.UpdateConsultationTTBDUseCase(
        gh<_i270.ConsultationCustomerRepository>(),
      ),
    );
    gh.factory<_i320.CompleteConsultationCustomerUseCase>(
      () => _i320.CompleteConsultationCustomerUseCase(
        gh<_i270.ConsultationCustomerRepository>(),
      ),
    );
    gh.factory<_i321.EditServiceConsultationCustomerUseCase>(
      () => _i321.EditServiceConsultationCustomerUseCase(
        gh<_i270.ConsultationCustomerRepository>(),
      ),
    );
    gh.factory<_i322.UpdateTreatmentDetailUseCase>(
      () => _i322.UpdateTreatmentDetailUseCase(
        gh<_i270.ConsultationCustomerRepository>(),
      ),
    );
    gh.factory<_i323.RemoveServiceConsultationCustomerUseCase>(
      () => _i323.RemoveServiceConsultationCustomerUseCase(
        gh<_i270.ConsultationCustomerRepository>(),
      ),
    );
    gh.factory<_i324.GetTreatmentDetailUseCase>(
      () => _i324.GetTreatmentDetailUseCase(
        gh<_i270.ConsultationCustomerRepository>(),
      ),
    );
    gh.factory<_i325.OrderFoodUploadUseCase>(
      () => _i325.OrderFoodUploadUseCase(gh<_i198.FoodRepository>()),
    );
    gh.lazySingleton<_i326.CustomerRecordRepository>(
      () => _i327.CustomerRecordRepositoryImpl(
        gh<_i18.CustomerRecordApiService>(),
        gh<_i23.EZCache>(),
      ),
    );
    gh.factory<_i328.RemoveCreateChatFolderUseCase>(
      () => _i328.RemoveCreateChatFolderUseCase(
        gh<_i126.CreateChatFolderRepository>(),
      ),
    );
    gh.factory<_i329.GetSavedCreateChatFolderUseCase>(
      () => _i329.GetSavedCreateChatFolderUseCase(
        gh<_i126.CreateChatFolderRepository>(),
      ),
    );
    gh.factory<_i330.LoadCreateChatFolderUseCase>(
      () => _i330.LoadCreateChatFolderUseCase(
        gh<_i126.CreateChatFolderRepository>(),
      ),
    );
    gh.factory<_i331.GetCreateChatFolderUseCase>(
      () => _i331.GetCreateChatFolderUseCase(
        gh<_i126.CreateChatFolderRepository>(),
      ),
    );
    gh.factory<_i332.SaveCreateChatFolderUseCase>(
      () => _i332.SaveCreateChatFolderUseCase(
        gh<_i126.CreateChatFolderRepository>(),
      ),
    );
    gh.factory<_i333.ConversationLoadCreateChatFolderUseCase>(
      () => _i333.ConversationLoadCreateChatFolderUseCase(
        gh<_i126.CreateChatFolderRepository>(),
      ),
    );
    gh.factory<_i334.UpdateCreateChatFolderUseCase>(
      () => _i334.UpdateCreateChatFolderUseCase(
        gh<_i126.CreateChatFolderRepository>(),
      ),
    );
    gh.factory<_i335.RemoveFolderCreateChatFolderUseCase>(
      () => _i335.RemoveFolderCreateChatFolderUseCase(
        gh<_i126.CreateChatFolderRepository>(),
      ),
    );
    gh.lazySingleton<_i336.ChatSelectBranchRepository>(
      () => _i337.ChatSelectBranchRepositoryImpl(
        gh<_i18.ChatSelectBranchApiService>(),
        gh<_i23.EZCache>(),
      ),
    );
    gh.factory<_i338.UploadStickerSetUseCase>(
      () => _i338.UploadStickerSetUseCase(gh<_i186.StickerSocailRepository>()),
    );
    gh.factory<_i339.GetStickerOnlySetUseCase>(
      () => _i339.GetStickerOnlySetUseCase(gh<_i186.StickerSocailRepository>()),
    );
    gh.factory<_i340.GetStickerRecentUseCase>(
      () => _i340.GetStickerRecentUseCase(gh<_i186.StickerSocailRepository>()),
    );
    gh.factory<_i341.RemoveStickerUseCase>(
      () => _i341.RemoveStickerUseCase(gh<_i186.StickerSocailRepository>()),
    );
    gh.factory<_i342.UpdateStickerRecentUseCase>(
      () =>
          _i342.UpdateStickerRecentUseCase(gh<_i186.StickerSocailRepository>()),
    );
    gh.factory<_i343.GetStickerSetUseCase>(
      () => _i343.GetStickerSetUseCase(gh<_i186.StickerSocailRepository>()),
    );
    gh.factory<_i344.CreateStickerListUseCase>(
      () => _i344.CreateStickerListUseCase(gh<_i186.StickerSocailRepository>()),
    );
    gh.factory<_i345.UploadStickerUseCase>(
      () => _i345.UploadStickerUseCase(gh<_i186.StickerSocailRepository>()),
    );
    gh.factory<_i346.GetStickerListUseCase>(
      () => _i346.GetStickerListUseCase(gh<_i186.StickerSocailRepository>()),
    );
    gh.factory<_i347.RemoveStickerSetUseCase>(
      () => _i347.RemoveStickerSetUseCase(gh<_i186.StickerSocailRepository>()),
    );
    gh.lazySingleton<_i348.PxListRepository>(
      () => _i349.PxListRepositoryImpl(
        gh<_i18.PxListApiService>(),
        gh<_i23.EZCache>(),
      ),
    );
    gh.lazySingleton<_i350.TaskRepository>(
      () => _i351.TaskRepositoryImpl(gh<_i9.TaskApiService>()),
    );
    gh.lazySingleton<_i352.DevRepository>(
      () =>
          _i353.DevRepositoryImpl(gh<_i18.DevApiService>(), gh<_i23.EZCache>()),
    );
    gh.lazySingleton<_i354.LocationGoogleRepository>(
      () => _i355.LocationGoogleRepositoryImpl(
        gh<_i18.LocationGoogleApiService>(),
      ),
    );
    gh.factory<_i356.SaveProfileUseCase>(
      () => _i356.SaveProfileUseCase(gh<_i268.UserRepository>()),
    );
    gh.factory<_i357.SaveNavigationUseCase>(
      () => _i357.SaveNavigationUseCase(gh<_i268.UserRepository>()),
    );
    gh.factory<_i358.SaveEnableOnlineLoggerUseCase>(
      () => _i358.SaveEnableOnlineLoggerUseCase(gh<_i268.UserRepository>()),
    );
    gh.factory<_i359.SetOnboardingUseCase>(
      () => _i359.SetOnboardingUseCase(gh<_i268.UserRepository>()),
    );
    gh.factory<_i360.HasUserDataUseCase>(
      () => _i360.HasUserDataUseCase(gh<_i268.UserRepository>()),
    );
    gh.factory<_i361.PersistTokenUseCase>(
      () => _i361.PersistTokenUseCase(gh<_i268.UserRepository>()),
    );
    gh.factory<_i362.DeleteTokenUseCase>(
      () => _i362.DeleteTokenUseCase(gh<_i268.UserRepository>()),
    );
    gh.factory<_i363.ClearCacheUseCase>(
      () => _i363.ClearCacheUseCase(gh<_i268.UserRepository>()),
    );
    gh.factory<_i364.DeleteProfileUseCase>(
      () => _i364.DeleteProfileUseCase(gh<_i268.UserRepository>()),
    );
    gh.factory<_i365.SaveAccountUseCase>(
      () => _i365.SaveAccountUseCase(gh<_i268.UserRepository>()),
    );
    gh.factory<_i366.DeleteHomeMenuCacheUseCase>(
      () => _i366.DeleteHomeMenuCacheUseCase(gh<_i268.UserRepository>()),
    );
    gh.factory<_i367.CheckinUseCase>(
      () => _i367.CheckinUseCase(gh<_i268.UserRepository>()),
    );
    gh.lazySingleton<_i368.MedicalServiceListRepository>(
      () => _i369.MedicalServiceListRepositoryImpl(
        gh<_i18.MedicalServiceListApiService>(),
        gh<_i9.EZCache>(),
      ),
    );
    gh.lazySingleton<_i370.ProductConfirmRepository>(
      () => _i371.ProductConfirmRepositoryImpl(
        gh<_i18.ProductConfirmApiService>(),
      ),
    );
    gh.factory<_i372.UniversalQrScanUseCase>(
      () => _i372.UniversalQrScanUseCase(gh<_i198.FoodRepository>()),
    );
    gh.lazySingleton<_i373.DetailCrmCustomerRepository>(
      () => _i374.DetailCrmCustomerRepositoryImpl(
        gh<_i18.DetailCrmCustomerApiService>(),
        gh<_i23.EZCache>(),
      ),
    );
    gh.lazySingleton<_i375.TakingCareCustomerRepository>(
      () => _i376.TakingCareCustomerRepositoryImpl(
        gh<_i18.TakingCareCustomerApiService>(),
        gh<_i23.EZCache>(),
        gh<_i144.MediaUploadApiService>(),
      ),
    );
    gh.factory<_i377.GetSavedMedicalDepartmentListUseCase>(
      () => _i377.GetSavedMedicalDepartmentListUseCase(
        gh<_i204.MedicalDepartmentListRepository>(),
      ),
    );
    gh.factory<_i378.GetMedicalDepartmentListUseCase>(
      () => _i378.GetMedicalDepartmentListUseCase(
        gh<_i204.MedicalDepartmentListRepository>(),
      ),
    );
    gh.factory<_i379.SaveMedicalDepartmentListUseCase>(
      () => _i379.SaveMedicalDepartmentListUseCase(
        gh<_i204.MedicalDepartmentListRepository>(),
      ),
    );
    gh.factory<_i380.RemoveMedicalDepartmentListUseCase>(
      () => _i380.RemoveMedicalDepartmentListUseCase(
        gh<_i204.MedicalDepartmentListRepository>(),
      ),
    );
    gh.lazySingleton<_i381.MediaUploadRepository>(
      () => _i382.MediaUploadRepositoryImpl(gh<_i144.MediaUploadApiService>()),
    );
    gh.lazySingleton<_i383.CustomerBookingInfoRepository>(
      () => _i384.CustomerBookingInfoRepositoryImpl(
        gh<_i18.CustomerBookingInfoApiService>(),
        gh<_i23.EZCache>(),
      ),
    );
    gh.lazySingleton<_i385.CustomerListRepository>(
      () => _i386.CustomerListRepositoryImpl(
        gh<_i18.CustomerListApiService>(),
        gh<_i23.EZCache>(),
      ),
    );
    gh.lazySingleton<_i387.PxRecheckRepository>(
      () => _i388.PxRecheckRepositoryImpl(
        gh<_i18.PxRecheckApiService>(),
        gh<_i23.EZCache>(),
      ),
    );
    gh.lazySingleton<_i389.TicketDetailRepository>(
      () => _i390.TicketDetailRepositoryImpl(
        gh<_i18.TicketDetailApiService>(),
        gh<_i23.EZCache>(),
      ),
    );
    gh.factory<_i391.PostLikeStoryUseCase>(
      () => _i391.PostLikeStoryUseCase(gh<_i196.LikeListRepository>()),
    );
    gh.factory<_i392.PostLikeCommentUseCase>(
      () => _i392.PostLikeCommentUseCase(gh<_i196.LikeListRepository>()),
    );
    gh.factory<_i393.GetLikeListUseCase>(
      () => _i393.GetLikeListUseCase(gh<_i196.LikeListRepository>()),
    );
    gh.lazySingleton<_i394.NewsRepository>(
      () => _i395.NewsRepositoryImpl(gh<_i9.NewsApiService>()),
    );
    gh.lazySingleton<_i396.CreateCustomerRepository>(
      () => _i397.CreateCustomerRepositoryImpl(
        gh<_i18.CreateCustomerApiService>(),
        gh<_i23.EZCache>(),
      ),
    );
    gh.lazySingleton<_i398.CheckinRepository>(
      () => _i399.CheckinRepositoryImpl(gh<_i9.CheckinApiService>()),
    );
    gh.factory<_i400.CreatingTaskUseCase>(
      () => _i400.CreatingTaskUseCase(gh<_i350.TaskRepository>()),
    );
    gh.factory<_i401.GetRepeatTaskUseCase>(
      () => _i401.GetRepeatTaskUseCase(gh<_i350.TaskRepository>()),
    );
    gh.factory<_i402.GetJobSchedulerUseCase>(
      () => _i402.GetJobSchedulerUseCase(gh<_i350.TaskRepository>()),
    );
    gh.factory<_i403.SubmitJobSchedulerUseCase>(
      () => _i403.SubmitJobSchedulerUseCase(gh<_i350.TaskRepository>()),
    );
    gh.factory<_i404.GetGeneralJobSchedulerUseCase>(
      () => _i404.GetGeneralJobSchedulerUseCase(gh<_i350.TaskRepository>()),
    );
    gh.factory<_i405.GetDetailJobSchedulerUseCase>(
      () => _i405.GetDetailJobSchedulerUseCase(gh<_i350.TaskRepository>()),
    );
    gh.lazySingleton<_i406.CustomerProfileRepository>(
      () => _i407.CustomerProfileRepositoryImpl(
        gh<_i9.CustomerProfileApiService>(),
        gh<_i9.EZCache>(),
      ),
    );
    gh.factory<_i408.GetStoryPersonListUseCase>(
      () => _i408.GetStoryPersonListUseCase(
        gh<_i169.StoryPersonListRepository>(),
      ),
    );
    gh.factory<_i409.GetStoryPersonListUserUseCase>(
      () => _i409.GetStoryPersonListUserUseCase(
        gh<_i169.StoryPersonListRepository>(),
      ),
    );
    gh.factory<_i410.SaveFontOptionUseCase>(
      () => _i410.SaveFontOptionUseCase(gh<_i142.SettingRepository>()),
    );
    gh.factory<_i411.GetLanguageOptionUseCase>(
      () => _i411.GetLanguageOptionUseCase(gh<_i142.SettingRepository>()),
    );
    gh.factory<_i412.GetThemeOptionUseCase>(
      () => _i412.GetThemeOptionUseCase(gh<_i142.SettingRepository>()),
    );
    gh.factory<_i413.SaveLanguageOptionUseCase>(
      () => _i413.SaveLanguageOptionUseCase(gh<_i142.SettingRepository>()),
    );
    gh.factory<_i414.SaveThemeOptionUseCase>(
      () => _i414.SaveThemeOptionUseCase(gh<_i142.SettingRepository>()),
    );
    gh.factory<_i415.IsDarkModeUseCase>(
      () => _i415.IsDarkModeUseCase(gh<_i142.SettingRepository>()),
    );
    gh.factory<_i416.GetFontOptionUseCase>(
      () => _i416.GetFontOptionUseCase(gh<_i142.SettingRepository>()),
    );
    gh.factory<_i417.IsLightModeUseCase>(
      () => _i417.IsLightModeUseCase(gh<_i142.SettingRepository>()),
    );
    gh.lazySingleton<_i418.NoteDetailsRepository>(
      () => _i419.NoteDetailsRepositoryImpl(
        gh<_i18.NoteDetailsApiService>(),
        gh<_i9.EZCache>(),
      ),
    );
    gh.factory<_i420.RepeatTaskBloc>(
      () => _i420.RepeatTaskBloc(gh<_i401.GetRepeatTaskUseCase>()),
    );
    gh.lazySingleton<_i421.BranchChatListRepository>(
      () => _i422.BranchChatListRepositoryImpl(
        gh<_i18.BranchChatListApiService>(),
        gh<_i23.EZCache>(),
      ),
    );
    gh.factory<_i423.GetSelectPxRoomUseCase>(
      () => _i423.GetSelectPxRoomUseCase(gh<_i194.SelectPxRoomRepository>()),
    );
    gh.factory<_i424.RemoveSelectPxRoomUseCase>(
      () => _i424.RemoveSelectPxRoomUseCase(gh<_i194.SelectPxRoomRepository>()),
    );
    gh.factory<_i425.RoomChangeSelectPxRoomUseCase>(
      () => _i425.RoomChangeSelectPxRoomUseCase(
        gh<_i194.SelectPxRoomRepository>(),
      ),
    );
    gh.factory<_i426.GetSavedSelectPxRoomUseCase>(
      () =>
          _i426.GetSavedSelectPxRoomUseCase(gh<_i194.SelectPxRoomRepository>()),
    );
    gh.factory<_i427.SaveSelectPxRoomUseCase>(
      () => _i427.SaveSelectPxRoomUseCase(gh<_i194.SelectPxRoomRepository>()),
    );
    gh.lazySingleton<_i428.ListCustomerRepository>(
      () => _i429.ListCustomerRepositoryImpl(
        gh<_i9.ListCustomerApiService>(),
        gh<_i9.EZCache>(),
      ),
    );
    gh.factory<_i430.ChatBloc>(
      () => _i430.ChatBloc(
        gh<_i232.GetChatUseCase>(),
        gh<_i221.SaveChatUseCase>(),
        gh<_i214.GetSavedChatUseCase>(),
        gh<_i234.RemoveChatUseCase>(),
        gh<_i229.SendChatUseCase>(),
        gh<_i224.UploadFileChatUseCase>(),
        gh<_i235.ConversationDetailsUpdateChatUseCase>(),
        gh<_i227.ReactChatUseCase>(),
        gh<_i228.GetConversationByIdUseCase>(),
        gh<_i233.MessageRemoveChatUseCase>(),
        gh<_i215.MessageEditChatUseCase>(),
        gh<_i219.GetConversationChatUseCase>(),
        gh<_i230.PinMessageChatUseCase>(),
        gh<_i225.UnpinMessageChatUseCase>(),
        gh<_i220.GetPinListChatUseCase>(),
        gh<_i231.UpdatePollChatUseCase>(),
        gh<_i222.VotePollChatUseCase>(),
        gh<_i216.GetUserSeenChatUseCase>(),
        gh<_i218.SearchChatUseCase>(),
        gh<_i217.TranscribeChatUseCase>(),
        gh<_i226.ReplyBotMessageChatUseCase>(),
        gh<_i223.GetUserStickerChatUseCase>(),
        gh<_i288.GetUserRulesGroupChatDetailUseCase>(),
      ),
    );
    gh.lazySingleton<_i431.ScheduleDetailsRepository>(
      () => _i432.ScheduleDetailsRepositoryImpl(
        gh<_i433.ScheduleDetailsApiService>(),
        gh<_i9.EZCache>(),
      ),
    );
    gh.factory<_i434.TicketCreatedTypeUseCase>(
      () => _i434.TicketCreatedTypeUseCase(gh<_i258.TicketRepository>()),
    );
    gh.factory<_i435.GetTicketAllGroupUseCase>(
      () => _i435.GetTicketAllGroupUseCase(gh<_i258.TicketRepository>()),
    );
    gh.factory<_i436.CreateTicketUseCase>(
      () => _i436.CreateTicketUseCase(gh<_i258.TicketRepository>()),
    );
    gh.factory<_i437.GetTicketTicketv2UseCase>(
      () => _i437.GetTicketTicketv2UseCase(gh<_i258.TicketRepository>()),
    );
    gh.factory<_i438.TicketUploadFileUseCase>(
      () => _i438.TicketUploadFileUseCase(gh<_i258.TicketRepository>()),
    );
    gh.factory<_i439.GetMyTicketTicketv2UseCase>(
      () => _i439.GetMyTicketTicketv2UseCase(gh<_i258.TicketRepository>()),
    );
    gh.factory<_i440.UpdateTicketUseCase>(
      () => _i440.UpdateTicketUseCase(gh<_i258.TicketRepository>()),
    );
    gh.factory<_i441.GetTicketAllTypeUseCase>(
      () => _i441.GetTicketAllTypeUseCase(gh<_i258.TicketRepository>()),
    );
    gh.factory<_i442.TicketGroupTypeUseCase>(
      () => _i442.TicketGroupTypeUseCase(gh<_i258.TicketRepository>()),
    );
    gh.lazySingleton<_i443.StaffEvaluationPeriodsRepository>(
      () => _i444.StaffEvaluationPeriodsRepositoryImpl(
        gh<_i18.StaffEvaluationPeriodsApiService>(),
        gh<_i23.EZCache>(),
      ),
    );
    gh.lazySingleton<_i445.PxUnasignedRepository>(
      () => _i446.PxUnasignedRepositoryImpl(
        gh<_i18.PxUnasignedApiService>(),
        gh<_i23.EZCache>(),
      ),
    );
    gh.factory<_i447.GetDropDownStatusUseCase>(
      () => _i447.GetDropDownStatusUseCase(gh<_i210.UserTickerRepository>()),
    );
    gh.factory<_i448.GetUserTicketUseCase>(
      () => _i448.GetUserTicketUseCase(gh<_i210.UserTickerRepository>()),
    );
    gh.lazySingleton<_i449.StaffRepository>(
      () => _i450.StaffRepositoryImpl(gh<_i9.StaffApiService>()),
    );
    gh.factory<_i451.RemoveCustomerRecordUseCase>(
      () => _i451.RemoveCustomerRecordUseCase(
        gh<_i326.CustomerRecordRepository>(),
      ),
    );
    gh.factory<_i452.GetSavedCustomerRecordUseCase>(
      () => _i452.GetSavedCustomerRecordUseCase(
        gh<_i326.CustomerRecordRepository>(),
      ),
    );
    gh.factory<_i453.GetCustomerRecordUseCase>(
      () =>
          _i453.GetCustomerRecordUseCase(gh<_i326.CustomerRecordRepository>()),
    );
    gh.factory<_i454.SaveCustomerRecordUseCase>(
      () =>
          _i454.SaveCustomerRecordUseCase(gh<_i326.CustomerRecordRepository>()),
    );
    gh.lazySingleton<_i455.MedicalProductCreationRepository>(
      () => _i456.MedicalProductCreationRepositoryImpl(
        gh<_i18.MedicalProductCreationApiService>(),
        gh<_i9.EZCache>(),
      ),
    );
    gh.lazySingleton<_i457.HomeRepository>(
      () => _i458.HomeRepositoryImpl(gh<_i9.HomeApiService>()),
    );
    gh.factory<_i459.GetEmojiListUseCase>(
      () => _i459.GetEmojiListUseCase(gh<_i171.StoryListRepository>()),
    );
    gh.factory<_i460.GetStoryListUseCase>(
      () => _i460.GetStoryListUseCase(gh<_i171.StoryListRepository>()),
    );
    gh.factory<_i461.GetStoryListSearchUseCase>(
      () => _i461.GetStoryListSearchUseCase(gh<_i171.StoryListRepository>()),
    );
    gh.factory<_i462.PostStoryUseCase>(
      () => _i462.PostStoryUseCase(gh<_i171.StoryListRepository>()),
    );
    gh.factory<_i463.PutStoryVoteUseCase>(
      () => _i463.PutStoryVoteUseCase(gh<_i171.StoryListRepository>()),
    );
    gh.factory<_i464.GetTotalNotificationUseCase>(
      () => _i464.GetTotalNotificationUseCase(gh<_i171.StoryListRepository>()),
    );
    gh.factory<_i465.UpdateStoryUseCase>(
      () => _i465.UpdateStoryUseCase(gh<_i171.StoryListRepository>()),
    );
    gh.factory<_i466.SocialUploadFileUseCase>(
      () => _i466.SocialUploadFileUseCase(gh<_i171.StoryListRepository>()),
    );
    gh.factory<_i467.GetVoteUsersUseCase>(
      () => _i467.GetVoteUsersUseCase(gh<_i171.StoryListRepository>()),
    );
    gh.factory<_i468.DeleteStoryVoteUseCase>(
      () => _i468.DeleteStoryVoteUseCase(gh<_i171.StoryListRepository>()),
    );
    gh.factory<_i469.DeleteStoryUseCase>(
      () => _i469.DeleteStoryUseCase(gh<_i171.StoryListRepository>()),
    );
    gh.factory<_i470.GetStoryRuleUseCase>(
      () => _i470.GetStoryRuleUseCase(gh<_i171.StoryListRepository>()),
    );
    gh.lazySingleton<_i471.CommentListRepository>(
      () => _i472.CommentListRepositoryImpl(
        gh<_i15.CommentListApiService>(),
        gh<_i146.SocialApiService>(),
      ),
    );
    gh.factory<_i473.GetSavedMedicalServiceListUseCase>(
      () => _i473.GetSavedMedicalServiceListUseCase(
        gh<_i368.MedicalServiceListRepository>(),
      ),
    );
    gh.factory<_i474.SaveMedicalServiceListUseCase>(
      () => _i474.SaveMedicalServiceListUseCase(
        gh<_i368.MedicalServiceListRepository>(),
      ),
    );
    gh.factory<_i475.RemoveMedicalServiceListUseCase>(
      () => _i475.RemoveMedicalServiceListUseCase(
        gh<_i368.MedicalServiceListRepository>(),
      ),
    );
    gh.factory<_i476.GetMedicalServiceListUseCase>(
      () => _i476.GetMedicalServiceListUseCase(
        gh<_i368.MedicalServiceListRepository>(),
      ),
    );
    gh.factory<_i477.CreateChatGroupBloc>(
      () => _i477.CreateChatGroupBloc(
        gh<_i184.GetCreateChatGroupUseCase>(),
        gh<_i182.SaveCreateChatGroupUseCase>(),
        gh<_i181.GetSavedCreateChatGroupUseCase>(),
        gh<_i185.RemoveCreateChatGroupUseCase>(),
        gh<_i183.UserLoadCreateChatGroupUseCase>(),
        gh<_i224.UploadFileChatUseCase>(),
      ),
    );
    gh.factory<_i478.GetSavedChatSelectBranchUseCase>(
      () => _i478.GetSavedChatSelectBranchUseCase(
        gh<_i336.ChatSelectBranchRepository>(),
      ),
    );
    gh.factory<_i479.RemoveChatSelectBranchUseCase>(
      () => _i479.RemoveChatSelectBranchUseCase(
        gh<_i336.ChatSelectBranchRepository>(),
      ),
    );
    gh.factory<_i480.SaveChatSelectBranchUseCase>(
      () => _i480.SaveChatSelectBranchUseCase(
        gh<_i336.ChatSelectBranchRepository>(),
      ),
    );
    gh.factory<_i481.GetChatSelectBranchUseCase>(
      () => _i481.GetChatSelectBranchUseCase(
        gh<_i336.ChatSelectBranchRepository>(),
      ),
    );
    gh.factory<_i482.SaveDetailStaffEvaluationPeriodUseCase>(
      () => _i482.SaveDetailStaffEvaluationPeriodUseCase(
        gh<_i206.DetailStaffEvaluationPeriodRepository>(),
      ),
    );
    gh.factory<_i483.EmployeeFetchDetailStaffEvaluationPeriodUseCase>(
      () => _i483.EmployeeFetchDetailStaffEvaluationPeriodUseCase(
        gh<_i206.DetailStaffEvaluationPeriodRepository>(),
      ),
    );
    gh.factory<_i484.RemoveDetailStaffEvaluationPeriodUseCase>(
      () => _i484.RemoveDetailStaffEvaluationPeriodUseCase(
        gh<_i206.DetailStaffEvaluationPeriodRepository>(),
      ),
    );
    gh.factory<_i485.GetSavedDetailStaffEvaluationPeriodUseCase>(
      () => _i485.GetSavedDetailStaffEvaluationPeriodUseCase(
        gh<_i206.DetailStaffEvaluationPeriodRepository>(),
      ),
    );
    gh.factory<_i486.GetDetailStaffEvaluationPeriodUseCase>(
      () => _i486.GetDetailStaffEvaluationPeriodUseCase(
        gh<_i206.DetailStaffEvaluationPeriodRepository>(),
      ),
    );
    gh.factory<_i487.FetchHistoryCheckinUseCase>(
      () => _i487.FetchHistoryCheckinUseCase(gh<_i398.CheckinRepository>()),
    );
    gh.lazySingleton<_i488.NotificationRepository>(
      () => _i489.NotificationRepositoryImpl(gh<_i9.NotificationApiService>()),
    );
    gh.lazySingleton<_i490.EformRepository>(
      () => _i491.EformRepositoryImpl(gh<_i9.EformApiService>()),
    );
    gh.factory<_i492.MedicalServiceListBloc>(
      () => _i492.MedicalServiceListBloc(
        gh<_i476.GetMedicalServiceListUseCase>(),
        gh<_i474.SaveMedicalServiceListUseCase>(),
        gh<_i473.GetSavedMedicalServiceListUseCase>(),
        gh<_i475.RemoveMedicalServiceListUseCase>(),
      ),
    );
    gh.factory<_i493.DetailJobSchedulerBloc>(
      () => _i493.DetailJobSchedulerBloc(
        gh<_i405.GetDetailJobSchedulerUseCase>(),
        gh<_i403.SubmitJobSchedulerUseCase>(),
      ),
    );
    gh.factory<_i494.FontsBloc>(
      () => _i494.FontsBloc(
        gh<_i416.GetFontOptionUseCase>(),
        gh<_i410.SaveFontOptionUseCase>(),
        gh<_i246.CacheTextScaleGetUseCase>(),
        gh<_i237.CacheTextScaleSaveUseCase>(),
      ),
    );
    gh.factory<_i495.StoryPersonListBloc>(
      () => _i495.StoryPersonListBloc(
        gh<_i408.GetStoryPersonListUseCase>(),
        gh<_i469.DeleteStoryUseCase>(),
        gh<_i391.PostLikeStoryUseCase>(),
        gh<_i462.PostStoryUseCase>(),
        gh<_i465.UpdateStoryUseCase>(),
        gh<_i409.GetStoryPersonListUserUseCase>(),
      ),
    );
    gh.factory<_i496.NotificationListBloc>(
      () => _i496.NotificationListBloc(
        gh<_i263.GetNotificationListUseCase>(),
        gh<_i266.SaveNotificationListUseCase>(),
        gh<_i267.GetSavedNotificationListUseCase>(),
        gh<_i262.RemoveNotificationListUseCase>(),
        gh<_i264.PutReadAllSocialUseCase>(),
        gh<_i265.DeleteNotificationSocialUseCase>(),
      ),
    );
    gh.factory<_i497.GetSavedImportantNotesUseCase>(
      () => _i497.GetSavedImportantNotesUseCase(
        gh<_i208.ImportantNotesRepository>(),
      ),
    );
    gh.factory<_i498.SaveImportantNotesUseCase>(
      () =>
          _i498.SaveImportantNotesUseCase(gh<_i208.ImportantNotesRepository>()),
    );
    gh.factory<_i499.GetNoteCategoryImportantNotesUseCase>(
      () => _i499.GetNoteCategoryImportantNotesUseCase(
        gh<_i208.ImportantNotesRepository>(),
      ),
    );
    gh.factory<_i500.GetImportantNotesUseCase>(
      () =>
          _i500.GetImportantNotesUseCase(gh<_i208.ImportantNotesRepository>()),
    );
    gh.factory<_i501.RemoveImportantNotesUseCase>(
      () => _i501.RemoveImportantNotesUseCase(
        gh<_i208.ImportantNotesRepository>(),
      ),
    );
    gh.lazySingleton<_i502.ServiceAndProductRepository>(
      () => _i503.ServiceAndProductRepositoryImpl(
        gh<_i18.ServiceAndProductApiService>(),
        gh<_i9.EZCache>(),
      ),
    );
    gh.factory<_i504.RemoveStaffEvaluationPeriodsUseCase>(
      () => _i504.RemoveStaffEvaluationPeriodsUseCase(
        gh<_i443.StaffEvaluationPeriodsRepository>(),
      ),
    );
    gh.factory<_i505.SaveStaffEvaluationPeriodsUseCase>(
      () => _i505.SaveStaffEvaluationPeriodsUseCase(
        gh<_i443.StaffEvaluationPeriodsRepository>(),
      ),
    );
    gh.factory<_i506.GetSavedStaffEvaluationPeriodsUseCase>(
      () => _i506.GetSavedStaffEvaluationPeriodsUseCase(
        gh<_i443.StaffEvaluationPeriodsRepository>(),
      ),
    );
    gh.factory<_i507.GetStaffEvaluationPeriodsUseCase>(
      () => _i507.GetStaffEvaluationPeriodsUseCase(
        gh<_i443.StaffEvaluationPeriodsRepository>(),
      ),
    );
    gh.factory<_i508.GetCustomerInfoByQrUseCase>(
      () => _i508.GetCustomerInfoByQrUseCase(gh<_i294.CustomerRepository>()),
    );
    gh.factory<_i509.GetCustomerInfoUseCase>(
      () => _i509.GetCustomerInfoUseCase(gh<_i294.CustomerRepository>()),
    );
    gh.factory<_i510.GetCustomerRoomCodeUseCase>(
      () => _i510.GetCustomerRoomCodeUseCase(gh<_i294.CustomerRepository>()),
    );
    gh.lazySingleton<_i511.MedicalLogDetailRepository>(
      () => _i512.MedicalLogDetailRepositoryImpl(
        gh<_i18.MedicalLogDetailApiService>(),
        gh<_i9.EZCache>(),
      ),
    );
    gh.factory<_i513.ProductsMedicalProductCreationUseCase>(
      () => _i513.ProductsMedicalProductCreationUseCase(
        gh<_i455.MedicalProductCreationRepository>(),
      ),
    );
    gh.factory<_i514.GetSavedMedicalProductCreationUseCase>(
      () => _i514.GetSavedMedicalProductCreationUseCase(
        gh<_i455.MedicalProductCreationRepository>(),
      ),
    );
    gh.factory<_i515.RemoveMedicalProductCreationUseCase>(
      () => _i515.RemoveMedicalProductCreationUseCase(
        gh<_i455.MedicalProductCreationRepository>(),
      ),
    );
    gh.factory<_i516.SaveMedicalProductCreationUseCase>(
      () => _i516.SaveMedicalProductCreationUseCase(
        gh<_i455.MedicalProductCreationRepository>(),
      ),
    );
    gh.factory<_i517.MedicalProductCreationUseCase>(
      () => _i517.MedicalProductCreationUseCase(
        gh<_i455.MedicalProductCreationRepository>(),
      ),
    );
    gh.lazySingleton<_i518.CustomerScheduleRepository>(
      () => _i519.CustomerScheduleRepositoryImpl(
        gh<_i18.CustomerScheduleApiService>(),
        gh<_i9.EZCache>(),
      ),
    );
    gh.lazySingleton<_i520.KpiEmployeeRepository>(
      () => _i521.KpiEmployeeRepositoryImpl(gh<_i18.KpiEmployeeApiService>()),
    );
    gh.factory<_i522.RemovePxTaskListUseCase>(
      () => _i522.RemovePxTaskListUseCase(gh<_i256.PxTaskListRepository>()),
    );
    gh.factory<_i523.GetSavedPxTaskListUseCase>(
      () => _i523.GetSavedPxTaskListUseCase(gh<_i256.PxTaskListRepository>()),
    );
    gh.factory<_i524.SavePxTaskListUseCase>(
      () => _i524.SavePxTaskListUseCase(gh<_i256.PxTaskListRepository>()),
    );
    gh.factory<_i525.GetPxTaskListUseCase>(
      () => _i525.GetPxTaskListUseCase(gh<_i256.PxTaskListRepository>()),
    );
    gh.factory<_i526.ChatSelectBranchBloc>(
      () => _i526.ChatSelectBranchBloc(
        gh<_i481.GetChatSelectBranchUseCase>(),
        gh<_i480.SaveChatSelectBranchUseCase>(),
        gh<_i479.RemoveChatSelectBranchUseCase>(),
      ),
    );
    gh.factory<_i527.OrderFoodDeleteUseCase>(
      () => _i527.OrderFoodDeleteUseCase(gh<_i198.FoodRepository>()),
    );
    gh.factory<_i528.OrderFoodGetUseCase>(
      () => _i528.OrderFoodGetUseCase(gh<_i198.FoodRepository>()),
    );
    gh.factory<_i529.OrderFoodCreatedUseCase>(
      () => _i529.OrderFoodCreatedUseCase(gh<_i198.FoodRepository>()),
    );
    gh.factory<_i530.OrderFoodCreateReportUseCase>(
      () => _i530.OrderFoodCreateReportUseCase(gh<_i198.FoodRepository>()),
    );
    gh.factory<_i531.SetDefaultAddressFoodUseCase>(
      () => _i531.SetDefaultAddressFoodUseCase(gh<_i198.FoodRepository>()),
    );
    gh.factory<_i532.RoomLoadDetailCrmCustomerUseCase>(
      () => _i532.RoomLoadDetailCrmCustomerUseCase(
        gh<_i373.DetailCrmCustomerRepository>(),
      ),
    );
    gh.factory<_i533.PromotionLoadDetailCrmCustomerUseCase>(
      () => _i533.PromotionLoadDetailCrmCustomerUseCase(
        gh<_i373.DetailCrmCustomerRepository>(),
      ),
    );
    gh.factory<_i534.ServiceFetchDetailCrmCustomerUseCase>(
      () => _i534.ServiceFetchDetailCrmCustomerUseCase(
        gh<_i373.DetailCrmCustomerRepository>(),
      ),
    );
    gh.factory<_i535.AdviceFetchDetailCrmCustomerUseCase>(
      () => _i535.AdviceFetchDetailCrmCustomerUseCase(
        gh<_i373.DetailCrmCustomerRepository>(),
      ),
    );
    gh.factory<_i536.GetDetailCrmCustomerUseCase>(
      () => _i536.GetDetailCrmCustomerUseCase(
        gh<_i373.DetailCrmCustomerRepository>(),
      ),
    );
    gh.factory<_i537.SaveDetailCrmCustomerUseCase>(
      () => _i537.SaveDetailCrmCustomerUseCase(
        gh<_i373.DetailCrmCustomerRepository>(),
      ),
    );
    gh.factory<_i538.ServiceLoadDetailCrmCustomerUseCase>(
      () => _i538.ServiceLoadDetailCrmCustomerUseCase(
        gh<_i373.DetailCrmCustomerRepository>(),
      ),
    );
    gh.factory<_i539.RemoveDetailCrmCustomerUseCase>(
      () => _i539.RemoveDetailCrmCustomerUseCase(
        gh<_i373.DetailCrmCustomerRepository>(),
      ),
    );
    gh.factory<_i540.CallLogFetchDetailCrmCustomerUseCase>(
      () => _i540.CallLogFetchDetailCrmCustomerUseCase(
        gh<_i373.DetailCrmCustomerRepository>(),
      ),
    );
    gh.factory<_i541.BranchLoadDetailCrmCustomerUseCase>(
      () => _i541.BranchLoadDetailCrmCustomerUseCase(
        gh<_i373.DetailCrmCustomerRepository>(),
      ),
    );
    gh.factory<_i542.GetSavedDetailCrmCustomerUseCase>(
      () => _i542.GetSavedDetailCrmCustomerUseCase(
        gh<_i373.DetailCrmCustomerRepository>(),
      ),
    );
    gh.factory<_i543.BookingLogFetchDetailCrmCustomerUseCase>(
      () => _i543.BookingLogFetchDetailCrmCustomerUseCase(
        gh<_i373.DetailCrmCustomerRepository>(),
      ),
    );
    gh.factory<_i544.BookDetailCrmCustomerUseCase>(
      () => _i544.BookDetailCrmCustomerUseCase(
        gh<_i373.DetailCrmCustomerRepository>(),
      ),
    );
    gh.factory<_i545.TimeLoadDetailCrmCustomerUseCase>(
      () => _i545.TimeLoadDetailCrmCustomerUseCase(
        gh<_i373.DetailCrmCustomerRepository>(),
      ),
    );
    gh.factory<_i546.AdviceUpdateDetailCrmCustomerUseCase>(
      () => _i546.AdviceUpdateDetailCrmCustomerUseCase(
        gh<_i373.DetailCrmCustomerRepository>(),
      ),
    );
    gh.factory<_i547.AdviceTypeFetchDetailCrmCustomerUseCase>(
      () => _i547.AdviceTypeFetchDetailCrmCustomerUseCase(
        gh<_i373.DetailCrmCustomerRepository>(),
      ),
    );
    gh.factory<_i548.BookingDetailLoadDetailCrmCustomerUseCase>(
      () => _i548.BookingDetailLoadDetailCrmCustomerUseCase(
        gh<_i373.DetailCrmCustomerRepository>(),
      ),
    );
    gh.factory<_i549.MessageLogFetchDetailCrmCustomerUseCase>(
      () => _i549.MessageLogFetchDetailCrmCustomerUseCase(
        gh<_i373.DetailCrmCustomerRepository>(),
      ),
    );
    gh.factory<_i550.NumberBookingLoadDetailCrmCustomerUseCase>(
      () => _i550.NumberBookingLoadDetailCrmCustomerUseCase(
        gh<_i373.DetailCrmCustomerRepository>(),
      ),
    );
    gh.factory<_i551.BookingLoadDetailCrmCustomerUseCase>(
      () => _i551.BookingLoadDetailCrmCustomerUseCase(
        gh<_i373.DetailCrmCustomerRepository>(),
      ),
    );
    gh.lazySingleton<_i552.PxUnasignedUpdateRepository>(
      () => _i553.PxUnasignedUpdateRepositoryImpl(
        gh<_i18.PxUnasignedUpdateApiService>(),
        gh<_i23.EZCache>(),
      ),
    );
    gh.lazySingleton<_i554.UserListRepository>(
      () => _i555.UserListRepositoryImpl(
        gh<_i18.UserListApiService>(),
        gh<_i23.EZCache>(),
      ),
    );
    gh.factory<_i556.GetServicesUseCase>(
      () => _i556.GetServicesUseCase(gh<_i457.HomeRepository>()),
    );
    gh.factory<_i557.PopupInfoUseCase>(
      () => _i557.PopupInfoUseCase(gh<_i457.HomeRepository>()),
    );
    gh.factory<_i558.CreateChatFolderBloc>(
      () => _i558.CreateChatFolderBloc(
        gh<_i331.GetCreateChatFolderUseCase>(),
        gh<_i332.SaveCreateChatFolderUseCase>(),
        gh<_i329.GetSavedCreateChatFolderUseCase>(),
        gh<_i328.RemoveCreateChatFolderUseCase>(),
        gh<_i330.LoadCreateChatFolderUseCase>(),
        gh<_i335.RemoveFolderCreateChatFolderUseCase>(),
        gh<_i333.ConversationLoadCreateChatFolderUseCase>(),
        gh<_i334.UpdateCreateChatFolderUseCase>(),
      ),
    );
    gh.factory<_i559.SaveTicketDetailUseCase>(
      () => _i559.SaveTicketDetailUseCase(gh<_i389.TicketDetailRepository>()),
    );
    gh.factory<_i560.GetTicketDetailUseCase>(
      () => _i560.GetTicketDetailUseCase(gh<_i389.TicketDetailRepository>()),
    );
    gh.factory<_i561.GetTicketDetailReasonUseCase>(
      () => _i561.GetTicketDetailReasonUseCase(
        gh<_i389.TicketDetailRepository>(),
      ),
    );
    gh.factory<_i562.GetSavedTicketDetailUseCase>(
      () =>
          _i562.GetSavedTicketDetailUseCase(gh<_i389.TicketDetailRepository>()),
    );
    gh.factory<_i563.ReceptTicketDetailUseCase>(
      () => _i563.ReceptTicketDetailUseCase(gh<_i389.TicketDetailRepository>()),
    );
    gh.factory<_i564.TicketDetailReworkUseCase>(
      () => _i564.TicketDetailReworkUseCase(gh<_i389.TicketDetailRepository>()),
    );
    gh.factory<_i565.ConfirmTicketDetailUseCase>(
      () =>
          _i565.ConfirmTicketDetailUseCase(gh<_i389.TicketDetailRepository>()),
    );
    gh.factory<_i566.RemoveTicketDetailUseCase>(
      () => _i566.RemoveTicketDetailUseCase(gh<_i389.TicketDetailRepository>()),
    );
    gh.lazySingleton<_i567.CustomerInfoDetailsRepository>(
      () => _i568.CustomerInfoDetailsRepositoryImpl(
        gh<_i9.CustomerInfoDetailsApiService>(),
        gh<_i9.EZCache>(),
      ),
    );
    gh.lazySingleton<_i569.HrOrganizationRepository>(
      () => _i570.HrOrganizationRepositoryImpl(
        gh<_i18.HrOrganizationApiService>(),
        gh<_i23.EZCache>(),
      ),
    );
    gh.factory<_i571.RemoveScheduleDetailsUseCase>(
      () => _i571.RemoveScheduleDetailsUseCase(
        gh<_i431.ScheduleDetailsRepository>(),
      ),
    );
    gh.factory<_i572.SaveScheduleDetailsUseCase>(
      () => _i572.SaveScheduleDetailsUseCase(
        gh<_i431.ScheduleDetailsRepository>(),
      ),
    );
    gh.factory<_i573.GetSavedScheduleDetailsUseCase>(
      () => _i573.GetSavedScheduleDetailsUseCase(
        gh<_i431.ScheduleDetailsRepository>(),
      ),
    );
    gh.factory<_i574.GetScheduleDetailsUseCase>(
      () => _i574.GetScheduleDetailsUseCase(
        gh<_i431.ScheduleDetailsRepository>(),
      ),
    );
    gh.factory<_i575.GetProvinceCreateCustomerUseCase>(
      () => _i575.GetProvinceCreateCustomerUseCase(
        gh<_i396.CreateCustomerRepository>(),
      ),
    );
    gh.factory<_i576.GetJobCreateCustomerUseCase>(
      () => _i576.GetJobCreateCustomerUseCase(
        gh<_i396.CreateCustomerRepository>(),
      ),
    );
    gh.factory<_i577.RemoveCreateCustomerUseCase>(
      () => _i577.RemoveCreateCustomerUseCase(
        gh<_i396.CreateCustomerRepository>(),
      ),
    );
    gh.factory<_i578.SaveCreateCustomerUseCase>(
      () =>
          _i578.SaveCreateCustomerUseCase(gh<_i396.CreateCustomerRepository>()),
    );
    gh.factory<_i579.GetDistrictCreateCustomerUseCase>(
      () => _i579.GetDistrictCreateCustomerUseCase(
        gh<_i396.CreateCustomerRepository>(),
      ),
    );
    gh.factory<_i580.GetWardCreateCustomerUseCase>(
      () => _i580.GetWardCreateCustomerUseCase(
        gh<_i396.CreateCustomerRepository>(),
      ),
    );
    gh.factory<_i581.GetCreateCustomerUseCase>(
      () =>
          _i581.GetCreateCustomerUseCase(gh<_i396.CreateCustomerRepository>()),
    );
    gh.factory<_i582.GetSavedCreateCustomerUseCase>(
      () => _i582.GetSavedCreateCustomerUseCase(
        gh<_i396.CreateCustomerRepository>(),
      ),
    );
    gh.factory<_i583.UpdateCreateCustomerUseCase>(
      () => _i583.UpdateCreateCustomerUseCase(
        gh<_i396.CreateCustomerRepository>(),
      ),
    );
    gh.factory<_i584.SurveyLoadCreateCustomerUseCase>(
      () => _i584.SurveyLoadCreateCustomerUseCase(
        gh<_i396.CreateCustomerRepository>(),
      ),
    );
    gh.factory<_i585.CustomerSearchCreateCustomerUseCase>(
      () => _i585.CustomerSearchCreateCustomerUseCase(
        gh<_i396.CreateCustomerRepository>(),
      ),
    );
    gh.factory<_i586.InfoCustomerBloc>(
      () => _i586.InfoCustomerBloc(gh<_i509.GetCustomerInfoUseCase>()),
    );
    gh.factory<_i587.GetProductConfirmBranchUseCase>(
      () => _i587.GetProductConfirmBranchUseCase(
        gh<_i370.ProductConfirmRepository>(),
      ),
    );
    gh.factory<_i588.GetProductDetailConfirmUseCase>(
      () => _i588.GetProductDetailConfirmUseCase(
        gh<_i370.ProductConfirmRepository>(),
      ),
    );
    gh.factory<_i589.ApprovalProductDetailConfirmUsecase>(
      () => _i589.ApprovalProductDetailConfirmUsecase(
        gh<_i370.ProductConfirmRepository>(),
      ),
    );
    gh.factory<_i590.GetProductConfirmUseCase>(
      () =>
          _i590.GetProductConfirmUseCase(gh<_i370.ProductConfirmRepository>()),
    );
    gh.factory<_i591.RejectProductDetailConfirmUsecase>(
      () => _i591.RejectProductDetailConfirmUsecase(
        gh<_i370.ProductConfirmRepository>(),
      ),
    );
    gh.factory<_i592.SocketAccessTokenGetLoginUseCase>(
      () => _i592.SocketAccessTokenGetLoginUseCase(gh<_i268.UserRepository>()),
    );
    gh.factory<_i593.GetNewsUseCase>(
      () => _i593.GetNewsUseCase(gh<_i394.NewsRepository>()),
    );
    gh.factory<_i594.SearchNewsUseCase>(
      () => _i594.SearchNewsUseCase(gh<_i394.NewsRepository>()),
    );
    gh.factory<_i595.GetDetailNewsUseCase>(
      () => _i595.GetDetailNewsUseCase(gh<_i394.NewsRepository>()),
    );
    gh.factory<_i596.GetDepartmentUseCase>(
      () => _i596.GetDepartmentUseCase(gh<_i449.StaffRepository>()),
    );
    gh.factory<_i597.GetFunctionRoomUseCase>(
      () => _i597.GetFunctionRoomUseCase(gh<_i449.StaffRepository>()),
    );
    gh.factory<_i598.GetStaffUseCase>(
      () => _i598.GetStaffUseCase(gh<_i449.StaffRepository>()),
    );
    gh.factory<_i599.CreateEformUseCase>(
      () => _i599.CreateEformUseCase(gh<_i490.EformRepository>()),
    );
    gh.factory<_i600.ApprovingSignalEformUseCase>(
      () => _i600.ApprovingSignalEformUseCase(gh<_i490.EformRepository>()),
    );
    gh.factory<_i601.ApprovingOtpEformUseCase>(
      () => _i601.ApprovingOtpEformUseCase(gh<_i490.EformRepository>()),
    );
    gh.factory<_i602.GetEformRequestTypeUseCase>(
      () => _i602.GetEformRequestTypeUseCase(gh<_i490.EformRepository>()),
    );
    gh.factory<_i603.RejectEformUseCase>(
      () => _i603.RejectEformUseCase(gh<_i490.EformRepository>()),
    );
    gh.factory<_i604.ApprovingEformUseCase>(
      () => _i604.ApprovingEformUseCase(gh<_i490.EformRepository>()),
    );
    gh.factory<_i605.GetDetailEformUseCase>(
      () => _i605.GetDetailEformUseCase(gh<_i490.EformRepository>()),
    );
    gh.factory<_i606.GetEformUseCase>(
      () => _i606.GetEformUseCase(gh<_i490.EformRepository>()),
    );
    gh.factory<_i607.NewsBloc>(
      () => _i607.NewsBloc(
        gh<_i593.GetNewsUseCase>(),
        gh<_i594.SearchNewsUseCase>(),
      ),
    );
    gh.factory<_i608.OrderFoodBloc>(
      () => _i608.OrderFoodBloc(
        gh<_i528.OrderFoodGetUseCase>(),
        gh<_i529.OrderFoodCreatedUseCase>(),
        gh<_i527.OrderFoodDeleteUseCase>(),
        gh<_i372.UniversalQrScanUseCase>(),
        gh<_i531.SetDefaultAddressFoodUseCase>(),
      ),
    );
    gh.factory<_i609.CompleteTicketActiveUseCase>(
      () =>
          _i609.CompleteTicketActiveUseCase(gh<_i202.TicketActiveRepository>()),
    );
    gh.factory<_i610.GetTicketActiveUseCase>(
      () => _i610.GetTicketActiveUseCase(gh<_i202.TicketActiveRepository>()),
    );
    gh.factory<_i611.CreateTicketActiveUseCase>(
      () => _i611.CreateTicketActiveUseCase(gh<_i202.TicketActiveRepository>()),
    );
    gh.factory<_i612.DetailEformBloc>(
      () => _i612.DetailEformBloc(
        gh<_i605.GetDetailEformUseCase>(),
        gh<_i604.ApprovingEformUseCase>(),
      ),
    );
    gh.factory<_i613.GetAddressNearLocationGoogleUseCase>(
      () => _i613.GetAddressNearLocationGoogleUseCase(
        gh<_i354.LocationGoogleRepository>(),
      ),
    );
    gh.factory<_i614.GetLocationGoogleUseCase>(
      () =>
          _i614.GetLocationGoogleUseCase(gh<_i354.LocationGoogleRepository>()),
    );
    gh.factory<_i615.GetAddressAddressLocationGoogleUseCase>(
      () => _i615.GetAddressAddressLocationGoogleUseCase(
        gh<_i354.LocationGoogleRepository>(),
      ),
    );
    gh.factory<_i616.GetAddressSearchLocationGoogleUseCase>(
      () => _i616.GetAddressSearchLocationGoogleUseCase(
        gh<_i354.LocationGoogleRepository>(),
      ),
    );
    gh.factory<_i617.SaveHrOrganizationUseCase>(
      () =>
          _i617.SaveHrOrganizationUseCase(gh<_i569.HrOrganizationRepository>()),
    );
    gh.factory<_i618.GetHrOrganizationUseCase>(
      () =>
          _i618.GetHrOrganizationUseCase(gh<_i569.HrOrganizationRepository>()),
    );
    gh.factory<_i619.GetSavedHrOrganizationUseCase>(
      () => _i619.GetSavedHrOrganizationUseCase(
        gh<_i569.HrOrganizationRepository>(),
      ),
    );
    gh.factory<_i620.RemoveHrOrganizationUseCase>(
      () => _i620.RemoveHrOrganizationUseCase(
        gh<_i569.HrOrganizationRepository>(),
      ),
    );
    gh.factory<_i621.GetRoomListAddTagsImageUseCase>(
      () => _i621.GetRoomListAddTagsImageUseCase(
        gh<_i275.AddTagsImageRepository>(),
      ),
    );
    gh.factory<_i622.CreateImageTagAddTagsImageUseCase>(
      () => _i622.CreateImageTagAddTagsImageUseCase(
        gh<_i275.AddTagsImageRepository>(),
      ),
    );
    gh.factory<_i623.CreateMergeImageAddTagsImageUseCase>(
      () => _i623.CreateMergeImageAddTagsImageUseCase(
        gh<_i275.AddTagsImageRepository>(),
      ),
    );
    gh.factory<_i624.SaveAddTagsImageUseCase>(
      () => _i624.SaveAddTagsImageUseCase(gh<_i275.AddTagsImageRepository>()),
    );
    gh.factory<_i625.GetSavedAddTagsImageUseCase>(
      () =>
          _i625.GetSavedAddTagsImageUseCase(gh<_i275.AddTagsImageRepository>()),
    );
    gh.factory<_i626.RemoveAddTagsImageUseCase>(
      () => _i626.RemoveAddTagsImageUseCase(gh<_i275.AddTagsImageRepository>()),
    );
    gh.factory<_i627.GetTagListAddTagsImageUseCase>(
      () => _i627.GetTagListAddTagsImageUseCase(
        gh<_i275.AddTagsImageRepository>(),
      ),
    );
    gh.factory<_i628.UserSearchAddTagsImageUseCase>(
      () => _i628.UserSearchAddTagsImageUseCase(
        gh<_i275.AddTagsImageRepository>(),
      ),
    );
    gh.factory<_i629.GetAddTagsImageUseCase>(
      () => _i629.GetAddTagsImageUseCase(gh<_i275.AddTagsImageRepository>()),
    );
    gh.factory<_i630.GetImageListAddTagsImageUseCase>(
      () => _i630.GetImageListAddTagsImageUseCase(
        gh<_i275.AddTagsImageRepository>(),
      ),
    );
    gh.factory<_i631.DetailNewsBloc>(
      () => _i631.DetailNewsBloc(gh<_i595.GetDetailNewsUseCase>()),
    );
    gh.factory<_i632.EmployeeGetBranchSelectionUseCase>(
      () => _i632.EmployeeGetBranchSelectionUseCase(
        gh<_i200.BranchSelectionRepository>(),
      ),
    );
    gh.factory<_i633.RemoveBranchSelectionUseCase>(
      () => _i633.RemoveBranchSelectionUseCase(
        gh<_i200.BranchSelectionRepository>(),
      ),
    );
    gh.factory<_i634.SaveBranchSelectionUseCase>(
      () => _i634.SaveBranchSelectionUseCase(
        gh<_i200.BranchSelectionRepository>(),
      ),
    );
    gh.factory<_i635.BedSelectBranchSelectionUseCase>(
      () => _i635.BedSelectBranchSelectionUseCase(
        gh<_i200.BranchSelectionRepository>(),
      ),
    );
    gh.factory<_i636.GetFloorBranchSelectionUseCase>(
      () => _i636.GetFloorBranchSelectionUseCase(
        gh<_i200.BranchSelectionRepository>(),
      ),
    );
    gh.factory<_i637.BedChangeBranchSelectionUseCase>(
      () => _i637.BedChangeBranchSelectionUseCase(
        gh<_i200.BranchSelectionRepository>(),
      ),
    );
    gh.factory<_i638.EstimateTimeGetBranchSelectionUseCase>(
      () => _i638.EstimateTimeGetBranchSelectionUseCase(
        gh<_i200.BranchSelectionRepository>(),
      ),
    );
    gh.factory<_i639.GetSavedBranchSelectionUseCase>(
      () => _i639.GetSavedBranchSelectionUseCase(
        gh<_i200.BranchSelectionRepository>(),
      ),
    );
    gh.factory<_i640.GetBedBranchSelectionUseCase>(
      () => _i640.GetBedBranchSelectionUseCase(
        gh<_i200.BranchSelectionRepository>(),
      ),
    );
    gh.factory<_i641.GetBranchSelectionUseCase>(
      () => _i641.GetBranchSelectionUseCase(
        gh<_i200.BranchSelectionRepository>(),
      ),
    );
    gh.factory<_i642.GetRoomBranchSelectionUseCase>(
      () => _i642.GetRoomBranchSelectionUseCase(
        gh<_i200.BranchSelectionRepository>(),
      ),
    );
    gh.factory<_i643.GetProvinceUseCase>(
      () => _i643.GetProvinceUseCase(gh<_i200.BranchSelectionRepository>()),
    );
    gh.lazySingleton<_i644.ConsultationManagerRepository>(
      () => _i645.ConsultationManagerRepositoryImpl(
        gh<_i18.ConsultationManagerApiService>(),
        gh<_i23.EZCache>(),
      ),
    );
    gh.factory<_i646.MedicalDepartmentListBloc>(
      () => _i646.MedicalDepartmentListBloc(
        gh<_i378.GetMedicalDepartmentListUseCase>(),
        gh<_i379.SaveMedicalDepartmentListUseCase>(),
        gh<_i377.GetSavedMedicalDepartmentListUseCase>(),
        gh<_i380.RemoveMedicalDepartmentListUseCase>(),
      ),
    );
    gh.factory<_i647.PostReadNotificationUseCase>(
      () =>
          _i647.PostReadNotificationUseCase(gh<_i488.NotificationRepository>()),
    );
    gh.factory<_i648.GetDetailNotificationUseCase>(
      () => _i648.GetDetailNotificationUseCase(
        gh<_i488.NotificationRepository>(),
      ),
    );
    gh.factory<_i649.GetNavigationInfoUseCase>(
      () => _i649.GetNavigationInfoUseCase(gh<_i488.NotificationRepository>()),
    );
    gh.factory<_i650.SearchNotificationsUseCase>(
      () =>
          _i650.SearchNotificationsUseCase(gh<_i488.NotificationRepository>()),
    );
    gh.factory<_i651.GetNotificationsUseCase>(
      () => _i651.GetNotificationsUseCase(gh<_i488.NotificationRepository>()),
    );
    gh.factory<_i652.ReadAllNotificationUseCase>(
      () =>
          _i652.ReadAllNotificationUseCase(gh<_i488.NotificationRepository>()),
    );
    gh.lazySingleton<_i653.AssignTaskRepository>(
      () => _i654.AssignTaskRepositoryImpl(
        gh<_i18.AssignTaskApiService>(),
        gh<_i9.EZCache>(),
      ),
    );
    gh.lazySingleton<_i655.MedicalServiceLogListRepository>(
      () => _i656.MedicalServiceLogListRepositoryImpl(
        gh<_i18.MedicalServiceLogListApiService>(),
        gh<_i9.EZCache>(),
      ),
    );
    gh.factory<_i657.SuggestServicesFetchCustomerBookingInfoUseCase>(
      () => _i657.SuggestServicesFetchCustomerBookingInfoUseCase(
        gh<_i383.CustomerBookingInfoRepository>(),
      ),
    );
    gh.factory<_i658.RemoveCustomerBookingInfoUseCase>(
      () => _i658.RemoveCustomerBookingInfoUseCase(
        gh<_i383.CustomerBookingInfoRepository>(),
      ),
    );
    gh.factory<_i659.ServiceDetailsLoadCustomerBookingInfoUseCase>(
      () => _i659.ServiceDetailsLoadCustomerBookingInfoUseCase(
        gh<_i383.CustomerBookingInfoRepository>(),
      ),
    );
    gh.factory<_i660.SaveCustomerBookingInfoUseCase>(
      () => _i660.SaveCustomerBookingInfoUseCase(
        gh<_i383.CustomerBookingInfoRepository>(),
      ),
    );
    gh.factory<_i661.GetCustomerBookingInfoUseCase>(
      () => _i661.GetCustomerBookingInfoUseCase(
        gh<_i383.CustomerBookingInfoRepository>(),
      ),
    );
    gh.factory<_i662.BookedServicesFetchCustomerBookingInfoUseCase>(
      () => _i662.BookedServicesFetchCustomerBookingInfoUseCase(
        gh<_i383.CustomerBookingInfoRepository>(),
      ),
    );
    gh.factory<_i663.GetSavedCustomerBookingInfoUseCase>(
      () => _i663.GetSavedCustomerBookingInfoUseCase(
        gh<_i383.CustomerBookingInfoRepository>(),
      ),
    );
    gh.factory<_i664.UsedServiceFetchCustomerBookingInfoUseCase>(
      () => _i664.UsedServiceFetchCustomerBookingInfoUseCase(
        gh<_i383.CustomerBookingInfoRepository>(),
      ),
    );
    gh.factory<_i665.UploadFeedbackUseCase>(
      () => _i665.UploadFeedbackUseCase(gh<_i381.MediaUploadRepository>()),
    );
    gh.factory<_i666.UploadCheckInImageUseCase>(
      () => _i666.UploadCheckInImageUseCase(gh<_i381.MediaUploadRepository>()),
    );
    gh.factory<_i667.UploadAvatarUseCase>(
      () => _i667.UploadAvatarUseCase(gh<_i381.MediaUploadRepository>()),
    );
    gh.factory<_i668.UploadBackgroundUseCase>(
      () => _i668.UploadBackgroundUseCase(gh<_i381.MediaUploadRepository>()),
    );
    gh.factory<_i669.UploadKYCUseCase>(
      () => _i669.UploadKYCUseCase(gh<_i381.MediaUploadRepository>()),
    );
    gh.factory<_i670.DoctorFetchServiceDetailUseCase>(
      () => _i670.DoctorFetchServiceDetailUseCase(
        gh<_i270.ConsultationCustomerRepository>(),
      ),
    );
    gh.factory<_i671.EmployeeFetchServiceDetailUseCase>(
      () => _i671.EmployeeFetchServiceDetailUseCase(
        gh<_i270.ConsultationCustomerRepository>(),
      ),
    );
    gh.factory<_i672.SendKycPhotosSettingUseCase>(
      () => _i672.SendKycPhotosSettingUseCase(gh<_i268.UserRepository>()),
    );
    gh.factory<_i673.CheckinPermissionCheckUserUseCase>(
      () => _i673.CheckinPermissionCheckUserUseCase(gh<_i268.UserRepository>()),
    );
    gh.factory<_i674.StringeeTokenFetchUserUseCase>(
      () => _i674.StringeeTokenFetchUserUseCase(gh<_i268.UserRepository>()),
    );
    gh.factory<_i675.CheckPermissionUserUseCase>(
      () => _i675.CheckPermissionUserUseCase(gh<_i268.UserRepository>()),
    );
    gh.factory<_i676.UserDeletionUseCase>(
      () => _i676.UserDeletionUseCase(gh<_i268.UserRepository>()),
    );
    gh.lazySingleton<_i677.MedicalTemplateListRepository>(
      () => _i678.MedicalTemplateListRepositoryImpl(
        gh<_i18.MedicalTemplateListApiService>(),
        gh<_i9.EZCache>(),
      ),
    );
    gh.factory<_i679.UpdateCommentUseCase>(
      () => _i679.UpdateCommentUseCase(gh<_i471.CommentListRepository>()),
    );
    gh.factory<_i680.GetCheckinPhotoUseCase>(
      () => _i680.GetCheckinPhotoUseCase(gh<_i254.CheckinPhotoRepository>()),
    );
    gh.factory<_i681.GetSavedCheckinPhotoUseCase>(
      () =>
          _i681.GetSavedCheckinPhotoUseCase(gh<_i254.CheckinPhotoRepository>()),
    );
    gh.factory<_i682.SaveCheckinPhotoUseCase>(
      () => _i682.SaveCheckinPhotoUseCase(gh<_i254.CheckinPhotoRepository>()),
    );
    gh.factory<_i683.RemoveCheckinPhotoUseCase>(
      () => _i683.RemoveCheckinPhotoUseCase(gh<_i254.CheckinPhotoRepository>()),
    );
    gh.factory<_i684.DetailStaffEvaluationPeriodBloc>(
      () => _i684.DetailStaffEvaluationPeriodBloc(
        gh<_i486.GetDetailStaffEvaluationPeriodUseCase>(),
        gh<_i482.SaveDetailStaffEvaluationPeriodUseCase>(),
        gh<_i485.GetSavedDetailStaffEvaluationPeriodUseCase>(),
        gh<_i484.RemoveDetailStaffEvaluationPeriodUseCase>(),
        gh<_i483.EmployeeFetchDetailStaffEvaluationPeriodUseCase>(),
      ),
    );
    gh.factory<_i685.HomeFindBloc>(
      () => _i685.HomeFindBloc(gh<_i671.EmployeeFetchServiceDetailUseCase>()),
    );
    gh.factory<_i686.ThemeBloc>(
      () => _i686.ThemeBloc(
        gh<_i412.GetThemeOptionUseCase>(),
        gh<_i414.SaveThemeOptionUseCase>(),
      ),
    );
    gh.factory<_i687.GetBranchChatListUseCase>(
      () =>
          _i687.GetBranchChatListUseCase(gh<_i421.BranchChatListRepository>()),
    );
    gh.factory<_i688.GetSavedBranchChatListUseCase>(
      () => _i688.GetSavedBranchChatListUseCase(
        gh<_i421.BranchChatListRepository>(),
      ),
    );
    gh.factory<_i689.RemoveBranchChatListUseCase>(
      () => _i689.RemoveBranchChatListUseCase(
        gh<_i421.BranchChatListRepository>(),
      ),
    );
    gh.factory<_i690.SaveBranchChatListUseCase>(
      () =>
          _i690.SaveBranchChatListUseCase(gh<_i421.BranchChatListRepository>()),
    );
    gh.lazySingleton<_i691.RequestRepository>(
      () => _i692.RequestRepositoryImpl(
        gh<_i9.RequestApiService>(),
        cache: gh<_i9.EZCache>(),
      ),
    );
    gh.factory<_i693.TabBarBloc>(
      () => _i693.TabBarBloc(
        gh<_i460.GetStoryListUseCase>(),
        gh<_i556.GetServicesUseCase>(),
      ),
    );
    gh.factory<_i694.ImportantNotesBloc>(
      () => _i694.ImportantNotesBloc(
        gh<_i500.GetImportantNotesUseCase>(),
        gh<_i498.SaveImportantNotesUseCase>(),
        gh<_i497.GetSavedImportantNotesUseCase>(),
        gh<_i501.RemoveImportantNotesUseCase>(),
      ),
    );
    gh.factory<_i695.SaveCustomerInfoDetailsUseCase>(
      () => _i695.SaveCustomerInfoDetailsUseCase(
        gh<_i567.CustomerInfoDetailsRepository>(),
      ),
    );
    gh.factory<_i696.GetCustomerInfoDetailsUseCase>(
      () => _i696.GetCustomerInfoDetailsUseCase(
        gh<_i567.CustomerInfoDetailsRepository>(),
      ),
    );
    gh.factory<_i697.CheckoutCustomerInfoDetailsUseCase>(
      () => _i697.CheckoutCustomerInfoDetailsUseCase(
        gh<_i567.CustomerInfoDetailsRepository>(),
      ),
    );
    gh.factory<_i698.GetSavedCustomerInfoDetailsUseCase>(
      () => _i698.GetSavedCustomerInfoDetailsUseCase(
        gh<_i567.CustomerInfoDetailsRepository>(),
      ),
    );
    gh.factory<_i699.RemoveCustomerInfoDetailsUseCase>(
      () => _i699.RemoveCustomerInfoDetailsUseCase(
        gh<_i567.CustomerInfoDetailsRepository>(),
      ),
    );
    gh.factory<_i700.CustomerRecordBloc>(
      () => _i700.CustomerRecordBloc(
        gh<_i453.GetCustomerRecordUseCase>(),
        gh<_i454.SaveCustomerRecordUseCase>(),
        gh<_i452.GetSavedCustomerRecordUseCase>(),
        gh<_i451.RemoveCustomerRecordUseCase>(),
      ),
    );
    gh.lazySingleton<_i701.MedicalServiceCreationRepository>(
      () => _i702.MedicalServiceCreationRepositoryImpl(
        gh<_i18.MedicalServiceCreationApiService>(),
        gh<_i9.EZCache>(),
      ),
    );
    gh.lazySingleton<_i703.TagImageRepository>(
      () => _i704.TagImageRepositoryImpl(gh<_i193.TagImageApiService>()),
    );
    gh.factory<_i705.GetKpiEmployeeDetailUseCase>(
      () =>
          _i705.GetKpiEmployeeDetailUseCase(gh<_i520.KpiEmployeeRepository>()),
    );
    gh.factory<_i706.GetKpiEmployeeUseCase>(
      () => _i706.GetKpiEmployeeUseCase(gh<_i520.KpiEmployeeRepository>()),
    );
    gh.factory<_i707.CreateConsultationCustomerProfileUseCase>(
      () => _i707.CreateConsultationCustomerProfileUseCase(
        gh<_i406.CustomerProfileRepository>(),
      ),
    );
    gh.factory<_i708.SaveCustomerProfileUseCase>(
      () => _i708.SaveCustomerProfileUseCase(
        gh<_i406.CustomerProfileRepository>(),
      ),
    );
    gh.factory<_i709.RemoveCustomerProfileUseCase>(
      () => _i709.RemoveCustomerProfileUseCase(
        gh<_i406.CustomerProfileRepository>(),
      ),
    );
    gh.factory<_i710.GetConsultationHistoryCustomerProfileUseCase>(
      () => _i710.GetConsultationHistoryCustomerProfileUseCase(
        gh<_i406.CustomerProfileRepository>(),
      ),
    );
    gh.factory<_i711.GetCustomerProfileUseCase>(
      () => _i711.GetCustomerProfileUseCase(
        gh<_i406.CustomerProfileRepository>(),
      ),
    );
    gh.factory<_i712.GetSavedCustomerProfileUseCase>(
      () => _i712.GetSavedCustomerProfileUseCase(
        gh<_i406.CustomerProfileRepository>(),
      ),
    );
    gh.factory<_i713.UpdateConsultationCustomerProfileUseCase>(
      () => _i713.UpdateConsultationCustomerProfileUseCase(
        gh<_i406.CustomerProfileRepository>(),
      ),
    );
    gh.factory<_i714.SubmitRatingHumanUseCase>(
      () => _i714.SubmitRatingHumanUseCase(gh<_i260.RatingHumanRepository>()),
    );
    gh.factory<_i715.GetQuestionDetailUseCase>(
      () => _i715.GetQuestionDetailUseCase(gh<_i260.RatingHumanRepository>()),
    );
    gh.factory<_i716.SaveRatingHumanUseCase>(
      () => _i716.SaveRatingHumanUseCase(gh<_i260.RatingHumanRepository>()),
    );
    gh.factory<_i717.GetRatingHumanUseCase>(
      () => _i717.GetRatingHumanUseCase(gh<_i260.RatingHumanRepository>()),
    );
    gh.factory<_i718.HrOrganizationBloc>(
      () => _i718.HrOrganizationBloc(
        gh<_i618.GetHrOrganizationUseCase>(),
        gh<_i617.SaveHrOrganizationUseCase>(),
        gh<_i619.GetSavedHrOrganizationUseCase>(),
        gh<_i620.RemoveHrOrganizationUseCase>(),
      ),
    );
    gh.factory<_i719.RemoveMedicineDetailUseCase>(
      () => _i719.RemoveMedicineDetailUseCase(
        gh<_i212.MedicineDetailRepository>(),
      ),
    );
    gh.factory<_i720.GetUnitMedicineDetailUseCase>(
      () => _i720.GetUnitMedicineDetailUseCase(
        gh<_i212.MedicineDetailRepository>(),
      ),
    );
    gh.factory<_i721.CreateMedicineDetailUseCase>(
      () => _i721.CreateMedicineDetailUseCase(
        gh<_i212.MedicineDetailRepository>(),
      ),
    );
    gh.factory<_i722.UpdateMedicineDetailUseCase>(
      () => _i722.UpdateMedicineDetailUseCase(
        gh<_i212.MedicineDetailRepository>(),
      ),
    );
    gh.factory<_i723.GetSavedMedicineDetailUseCase>(
      () => _i723.GetSavedMedicineDetailUseCase(
        gh<_i212.MedicineDetailRepository>(),
      ),
    );
    gh.factory<_i724.GetMedicineDetailUseCase>(
      () =>
          _i724.GetMedicineDetailUseCase(gh<_i212.MedicineDetailRepository>()),
    );
    gh.factory<_i725.SaveMedicineDetailUseCase>(
      () =>
          _i725.SaveMedicineDetailUseCase(gh<_i212.MedicineDetailRepository>()),
    );
    gh.factory<_i726.SaveCustomerScheduleUseCase>(
      () => _i726.SaveCustomerScheduleUseCase(
        gh<_i518.CustomerScheduleRepository>(),
      ),
    );
    gh.factory<_i727.GetSavedCustomerScheduleUseCase>(
      () => _i727.GetSavedCustomerScheduleUseCase(
        gh<_i518.CustomerScheduleRepository>(),
      ),
    );
    gh.factory<_i728.RemoveCustomerScheduleUseCase>(
      () => _i728.RemoveCustomerScheduleUseCase(
        gh<_i518.CustomerScheduleRepository>(),
      ),
    );
    gh.factory<_i729.GetCustomerScheduleUseCase>(
      () => _i729.GetCustomerScheduleUseCase(
        gh<_i518.CustomerScheduleRepository>(),
      ),
    );
    gh.factory<_i730.GetSavedDevUseCase>(
      () => _i730.GetSavedDevUseCase(gh<_i352.DevRepository>()),
    );
    gh.factory<_i731.GetDevUseCase>(
      () => _i731.GetDevUseCase(gh<_i352.DevRepository>()),
    );
    gh.factory<_i732.SaveDevUseCase>(
      () => _i732.SaveDevUseCase(gh<_i352.DevRepository>()),
    );
    gh.factory<_i733.MiniAppDevUseCase>(
      () => _i733.MiniAppDevUseCase(gh<_i352.DevRepository>()),
    );
    gh.factory<_i734.RemoveDevUseCase>(
      () => _i734.RemoveDevUseCase(gh<_i352.DevRepository>()),
    );
    gh.factory<_i735.ChatListBloc>(
      () => _i735.ChatListBloc(
        gh<_i165.GetChatListUseCase>(),
        gh<_i168.SaveChatListUseCase>(),
        gh<_i157.GetSavedChatListUseCase>(),
        gh<_i167.RemoveChatListUseCase>(),
        gh<_i159.SearchChatListUseCase>(),
        gh<_i161.SearchMessageChatListUseCase>(),
        gh<_i158.PinConversationChatListUseCase>(),
        gh<_i156.GetTotalUnreadChatListUseCase>(),
        gh<_i330.LoadCreateChatFolderUseCase>(),
        gh<_i333.ConversationLoadCreateChatFolderUseCase>(),
        gh<_i160.MarkAsReadChatListUseCase>(),
        gh<_i163.GetRecentContactsChatListUseCase>(),
        gh<_i166.UpdatePinConversationChatListUseCase>(),
        gh<_i154.SortFolderChatListUseCase>(),
        gh<_i164.GetUnreadConversationsChatListUseCase>(),
      ),
    );
    gh.factory<_i736.RemoveMedicalServiceCreationUseCase>(
      () => _i736.RemoveMedicalServiceCreationUseCase(
        gh<_i701.MedicalServiceCreationRepository>(),
      ),
    );
    gh.factory<_i737.ServicesMedicalServiceCreationUseCase>(
      () => _i737.ServicesMedicalServiceCreationUseCase(
        gh<_i701.MedicalServiceCreationRepository>(),
      ),
    );
    gh.factory<_i738.MethodsMedicalServiceCreationUseCase>(
      () => _i738.MethodsMedicalServiceCreationUseCase(
        gh<_i701.MedicalServiceCreationRepository>(),
      ),
    );
    gh.factory<_i739.GetSavedMedicalServiceCreationUseCase>(
      () => _i739.GetSavedMedicalServiceCreationUseCase(
        gh<_i701.MedicalServiceCreationRepository>(),
      ),
    );
    gh.factory<_i740.SaveMedicalServiceCreationUseCase>(
      () => _i740.SaveMedicalServiceCreationUseCase(
        gh<_i701.MedicalServiceCreationRepository>(),
      ),
    );
    gh.factory<_i741.MedicalServiceCreationUseCase>(
      () => _i741.MedicalServiceCreationUseCase(
        gh<_i701.MedicalServiceCreationRepository>(),
      ),
    );
    gh.factory<_i742.SaveFeedbackUseCase>(
      () => _i742.SaveFeedbackUseCase(gh<_i273.FeedbackRepository>()),
    );
    gh.factory<_i743.SendFeedbackUseCase>(
      () => _i743.SendFeedbackUseCase(gh<_i273.FeedbackRepository>()),
    );
    gh.factory<_i744.GetSavedFeedbackUseCase>(
      () => _i744.GetSavedFeedbackUseCase(gh<_i273.FeedbackRepository>()),
    );
    gh.factory<_i745.RemoveFeedbackUseCase>(
      () => _i745.RemoveFeedbackUseCase(gh<_i273.FeedbackRepository>()),
    );
    gh.factory<_i746.MultiLanguageBloc>(
      () => _i746.MultiLanguageBloc(
        gh<_i411.GetLanguageOptionUseCase>(),
        gh<_i413.SaveLanguageOptionUseCase>(),
      ),
    );
    gh.factory<_i747.EformBloc>(
      () => _i747.EformBloc(gh<_i606.GetEformUseCase>()),
    );
    gh.factory<_i748.CreateNoteDetailsUseCase>(
      () => _i748.CreateNoteDetailsUseCase(gh<_i418.NoteDetailsRepository>()),
    );
    gh.factory<_i749.UpdateNoteDetailsUseCase>(
      () => _i749.UpdateNoteDetailsUseCase(gh<_i418.NoteDetailsRepository>()),
    );
    gh.factory<_i750.RemoveNoteDetailsUseCase>(
      () => _i750.RemoveNoteDetailsUseCase(gh<_i418.NoteDetailsRepository>()),
    );
    gh.factory<_i751.GetSavedNoteDetailsUseCase>(
      () => _i751.GetSavedNoteDetailsUseCase(gh<_i418.NoteDetailsRepository>()),
    );
    gh.factory<_i752.GetNoteDetailsUseCase>(
      () => _i752.GetNoteDetailsUseCase(gh<_i418.NoteDetailsRepository>()),
    );
    gh.factory<_i753.SaveNoteDetailsUseCase>(
      () => _i753.SaveNoteDetailsUseCase(gh<_i418.NoteDetailsRepository>()),
    );
    gh.factory<_i754.SaveCustomerListUseCase>(
      () => _i754.SaveCustomerListUseCase(gh<_i385.CustomerListRepository>()),
    );
    gh.factory<_i755.GetCustomerListUseCase>(
      () => _i755.GetCustomerListUseCase(gh<_i385.CustomerListRepository>()),
    );
    gh.factory<_i756.GetSavedCustomerListUseCase>(
      () =>
          _i756.GetSavedCustomerListUseCase(gh<_i385.CustomerListRepository>()),
    );
    gh.factory<_i757.RemoveCustomerListUseCase>(
      () => _i757.RemoveCustomerListUseCase(gh<_i385.CustomerListRepository>()),
    );
    gh.factory<_i758.GetCustomerRelationShipListUseCase>(
      () => _i758.GetCustomerRelationShipListUseCase(
        gh<_i385.CustomerListRepository>(),
      ),
    );
    gh.factory<_i759.RemovePxUnasignedUseCase>(
      () => _i759.RemovePxUnasignedUseCase(gh<_i445.PxUnasignedRepository>()),
    );
    gh.factory<_i760.GetPxCustomerListUseCase>(
      () => _i760.GetPxCustomerListUseCase(gh<_i445.PxUnasignedRepository>()),
    );
    gh.factory<_i761.SavePxUnasignedUseCase>(
      () => _i761.SavePxUnasignedUseCase(gh<_i445.PxUnasignedRepository>()),
    );
    gh.factory<_i762.GetSavedPxUnasignedUseCase>(
      () => _i762.GetSavedPxUnasignedUseCase(gh<_i445.PxUnasignedRepository>()),
    );
    gh.factory<_i763.LikeListBloc>(
      () => _i763.LikeListBloc(gh<_i393.GetLikeListUseCase>()),
    );
    gh.factory<_i764.TicketActiveBloc>(
      () => _i764.TicketActiveBloc(gh<_i610.GetTicketActiveUseCase>()),
    );
    gh.factory<_i765.UpdateProfileUseCase>(
      () => _i765.UpdateProfileUseCase(gh<_i268.UserRepository>()),
    );
    gh.factory<_i766.CheckEmployeeUseCase>(
      () => _i766.CheckEmployeeUseCase(gh<_i268.UserRepository>()),
    );
    gh.factory<_i767.GetOtpUseCase>(
      () => _i767.GetOtpUseCase(gh<_i268.UserRepository>()),
    );
    gh.factory<_i768.CheckPhoneUseCase>(
      () => _i768.CheckPhoneUseCase(gh<_i268.UserRepository>()),
    );
    gh.factory<_i769.LoginUseCase>(
      () => _i769.LoginUseCase(gh<_i268.UserRepository>()),
    );
    gh.factory<_i770.LogoutUseCase>(
      () => _i770.LogoutUseCase(gh<_i268.UserRepository>()),
    );
    gh.factory<_i771.UploadImagesUseCase>(
      () => _i771.UploadImagesUseCase(gh<_i268.UserRepository>()),
    );
    gh.factory<_i772.GetProfilesUseCase>(
      () => _i772.GetProfilesUseCase(gh<_i268.UserRepository>()),
    );
    gh.factory<_i773.GetEnableOnlineLoggerUseCase>(
      () => _i773.GetEnableOnlineLoggerUseCase(gh<_i268.UserRepository>()),
    );
    gh.factory<_i774.GetProfileUseCase>(
      () => _i774.GetProfileUseCase(gh<_i268.UserRepository>()),
    );
    gh.factory<_i775.ResetPasswordUseCase>(
      () => _i775.ResetPasswordUseCase(gh<_i268.UserRepository>()),
    );
    gh.factory<_i776.UploadFilesUseCase>(
      () => _i776.UploadFilesUseCase(gh<_i268.UserRepository>()),
    );
    gh.factory<_i777.GetConfigurationUseCase>(
      () => _i777.GetConfigurationUseCase(gh<_i268.UserRepository>()),
    );
    gh.factory<_i778.ConfirmOtpUseCase>(
      () => _i778.ConfirmOtpUseCase(gh<_i268.UserRepository>()),
    );
    gh.factory<_i779.UpdateBioUseCase>(
      () => _i779.UpdateBioUseCase(gh<_i268.UserRepository>()),
    );
    gh.factory<_i780.LoginSocialUseCase>(
      () => _i780.LoginSocialUseCase(gh<_i268.UserRepository>()),
    );
    gh.factory<_i781.SubmitFeedbackUseCase>(
      () => _i781.SubmitFeedbackUseCase(gh<_i268.UserRepository>()),
    );
    gh.factory<_i782.UploadAudiosUseCase>(
      () => _i782.UploadAudiosUseCase(gh<_i268.UserRepository>()),
    );
    gh.factory<_i783.ChangePasswordUseCase>(
      () => _i783.ChangePasswordUseCase(gh<_i268.UserRepository>()),
    );
    gh.factory<_i784.UploadUseCase>(
      () => _i784.UploadUseCase(gh<_i268.UserRepository>()),
    );
    gh.factory<_i785.GetUserInfoUseCase>(
      () => _i785.GetUserInfoUseCase(gh<_i268.UserRepository>()),
    );
    gh.lazySingleton<_i786.CacheQuickActionRemoveUseCase>(
      () => _i786.CacheQuickActionRemoveUseCase(gh<_i268.UserRepository>()),
    );
    gh.lazySingleton<_i787.CacheQuickActionGetUseCase>(
      () => _i787.CacheQuickActionGetUseCase(gh<_i268.UserRepository>()),
    );
    gh.lazySingleton<_i788.CacheQuickActionSaveUseCase>(
      () => _i788.CacheQuickActionSaveUseCase(gh<_i268.UserRepository>()),
    );
    gh.lazySingleton<_i789.CacheLoginStringeeRemoveUseCase>(
      () => _i789.CacheLoginStringeeRemoveUseCase(gh<_i268.UserRepository>()),
    );
    gh.lazySingleton<_i790.IsShowBoardingUseCase>(
      () => _i790.IsShowBoardingUseCase(gh<_i268.UserRepository>()),
    );
    gh.lazySingleton<_i791.HasTokenUseCase>(
      () => _i791.HasTokenUseCase(gh<_i268.UserRepository>()),
    );
    gh.lazySingleton<_i792.CacheLoginStringeeGetUseCase>(
      () => _i792.CacheLoginStringeeGetUseCase(gh<_i268.UserRepository>()),
    );
    gh.lazySingleton<_i793.CacheUserUseCase>(
      () => _i793.CacheUserUseCase(gh<_i268.UserRepository>()),
    );
    gh.lazySingleton<_i794.CacheLoginStringeeSaveUseCase>(
      () => _i794.CacheLoginStringeeSaveUseCase(gh<_i268.UserRepository>()),
    );
    gh.lazySingleton<_i795.CacheCheckoutHourSaveUseCase>(
      () => _i795.CacheCheckoutHourSaveUseCase(gh<_i268.UserRepository>()),
    );
    gh.lazySingleton<_i796.CacheCheckinHourSaveUseCase>(
      () => _i796.CacheCheckinHourSaveUseCase(gh<_i268.UserRepository>()),
    );
    gh.lazySingleton<_i797.CacheCheckoutHourGetUseCase>(
      () => _i797.CacheCheckoutHourGetUseCase(gh<_i268.UserRepository>()),
    );
    gh.lazySingleton<_i798.CacheCheckinHourGetUseCase>(
      () => _i798.CacheCheckinHourGetUseCase(gh<_i268.UserRepository>()),
    );
    gh.factory<_i799.DevBloc>(
      () => _i799.DevBloc(
        gh<_i731.GetDevUseCase>(),
        gh<_i732.SaveDevUseCase>(),
        gh<_i730.GetSavedDevUseCase>(),
        gh<_i734.RemoveDevUseCase>(),
        gh<_i733.MiniAppDevUseCase>(),
      ),
    );
    gh.factory<_i800.ProductDetailConfirmBloc>(
      () => _i800.ProductDetailConfirmBloc(
        gh<_i588.GetProductDetailConfirmUseCase>(),
        gh<_i589.ApprovalProductDetailConfirmUsecase>(),
        gh<_i591.RejectProductDetailConfirmUsecase>(),
      ),
    );
    gh.lazySingleton<_i801.CollaboratorUserBloc>(
      () => _i801.CollaboratorUserBloc(gh<_i793.CacheUserUseCase>()),
    );
    gh.factory<_i802.ScheduleDetailsBloc>(
      () => _i802.ScheduleDetailsBloc(
        gh<_i574.GetScheduleDetailsUseCase>(),
        gh<_i572.SaveScheduleDetailsUseCase>(),
        gh<_i573.GetSavedScheduleDetailsUseCase>(),
        gh<_i571.RemoveScheduleDetailsUseCase>(),
      ),
    );
    gh.factory<_i803.NotiBotTypePostTakingCareCustomerUseCase>(
      () => _i803.NotiBotTypePostTakingCareCustomerUseCase(
        gh<_i375.TakingCareCustomerRepository>(),
      ),
    );
    gh.factory<_i804.RemoveImageTakingCareCustomerUseCase>(
      () => _i804.RemoveImageTakingCareCustomerUseCase(
        gh<_i375.TakingCareCustomerRepository>(),
      ),
    );
    gh.factory<_i805.FinishTaskTakingCareCustomerUseCase>(
      () => _i805.FinishTaskTakingCareCustomerUseCase(
        gh<_i375.TakingCareCustomerRepository>(),
      ),
    );
    gh.factory<_i806.GetSavedTakingCareCustomerUseCase>(
      () => _i806.GetSavedTakingCareCustomerUseCase(
        gh<_i375.TakingCareCustomerRepository>(),
      ),
    );
    gh.factory<_i807.CreateSupportTakingCareCustomerUseCase>(
      () => _i807.CreateSupportTakingCareCustomerUseCase(
        gh<_i375.TakingCareCustomerRepository>(),
      ),
    );
    gh.factory<_i808.GetSectionTakingCareCustomerUseCase>(
      () => _i808.GetSectionTakingCareCustomerUseCase(
        gh<_i375.TakingCareCustomerRepository>(),
      ),
    );
    gh.factory<_i809.RemoveTakingCareCustomerUseCase>(
      () => _i809.RemoveTakingCareCustomerUseCase(
        gh<_i375.TakingCareCustomerRepository>(),
      ),
    );
    gh.factory<_i810.SaveTakingCareCustomerUseCase>(
      () => _i810.SaveTakingCareCustomerUseCase(
        gh<_i375.TakingCareCustomerRepository>(),
      ),
    );
    gh.factory<_i811.CreateTreatmentDetailsTakingCareCustomerUseCase>(
      () => _i811.CreateTreatmentDetailsTakingCareCustomerUseCase(
        gh<_i375.TakingCareCustomerRepository>(),
      ),
    );
    gh.factory<_i812.CheckEmployeeInRoomTakingCareCustomerUseCase>(
      () => _i812.CheckEmployeeInRoomTakingCareCustomerUseCase(
        gh<_i375.TakingCareCustomerRepository>(),
      ),
    );
    gh.factory<_i813.UpdateServiceDetailUseCaseTakingCareCustomerUseCase>(
      () => _i813.UpdateServiceDetailUseCaseTakingCareCustomerUseCase(
        gh<_i375.TakingCareCustomerRepository>(),
      ),
    );
    gh.factory<_i814.GetTakingCareCustomerUseCase>(
      () => _i814.GetTakingCareCustomerUseCase(
        gh<_i375.TakingCareCustomerRepository>(),
      ),
    );
    gh.factory<_i815.GetTreatmentPhotoTakingCareCustomerUseCase>(
      () => _i815.GetTreatmentPhotoTakingCareCustomerUseCase(
        gh<_i375.TakingCareCustomerRepository>(),
      ),
    );
    gh.factory<_i816.BotTypeLoadTakingCareCustomerUseCase>(
      () => _i816.BotTypeLoadTakingCareCustomerUseCase(
        gh<_i375.TakingCareCustomerRepository>(),
      ),
    );
    gh.factory<_i817.UploadRecordTakingCareCustomerUseCase>(
      () => _i817.UploadRecordTakingCareCustomerUseCase(
        gh<_i375.TakingCareCustomerRepository>(),
      ),
    );
    gh.factory<_i818.UploadImagesTakingCareCustomerUseCase>(
      () => _i818.UploadImagesTakingCareCustomerUseCase(
        gh<_i375.TakingCareCustomerRepository>(),
      ),
    );
    gh.factory<_i819.GetSavedPxListUseCase>(
      () => _i819.GetSavedPxListUseCase(gh<_i348.PxListRepository>()),
    );
    gh.factory<_i820.GetPxListUseCase>(
      () => _i820.GetPxListUseCase(gh<_i348.PxListRepository>()),
    );
    gh.factory<_i821.SavePxListUseCase>(
      () => _i821.SavePxListUseCase(gh<_i348.PxListRepository>()),
    );
    gh.factory<_i822.RemovePxListUseCase>(
      () => _i822.RemovePxListUseCase(gh<_i348.PxListRepository>()),
    );
    gh.factory<_i823.SaveMedicalServiceLogListUseCase>(
      () => _i823.SaveMedicalServiceLogListUseCase(
        gh<_i655.MedicalServiceLogListRepository>(),
      ),
    );
    gh.factory<_i824.RemoveMedicalServiceLogListUseCase>(
      () => _i824.RemoveMedicalServiceLogListUseCase(
        gh<_i655.MedicalServiceLogListRepository>(),
      ),
    );
    gh.factory<_i825.GetMedicalServiceLogListUseCase>(
      () => _i825.GetMedicalServiceLogListUseCase(
        gh<_i655.MedicalServiceLogListRepository>(),
      ),
    );
    gh.factory<_i826.GetSavedMedicalServiceLogListUseCase>(
      () => _i826.GetSavedMedicalServiceLogListUseCase(
        gh<_i655.MedicalServiceLogListRepository>(),
      ),
    );
    gh.factory<_i827.GroupChatDetailBloc>(
      () => _i827.GroupChatDetailBloc(
        gh<_i284.GetGroupChatDetailUseCase>(),
        gh<_i293.SaveGroupChatDetailUseCase>(),
        gh<_i292.GetSavedGroupChatDetailUseCase>(),
        gh<_i279.RemoveGroupChatDetailUseCase>(),
        gh<_i282.MediaLoadGroupChatDetailUseCase>(),
        gh<_i289.FileLoadGroupChatDetailUseCase>(),
        gh<_i286.LinkLoadGroupChatDetailUseCase>(),
        gh<_i291.UpdateGroupChatDetailUseCase>(),
        gh<_i278.AvatarUploadGroupChatDetailUseCase>(),
        gh<_i772.GetProfilesUseCase>(),
        gh<_i281.DeleteGroupUseCase>(),
        gh<_i280.MemberInfoLoadGroupChatDetailUseCase>(),
        gh<_i283.UpdateMemberRuleGroupChatDetailUseCase>(),
        gh<_i287.UpdateAdminRuleGroupChatDetailUseCase>(),
        gh<_i288.GetUserRulesGroupChatDetailUseCase>(),
        gh<_i290.GetRuleByRoleGroupChatDetailUseCase>(),
        gh<_i285.ChangeOwnerGroupChatDetailUseCase>(),
        gh<_i277.GetUserExceptionGroupChatDetailUseCase>(),
      ),
    );
    gh.factory<_i828.CheckinReminderBloc>(
      () => _i828.CheckinReminderBloc(
        gh<_i798.CacheCheckinHourGetUseCase>(),
        gh<_i796.CacheCheckinHourSaveUseCase>(),
        gh<_i797.CacheCheckoutHourGetUseCase>(),
        gh<_i795.CacheCheckoutHourSaveUseCase>(),
      ),
    );
    gh.factory<_i829.CheckLeaderHomeUseCase>(
      () => _i829.CheckLeaderHomeUseCase(gh<_i457.HomeRepository>()),
    );
    gh.factory<_i830.WorkLeaderCheckHomeUseCase>(
      () => _i830.WorkLeaderCheckHomeUseCase(gh<_i457.HomeRepository>()),
    );
    gh.factory<_i831.CheckinCustomerUseCase>(
      () => _i831.CheckinCustomerUseCase(gh<_i294.CustomerRepository>()),
    );
    gh.factory<_i832.GetRoomListCustomerUseCase>(
      () => _i832.GetRoomListCustomerUseCase(gh<_i294.CustomerRepository>()),
    );
    gh.factory<_i833.SaveCustomerRoomCodeUseCase>(
      () => _i833.SaveCustomerRoomCodeUseCase(gh<_i294.CustomerRepository>()),
    );
    gh.factory<_i834.PrintCustomerUseCase>(
      () => _i834.PrintCustomerUseCase(gh<_i294.CustomerRepository>()),
    );
    gh.factory<_i835.StickerBloc>(
      () => _i835.StickerBloc(
        gh<_i344.CreateStickerListUseCase>(),
        gh<_i346.GetStickerListUseCase>(),
        gh<_i343.GetStickerSetUseCase>(),
        gh<_i340.GetStickerRecentUseCase>(),
        gh<_i345.UploadStickerUseCase>(),
        gh<_i342.UpdateStickerRecentUseCase>(),
        gh<_i339.GetStickerOnlySetUseCase>(),
        gh<_i347.RemoveStickerSetUseCase>(),
        gh<_i341.RemoveStickerUseCase>(),
        gh<_i338.UploadStickerSetUseCase>(),
      ),
    );
    gh.factory<_i836.NoteDetailsBloc>(
      () => _i836.NoteDetailsBloc(
        gh<_i752.GetNoteDetailsUseCase>(),
        gh<_i753.SaveNoteDetailsUseCase>(),
        gh<_i751.GetSavedNoteDetailsUseCase>(),
        gh<_i750.RemoveNoteDetailsUseCase>(),
        gh<_i748.CreateNoteDetailsUseCase>(),
        gh<_i749.UpdateNoteDetailsUseCase>(),
        gh<_i771.UploadImagesUseCase>(),
      ),
    );
    gh.factory<_i837.TakingCareCustomerBloc>(
      () => _i837.TakingCareCustomerBloc(
        gh<_i814.GetTakingCareCustomerUseCase>(),
        gh<_i810.SaveTakingCareCustomerUseCase>(),
        gh<_i806.GetSavedTakingCareCustomerUseCase>(),
        gh<_i809.RemoveTakingCareCustomerUseCase>(),
        gh<_i805.FinishTaskTakingCareCustomerUseCase>(),
        gh<_i818.UploadImagesTakingCareCustomerUseCase>(),
        gh<_i804.RemoveImageTakingCareCustomerUseCase>(),
        gh<_i817.UploadRecordTakingCareCustomerUseCase>(),
        gh<_i807.CreateSupportTakingCareCustomerUseCase>(),
        gh<_i812.CheckEmployeeInRoomTakingCareCustomerUseCase>(),
        gh<_i816.BotTypeLoadTakingCareCustomerUseCase>(),
        gh<_i303.ProductLoadConsultationCustomerUseCase>(),
        gh<_i324.GetTreatmentDetailUseCase>(),
        gh<_i803.NotiBotTypePostTakingCareCustomerUseCase>(),
        gh<_i815.GetTreatmentPhotoTakingCareCustomerUseCase>(),
        gh<_i813.UpdateServiceDetailUseCaseTakingCareCustomerUseCase>(),
      ),
    );
    gh.factory<_i838.DeleteTagByComboTagUsecase>(
      () => _i838.DeleteTagByComboTagUsecase(gh<_i703.TagImageRepository>()),
    );
    gh.factory<_i839.DeleteImageByComboTagUsecase>(
      () => _i839.DeleteImageByComboTagUsecase(gh<_i703.TagImageRepository>()),
    );
    gh.factory<_i840.GetComboTagUsecase>(
      () => _i840.GetComboTagUsecase(gh<_i703.TagImageRepository>()),
    );
    gh.factory<_i841.GetImageByComboTagUsecae>(
      () => _i841.GetImageByComboTagUsecae(gh<_i703.TagImageRepository>()),
    );
    gh.factory<_i842.StoryDetailBloc>(
      () => _i842.StoryDetailBloc(
        gh<_i177.GetStoryDetailUseCase>(),
        gh<_i469.DeleteStoryUseCase>(),
        gh<_i391.PostLikeStoryUseCase>(),
        gh<_i465.UpdateStoryUseCase>(),
      ),
    );
    gh.factory<_i843.GetSavedUserListUseCase>(
      () => _i843.GetSavedUserListUseCase(gh<_i554.UserListRepository>()),
    );
    gh.factory<_i844.GetUserListUseCase>(
      () => _i844.GetUserListUseCase(gh<_i554.UserListRepository>()),
    );
    gh.factory<_i845.RemoveUserListUseCase>(
      () => _i845.RemoveUserListUseCase(gh<_i554.UserListRepository>()),
    );
    gh.factory<_i846.SaveUserListUseCase>(
      () => _i846.SaveUserListUseCase(gh<_i554.UserListRepository>()),
    );
    gh.factory<_i847.GetCommentListUseCase>(
      () => _i847.GetCommentListUseCase(gh<_i471.CommentListRepository>()),
    );
    gh.factory<_i848.PostCommentUseCase>(
      () => _i848.PostCommentUseCase(gh<_i471.CommentListRepository>()),
    );
    gh.factory<_i849.DeleteCommentUseCase>(
      () => _i849.DeleteCommentUseCase(gh<_i471.CommentListRepository>()),
    );
    gh.factory<_i850.CommentUploadFileUseCase>(
      () => _i850.CommentUploadFileUseCase(gh<_i471.CommentListRepository>()),
    );
    gh.factory<_i851.CreatingEformBloc>(
      () => _i851.CreatingEformBloc(gh<_i599.CreateEformUseCase>()),
    );
    gh.factory<_i852.ProfileBloc>(
      () => _i852.ProfileBloc(
        gh<_i774.GetProfileUseCase>(),
        gh<_i667.UploadAvatarUseCase>(),
        gh<_i779.UpdateBioUseCase>(),
        gh<_i668.UploadBackgroundUseCase>(),
        gh<_i556.GetServicesUseCase>(),
      ),
    );
    gh.factory<_i853.BranchSelectionBloc>(
      () => _i853.BranchSelectionBloc(
        gh<_i641.GetBranchSelectionUseCase>(),
        gh<_i634.SaveBranchSelectionUseCase>(),
        gh<_i639.GetSavedBranchSelectionUseCase>(),
        gh<_i633.RemoveBranchSelectionUseCase>(),
        gh<_i643.GetProvinceUseCase>(),
        gh<_i636.GetFloorBranchSelectionUseCase>(),
      ),
    );
    gh.factory<_i854.StaffEvaluationPeriodsBloc>(
      () => _i854.StaffEvaluationPeriodsBloc(
        gh<_i507.GetStaffEvaluationPeriodsUseCase>(),
        gh<_i505.SaveStaffEvaluationPeriodsUseCase>(),
        gh<_i506.GetSavedStaffEvaluationPeriodsUseCase>(),
        gh<_i504.RemoveStaffEvaluationPeriodsUseCase>(),
      ),
    );
    gh.factory<_i855.SelectingOfficeBloc>(
      () => _i855.SelectingOfficeBloc(gh<_i596.GetDepartmentUseCase>()),
    );
    gh.factory<_i856.GetChoicesUseCase>(
      () => _i856.GetChoicesUseCase(gh<_i398.CheckinRepository>()),
    );
    gh.factory<_i857.RequestUpdateHistoryCheckinUseCase>(
      () => _i857.RequestUpdateHistoryCheckinUseCase(
        gh<_i398.CheckinRepository>(),
      ),
    );
    gh.factory<_i858.GetBranchesUseCase>(
      () => _i858.GetBranchesUseCase(gh<_i398.CheckinRepository>()),
    );
    gh.factory<_i859.GetCheckinTypesUseCase>(
      () => _i859.GetCheckinTypesUseCase(gh<_i398.CheckinRepository>()),
    );
    gh.factory<_i860.GetMonthlyHistoryCheckinUseCase>(
      () =>
          _i860.GetMonthlyHistoryCheckinUseCase(gh<_i398.CheckinRepository>()),
    );
    gh.factory<_i861.KpiEmployeeBloc>(
      () => _i861.KpiEmployeeBloc(
        gh<_i706.GetKpiEmployeeUseCase>(),
        gh<_i705.GetKpiEmployeeDetailUseCase>(),
      ),
    );
    gh.factory<_i862.UserListBloc>(
      () => _i862.UserListBloc(
        gh<_i846.SaveUserListUseCase>(),
        gh<_i843.GetSavedUserListUseCase>(),
        gh<_i845.RemoveUserListUseCase>(),
        gh<_i183.UserLoadCreateChatGroupUseCase>(),
        gh<_i291.UpdateGroupChatDetailUseCase>(),
      ),
    );
    gh.factory<_i863.BedSelectionBloc>(
      () => _i863.BedSelectionBloc(
        gh<_i642.GetRoomBranchSelectionUseCase>(),
        gh<_i636.GetFloorBranchSelectionUseCase>(),
        gh<_i640.GetBedBranchSelectionUseCase>(),
        gh<_i635.BedSelectBranchSelectionUseCase>(),
        gh<_i637.BedChangeBranchSelectionUseCase>(),
        gh<_i632.EmployeeGetBranchSelectionUseCase>(),
        gh<_i638.EstimateTimeGetBranchSelectionUseCase>(),
      ),
    );
    gh.factory<_i864.CreatingTaskBloc>(
      () => _i864.CreatingTaskBloc(
        gh<_i400.CreatingTaskUseCase>(),
        gh<_i784.UploadUseCase>(),
      ),
    );
    gh.factory<_i865.AssignPxRecheckUpdateUseCase>(
      () => _i865.AssignPxRecheckUpdateUseCase(gh<_i387.PxRecheckRepository>()),
    );
    gh.factory<_i866.RefuseBloc>(
      () => _i866.RefuseBloc(gh<_i603.RejectEformUseCase>()),
    );
    gh.factory<_i867.NotificationsBloc>(
      () => _i867.NotificationsBloc(
        gh<_i651.GetNotificationsUseCase>(),
        gh<_i650.SearchNotificationsUseCase>(),
        gh<_i652.ReadAllNotificationUseCase>(),
      ),
    );
    gh.factory<_i868.ConfirmOTPBloc>(
      () => _i868.ConfirmOTPBloc(
        gh<_i778.ConfirmOtpUseCase>(),
        gh<_i767.GetOtpUseCase>(),
        gh<_i242.GetKeyAppsFlyerUseCase>(),
      ),
    );
    gh.factory<_i869.GetStatusListCustomerUseCase>(
      () => _i869.GetStatusListCustomerUseCase(
        gh<_i428.ListCustomerRepository>(),
      ),
    );
    gh.factory<_i870.GetSavedListCustomerUseCase>(
      () =>
          _i870.GetSavedListCustomerUseCase(gh<_i428.ListCustomerRepository>()),
    );
    gh.factory<_i871.GetListCustomerUseCase>(
      () => _i871.GetListCustomerUseCase(gh<_i428.ListCustomerRepository>()),
    );
    gh.factory<_i872.SaveListCustomerUseCase>(
      () => _i872.SaveListCustomerUseCase(gh<_i428.ListCustomerRepository>()),
    );
    gh.factory<_i873.RemoveListCustomerUseCase>(
      () => _i873.RemoveListCustomerUseCase(gh<_i428.ListCustomerRepository>()),
    );
    gh.factory<_i874.SearchListCustomerUseCase>(
      () => _i874.SearchListCustomerUseCase(gh<_i428.ListCustomerRepository>()),
    );
    gh.factory<_i875.RemoveMedicalTemplateListUseCase>(
      () => _i875.RemoveMedicalTemplateListUseCase(
        gh<_i677.MedicalTemplateListRepository>(),
      ),
    );
    gh.factory<_i876.MedicalTemplateDetailGetMedicalTemplateListUseCase>(
      () => _i876.MedicalTemplateDetailGetMedicalTemplateListUseCase(
        gh<_i677.MedicalTemplateListRepository>(),
      ),
    );
    gh.factory<_i877.GetMedicalTemplateListUseCase>(
      () => _i877.GetMedicalTemplateListUseCase(
        gh<_i677.MedicalTemplateListRepository>(),
      ),
    );
    gh.factory<_i878.GetSavedMedicalTemplateListUseCase>(
      () => _i878.GetSavedMedicalTemplateListUseCase(
        gh<_i677.MedicalTemplateListRepository>(),
      ),
    );
    gh.factory<_i879.SaveMedicalTemplateListUseCase>(
      () => _i879.SaveMedicalTemplateListUseCase(
        gh<_i677.MedicalTemplateListRepository>(),
      ),
    );
    gh.factory<_i880.HaPointListGetMedicalLogDetailUseCase>(
      () => _i880.HaPointListGetMedicalLogDetailUseCase(
        gh<_i511.MedicalLogDetailRepository>(),
      ),
    );
    gh.factory<_i881.DoctorListGetMedicalLogDetailUseCase>(
      () => _i881.DoctorListGetMedicalLogDetailUseCase(
        gh<_i511.MedicalLogDetailRepository>(),
      ),
    );
    gh.factory<_i882.GetTattooColorMedicalLogDetailUseCase>(
      () => _i882.GetTattooColorMedicalLogDetailUseCase(
        gh<_i511.MedicalLogDetailRepository>(),
      ),
    );
    gh.factory<_i883.DosageListGetMedicalLogDetailUseCase>(
      () => _i883.DosageListGetMedicalLogDetailUseCase(
        gh<_i511.MedicalLogDetailRepository>(),
      ),
    );
    gh.factory<_i884.MedicineListGetMedicalLogDetailUseCase>(
      () => _i884.MedicineListGetMedicalLogDetailUseCase(
        gh<_i511.MedicalLogDetailRepository>(),
      ),
    );
    gh.factory<_i885.UpdateLogMedicalDetailUseCase>(
      () => _i885.UpdateLogMedicalDetailUseCase(
        gh<_i511.MedicalLogDetailRepository>(),
      ),
    );
    gh.factory<_i886.RemoveMedicalLogDetailUseCase>(
      () => _i886.RemoveMedicalLogDetailUseCase(
        gh<_i511.MedicalLogDetailRepository>(),
      ),
    );
    gh.factory<_i887.GetTattooTimeMedicalLogDetailUseCase>(
      () => _i887.GetTattooTimeMedicalLogDetailUseCase(
        gh<_i511.MedicalLogDetailRepository>(),
      ),
    );
    gh.factory<_i888.KhacnhoListGetMedicalLogDetailUseCase>(
      () => _i888.KhacnhoListGetMedicalLogDetailUseCase(
        gh<_i511.MedicalLogDetailRepository>(),
      ),
    );
    gh.factory<_i889.GetOriginStatusMedicalLogDetailUseCase>(
      () => _i889.GetOriginStatusMedicalLogDetailUseCase(
        gh<_i511.MedicalLogDetailRepository>(),
      ),
    );
    gh.factory<_i890.GetSavedMedicalLogDetailUseCase>(
      () => _i890.GetSavedMedicalLogDetailUseCase(
        gh<_i511.MedicalLogDetailRepository>(),
      ),
    );
    gh.factory<_i891.CreateLogMedicalDetailUseCase>(
      () => _i891.CreateLogMedicalDetailUseCase(
        gh<_i511.MedicalLogDetailRepository>(),
      ),
    );
    gh.factory<_i892.GetSkinMachineMedicalLogDetailUseCase>(
      () => _i892.GetSkinMachineMedicalLogDetailUseCase(
        gh<_i511.MedicalLogDetailRepository>(),
      ),
    );
    gh.factory<_i893.GetMedicalLogDetailUseCase>(
      () => _i893.GetMedicalLogDetailUseCase(
        gh<_i511.MedicalLogDetailRepository>(),
      ),
    );
    gh.factory<_i894.GetPostSaiMedicalLogDetailUseCase>(
      () => _i894.GetPostSaiMedicalLogDetailUseCase(
        gh<_i511.MedicalLogDetailRepository>(),
      ),
    );
    gh.factory<_i895.SaveMedicalLogDetailUseCase>(
      () => _i895.SaveMedicalLogDetailUseCase(
        gh<_i511.MedicalLogDetailRepository>(),
      ),
    );
    gh.factory<_i896.EformCategoryBloc>(
      () => _i896.EformCategoryBloc(gh<_i602.GetEformRequestTypeUseCase>()),
    );
    gh.factory<_i897.NoteFinishPxRecheckUseCase>(
      () => _i897.NoteFinishPxRecheckUseCase(gh<_i387.PxRecheckRepository>()),
    );
    gh.factory<_i898.WorkStatusUpdatePxRecheckUseCase>(
      () => _i898.WorkStatusUpdatePxRecheckUseCase(
        gh<_i387.PxRecheckRepository>(),
      ),
    );
    gh.factory<_i899.AssignsFetchPxRecheckUseCase>(
      () => _i899.AssignsFetchPxRecheckUseCase(gh<_i387.PxRecheckRepository>()),
    );
    gh.factory<_i900.JobSchedulerBloc>(
      () => _i900.JobSchedulerBloc(
        gh<_i404.GetGeneralJobSchedulerUseCase>(),
        gh<_i402.GetJobSchedulerUseCase>(),
      ),
    );
    gh.factory<_i901.CommentListBloc>(
      () => _i901.CommentListBloc(
        gh<_i847.GetCommentListUseCase>(),
        gh<_i850.CommentUploadFileUseCase>(),
        gh<_i848.PostCommentUseCase>(),
        gh<_i392.PostLikeCommentUseCase>(),
        gh<_i679.UpdateCommentUseCase>(),
        gh<_i849.DeleteCommentUseCase>(),
        gh<_i272.GetTagListUseCase>(),
      ),
    );
    gh.factory<_i902.ListCustomerBloc>(
      () => _i902.ListCustomerBloc(
        gh<_i871.GetListCustomerUseCase>(),
        gh<_i872.SaveListCustomerUseCase>(),
        gh<_i870.GetSavedListCustomerUseCase>(),
        gh<_i873.RemoveListCustomerUseCase>(),
        gh<_i869.GetStatusListCustomerUseCase>(),
        gh<_i874.SearchListCustomerUseCase>(),
        gh<_i697.CheckoutCustomerInfoDetailsUseCase>(),
      ),
    );
    gh.factory<_i903.StoryListBloc>(
      () => _i903.StoryListBloc(
        gh<_i460.GetStoryListUseCase>(),
        gh<_i462.PostStoryUseCase>(),
        gh<_i391.PostLikeStoryUseCase>(),
        gh<_i465.UpdateStoryUseCase>(),
        gh<_i469.DeleteStoryUseCase>(),
        gh<_i464.GetTotalNotificationUseCase>(),
        gh<_i461.GetStoryListSearchUseCase>(),
        gh<_i463.PutStoryVoteUseCase>(),
        gh<_i468.DeleteStoryVoteUseCase>(),
        gh<_i467.GetVoteUsersUseCase>(),
      ),
    );
    gh.factory<_i904.DropDownStatusBloc>(
      () => _i904.DropDownStatusBloc(gh<_i447.GetDropDownStatusUseCase>()),
    );
    gh.factory<_i905.BranchChatListBloc>(
      () => _i905.BranchChatListBloc(
        gh<_i687.GetBranchChatListUseCase>(),
        gh<_i690.SaveBranchChatListUseCase>(),
        gh<_i688.GetSavedBranchChatListUseCase>(),
        gh<_i689.RemoveBranchChatListUseCase>(),
      ),
    );
    gh.factory<_i906.GetServiceAndProductActionsUseCase>(
      () => _i906.GetServiceAndProductActionsUseCase(
        gh<_i502.ServiceAndProductRepository>(),
      ),
    );
    gh.factory<_i907.GetServiceAndProductUseCase>(
      () => _i907.GetServiceAndProductUseCase(
        gh<_i502.ServiceAndProductRepository>(),
      ),
    );
    gh.factory<_i908.SaveServiceAndProductUseCase>(
      () => _i908.SaveServiceAndProductUseCase(
        gh<_i502.ServiceAndProductRepository>(),
      ),
    );
    gh.factory<_i909.ServicesGetServiceAndProductUseCase>(
      () => _i909.ServicesGetServiceAndProductUseCase(
        gh<_i502.ServiceAndProductRepository>(),
      ),
    );
    gh.factory<_i910.ProductsGetServiceAndProductUseCase>(
      () => _i910.ProductsGetServiceAndProductUseCase(
        gh<_i502.ServiceAndProductRepository>(),
      ),
    );
    gh.factory<_i911.RemoveServiceAndProductUseCase>(
      () => _i911.RemoveServiceAndProductUseCase(
        gh<_i502.ServiceAndProductRepository>(),
      ),
    );
    gh.factory<_i912.GetSavedServiceAndProductUseCase>(
      () => _i912.GetSavedServiceAndProductUseCase(
        gh<_i502.ServiceAndProductRepository>(),
      ),
    );
    gh.factory<_i913.GetCategoryServiceAndProductUseCase>(
      () => _i913.GetCategoryServiceAndProductUseCase(
        gh<_i502.ServiceAndProductRepository>(),
      ),
    );
    gh.factory<_i914.TypeApprovingBloc>(
      () => _i914.TypeApprovingBloc(
        gh<_i600.ApprovingSignalEformUseCase>(),
        gh<_i784.UploadUseCase>(),
      ),
    );
    gh.factory<_i915.MedicalTemplateListBloc>(
      () => _i915.MedicalTemplateListBloc(
        gh<_i877.GetMedicalTemplateListUseCase>(),
        gh<_i879.SaveMedicalTemplateListUseCase>(),
        gh<_i878.GetSavedMedicalTemplateListUseCase>(),
        gh<_i875.RemoveMedicalTemplateListUseCase>(),
        gh<_i876.MedicalTemplateDetailGetMedicalTemplateListUseCase>(),
      ),
    );
    gh.factory<_i916.ProductConfirmBloc>(
      () => _i916.ProductConfirmBloc(
        gh<_i590.GetProductConfirmUseCase>(),
        gh<_i587.GetProductConfirmBranchUseCase>(),
      ),
    );
    gh.factory<_i917.SelectingStaffBloc>(
      () => _i917.SelectingStaffBloc(gh<_i598.GetStaffUseCase>()),
    );
    gh.factory<_i918.FunctionRoomBloc>(
      () => _i918.FunctionRoomBloc(gh<_i597.GetFunctionRoomUseCase>()),
    );
    gh.factory<_i919.ListFetchByStaffConsultationManagerUseCase>(
      () => _i919.ListFetchByStaffConsultationManagerUseCase(
        gh<_i644.ConsultationManagerRepository>(),
      ),
    );
    gh.factory<_i920.RoomFetchConsultationManagerUseCase>(
      () => _i920.RoomFetchConsultationManagerUseCase(
        gh<_i644.ConsultationManagerRepository>(),
      ),
    );
    gh.factory<_i921.GetConsultationManagerUseCase>(
      () => _i921.GetConsultationManagerUseCase(
        gh<_i644.ConsultationManagerRepository>(),
      ),
    );
    gh.factory<_i922.RemoveConsultationManagerUseCase>(
      () => _i922.RemoveConsultationManagerUseCase(
        gh<_i644.ConsultationManagerRepository>(),
      ),
    );
    gh.factory<_i923.SaveConsultationManagerUseCase>(
      () => _i923.SaveConsultationManagerUseCase(
        gh<_i644.ConsultationManagerRepository>(),
      ),
    );
    gh.factory<_i924.DeleteServiceAssignUseCase>(
      () => _i924.DeleteServiceAssignUseCase(
        gh<_i644.ConsultationManagerRepository>(),
      ),
    );
    gh.factory<_i925.AssignUpdateUseCase>(
      () =>
          _i925.AssignUpdateUseCase(gh<_i644.ConsultationManagerRepository>()),
    );
    gh.factory<_i926.BedAssignConsultationManagerUseCase>(
      () => _i926.BedAssignConsultationManagerUseCase(
        gh<_i644.ConsultationManagerRepository>(),
      ),
    );
    gh.factory<_i927.GetSavedConsultationManagerUseCase>(
      () => _i927.GetSavedConsultationManagerUseCase(
        gh<_i644.ConsultationManagerRepository>(),
      ),
    );
    gh.factory<_i928.BedFetchConsultationManagerUseCase>(
      () => _i928.BedFetchConsultationManagerUseCase(
        gh<_i644.ConsultationManagerRepository>(),
      ),
    );
    gh.factory<_i929.DeleteServiceCustomerUseCase>(
      () => _i929.DeleteServiceCustomerUseCase(
        gh<_i644.ConsultationManagerRepository>(),
      ),
    );
    gh.factory<_i930.GetCustomerConsultationManagerUseCase>(
      () => _i930.GetCustomerConsultationManagerUseCase(
        gh<_i644.ConsultationManagerRepository>(),
      ),
    );
    gh.factory<_i931.SelectPxRoomBloc>(
      () => _i931.SelectPxRoomBloc(
        gh<_i832.GetRoomListCustomerUseCase>(),
        gh<_i427.SaveSelectPxRoomUseCase>(),
        gh<_i424.RemoveSelectPxRoomUseCase>(),
        gh<_i833.SaveCustomerRoomCodeUseCase>(),
        gh<_i425.RoomChangeSelectPxRoomUseCase>(),
      ),
    );
    gh.factory<_i932.EmployeesInRoomUseCase>(
      () =>
          _i932.EmployeesInRoomUseCase(gh<_i552.PxUnasignedUpdateRepository>()),
    );
    gh.factory<_i933.EmployeesFetchPxUnasignedUpdateUseCase>(
      () => _i933.EmployeesFetchPxUnasignedUpdateUseCase(
        gh<_i552.PxUnasignedUpdateRepository>(),
      ),
    );
    gh.factory<_i934.AssignPxUnasignedUpdateUseCase>(
      () => _i934.AssignPxUnasignedUpdateUseCase(
        gh<_i552.PxUnasignedUpdateRepository>(),
      ),
    );
    gh.factory<_i935.WorksFetchPxUnasignedUpdateUseCase>(
      () => _i935.WorksFetchPxUnasignedUpdateUseCase(
        gh<_i552.PxUnasignedUpdateRepository>(),
      ),
    );
    gh.factory<_i936.GetPxUnasignedUpdateUseCase>(
      () => _i936.GetPxUnasignedUpdateUseCase(
        gh<_i552.PxUnasignedUpdateRepository>(),
      ),
    );
    gh.factory<_i937.SavePxUnasignedUpdateUseCase>(
      () => _i937.SavePxUnasignedUpdateUseCase(
        gh<_i552.PxUnasignedUpdateRepository>(),
      ),
    );
    gh.factory<_i938.RemovePxUnasignedUpdateUseCase>(
      () => _i938.RemovePxUnasignedUpdateUseCase(
        gh<_i552.PxUnasignedUpdateRepository>(),
      ),
    );
    gh.factory<_i939.GetSavedPxUnasignedUpdateUseCase>(
      () => _i939.GetSavedPxUnasignedUpdateUseCase(
        gh<_i552.PxUnasignedUpdateRepository>(),
      ),
    );
    gh.factory<_i940.HomeBloc>(
      () => _i940.HomeBloc(
        gh<_i556.GetServicesUseCase>(),
        gh<_i829.CheckLeaderHomeUseCase>(),
        gh<_i830.WorkLeaderCheckHomeUseCase>(),
        gh<_i832.GetRoomListCustomerUseCase>(),
      ),
    );
    gh.factory<_i941.MedicineDetailBloc>(
      () => _i941.MedicineDetailBloc(
        gh<_i724.GetMedicineDetailUseCase>(),
        gh<_i725.SaveMedicineDetailUseCase>(),
        gh<_i723.GetSavedMedicineDetailUseCase>(),
        gh<_i719.RemoveMedicineDetailUseCase>(),
        gh<_i720.GetUnitMedicineDetailUseCase>(),
      ),
    );
    gh.factory<_i942.TicketBloc>(
      () => _i942.TicketBloc(
        gh<_i437.GetTicketTicketv2UseCase>(),
        gh<_i434.TicketCreatedTypeUseCase>(),
        gh<_i439.GetMyTicketTicketv2UseCase>(),
        gh<_i438.TicketUploadFileUseCase>(),
        gh<_i436.CreateTicketUseCase>(),
        gh<_i442.TicketGroupTypeUseCase>(),
        gh<_i440.UpdateTicketUseCase>(),
        gh<_i610.GetTicketActiveUseCase>(),
        gh<_i435.GetTicketAllGroupUseCase>(),
        gh<_i441.GetTicketAllTypeUseCase>(),
      ),
    );
    gh.factory<_i943.OrderFoodServiceBloc>(
      () => _i943.OrderFoodServiceBloc(
        gh<_i325.OrderFoodUploadUseCase>(),
        gh<_i530.OrderFoodCreateReportUseCase>(),
      ),
    );
    gh.factory<_i944.DetailNotificationBloc>(
      () => _i944.DetailNotificationBloc(
        gh<_i648.GetDetailNotificationUseCase>(),
      ),
    );
    gh.factory<_i945.GetListSupportRequestUseCase>(
      () => _i945.GetListSupportRequestUseCase(gh<_i691.RequestRepository>()),
    );
    gh.factory<_i946.SendSupportRequestUseCase>(
      () => _i946.SendSupportRequestUseCase(gh<_i691.RequestRepository>()),
    );
    gh.factory<_i947.EditHomeMenuBloc>(
      () => _i947.EditHomeMenuBloc(gh<_i556.GetServicesUseCase>()),
    );
    gh.factory<_i948.MedicalServiceLogListBloc>(
      () => _i948.MedicalServiceLogListBloc(
        gh<_i825.GetMedicalServiceLogListUseCase>(),
        gh<_i823.SaveMedicalServiceLogListUseCase>(),
        gh<_i826.GetSavedMedicalServiceLogListUseCase>(),
        gh<_i824.RemoveMedicalServiceLogListUseCase>(),
      ),
    );
    gh.factory<_i949.UserTicketBloc>(
      () => _i949.UserTicketBloc(
        gh<_i448.GetUserTicketUseCase>(),
        gh<_i785.GetUserInfoUseCase>(),
      ),
    );
    gh.factory<_i950.CustomerListBloc>(
      () => _i950.CustomerListBloc(
        gh<_i755.GetCustomerListUseCase>(),
        gh<_i754.SaveCustomerListUseCase>(),
        gh<_i756.GetSavedCustomerListUseCase>(),
        gh<_i757.RemoveCustomerListUseCase>(),
        gh<_i758.GetCustomerRelationShipListUseCase>(),
      ),
    );
    gh.factory<_i951.CreateCustomerBloc>(
      () => _i951.CreateCustomerBloc(
        gh<_i581.GetCreateCustomerUseCase>(),
        gh<_i578.SaveCreateCustomerUseCase>(),
        gh<_i582.GetSavedCreateCustomerUseCase>(),
        gh<_i577.RemoveCreateCustomerUseCase>(),
        gh<_i575.GetProvinceCreateCustomerUseCase>(),
        gh<_i579.GetDistrictCreateCustomerUseCase>(),
        gh<_i580.GetWardCreateCustomerUseCase>(),
        gh<_i576.GetJobCreateCustomerUseCase>(),
        gh<_i481.GetChatSelectBranchUseCase>(),
        gh<_i583.UpdateCreateCustomerUseCase>(),
        gh<_i584.SurveyLoadCreateCustomerUseCase>(),
        gh<_i585.CustomerSearchCreateCustomerUseCase>(),
      ),
    );
    gh.factory<_i952.TicketDetailBloc>(
      () => _i952.TicketDetailBloc(
        gh<_i560.GetTicketDetailUseCase>(),
        gh<_i559.SaveTicketDetailUseCase>(),
        gh<_i562.GetSavedTicketDetailUseCase>(),
        gh<_i566.RemoveTicketDetailUseCase>(),
        gh<_i561.GetTicketDetailReasonUseCase>(),
        gh<_i565.ConfirmTicketDetailUseCase>(),
        gh<_i611.CreateTicketActiveUseCase>(),
        gh<_i563.ReceptTicketDetailUseCase>(),
        gh<_i438.TicketUploadFileUseCase>(),
        gh<_i610.GetTicketActiveUseCase>(),
        gh<_i564.TicketDetailReworkUseCase>(),
      ),
    );
    gh.factory<_i953.SetPasswordBloc>(
      () => _i953.SetPasswordBloc(gh<_i775.ResetPasswordUseCase>()),
    );
    gh.factory<_i954.CheckinPhotoBloc>(
      () => _i954.CheckinPhotoBloc(
        gh<_i680.GetCheckinPhotoUseCase>(),
        gh<_i682.SaveCheckinPhotoUseCase>(),
        gh<_i681.GetSavedCheckinPhotoUseCase>(),
        gh<_i683.RemoveCheckinPhotoUseCase>(),
      ),
    );
    gh.factory<_i955.StoryWriteBloc>(
      () => _i955.StoryWriteBloc(
        gh<_i470.GetStoryRuleUseCase>(),
        gh<_i466.SocialUploadFileUseCase>(),
        gh<_i465.UpdateStoryUseCase>(),
        gh<_i614.GetLocationGoogleUseCase>(),
        gh<_i615.GetAddressAddressLocationGoogleUseCase>(),
        gh<_i613.GetAddressNearLocationGoogleUseCase>(),
        gh<_i616.GetAddressSearchLocationGoogleUseCase>(),
        gh<_i459.GetEmojiListUseCase>(),
      ),
    );
    gh.factory<_i956.AuthenticationBloc>(
      () => _i956.AuthenticationBloc(
        gh<_i777.GetConfigurationUseCase>(),
        gh<_i791.HasTokenUseCase>(),
        gh<_i772.GetProfilesUseCase>(),
        gh<_i363.ClearCacheUseCase>(),
        gh<_i790.IsShowBoardingUseCase>(),
        gh<_i359.SetOnboardingUseCase>(),
        gh<_i673.CheckinPermissionCheckUserUseCase>(),
        gh<_i367.CheckinUseCase>(),
        gh<_i249.GetLatitudeUseCase>(),
        gh<_i252.GetLongitudeUseCase>(),
        gh<_i770.LogoutUseCase>(),
        gh<_i793.CacheUserUseCase>(),
        gh<_i666.UploadCheckInImageUseCase>(),
        gh<_i792.CacheLoginStringeeGetUseCase>(),
        gh<_i592.SocketAccessTokenGetLoginUseCase>(),
      ),
    );
    gh.factory<_i957.CustomerScheduleBloc>(
      () => _i957.CustomerScheduleBloc(
        gh<_i729.GetCustomerScheduleUseCase>(),
        gh<_i726.SaveCustomerScheduleUseCase>(),
        gh<_i727.GetSavedCustomerScheduleUseCase>(),
        gh<_i728.RemoveCustomerScheduleUseCase>(),
      ),
    );
    gh.factory<_i958.CustomerBloc>(
      () => _i958.CustomerBloc(
        gh<_i661.GetCustomerBookingInfoUseCase>(),
        gh<_i831.CheckinCustomerUseCase>(),
        gh<_i662.BookedServicesFetchCustomerBookingInfoUseCase>(),
        gh<_i832.GetRoomListCustomerUseCase>(),
        gh<_i834.PrintCustomerUseCase>(),
      ),
    );
    gh.factory<_i959.ApprovingOtpBloc>(
      () => _i959.ApprovingOtpBloc(gh<_i601.ApprovingOtpEformUseCase>()),
    );
    gh.factory<_i960.UserProfileBloc>(
      () => _i960.UserProfileBloc(gh<_i793.CacheUserUseCase>()),
    );
    gh.factory<_i961.DeleteAssignTaskUseCase>(
      () => _i961.DeleteAssignTaskUseCase(gh<_i653.AssignTaskRepository>()),
    );
    gh.factory<_i962.UpdateAssignTaskUseCase>(
      () => _i962.UpdateAssignTaskUseCase(gh<_i653.AssignTaskRepository>()),
    );
    gh.factory<_i963.GetSavedAssignTaskUseCase>(
      () => _i963.GetSavedAssignTaskUseCase(gh<_i653.AssignTaskRepository>()),
    );
    gh.factory<_i964.CreateAssignTaskUseCase>(
      () => _i964.CreateAssignTaskUseCase(gh<_i653.AssignTaskRepository>()),
    );
    gh.factory<_i965.SaveAssignTaskUseCase>(
      () => _i965.SaveAssignTaskUseCase(gh<_i653.AssignTaskRepository>()),
    );
    gh.factory<_i966.GetAssignTaskUseCase>(
      () => _i966.GetAssignTaskUseCase(gh<_i653.AssignTaskRepository>()),
    );
    gh.factory<_i967.RemoveAssignTaskUseCase>(
      () => _i967.RemoveAssignTaskUseCase(gh<_i653.AssignTaskRepository>()),
    );
    gh.factory<_i968.GetStaffAssignTaskUseCase>(
      () => _i968.GetStaffAssignTaskUseCase(gh<_i653.AssignTaskRepository>()),
    );
    gh.factory<_i969.CustomerInfoDetailsBloc>(
      () => _i969.CustomerInfoDetailsBloc(
        gh<_i696.GetCustomerInfoDetailsUseCase>(),
        gh<_i695.SaveCustomerInfoDetailsUseCase>(),
        gh<_i698.GetSavedCustomerInfoDetailsUseCase>(),
        gh<_i699.RemoveCustomerInfoDetailsUseCase>(),
        gh<_i697.CheckoutCustomerInfoDetailsUseCase>(),
      ),
    );
    gh.factory<_i970.LoginBloc>(
      () => _i970.LoginBloc(
        gh<_i767.GetOtpUseCase>(),
        gh<_i360.HasUserDataUseCase>(),
        gh<_i768.CheckPhoneUseCase>(),
        gh<_i769.LoginUseCase>(),
        gh<_i793.CacheUserUseCase>(),
        gh<_i780.LoginSocialUseCase>(),
        gh<_i592.SocketAccessTokenGetLoginUseCase>(),
        gh<_i675.CheckPermissionUserUseCase>(),
      ),
    );
    gh.factory<_i971.MonthlyHistoryCheckinBloc>(
      () => _i971.MonthlyHistoryCheckinBloc(
        gh<_i860.GetMonthlyHistoryCheckinUseCase>(),
      ),
    );
    gh.factory<_i972.ConsultationHistoryBloc>(
      () => _i972.ConsultationHistoryBloc(
        gh<_i710.GetConsultationHistoryCustomerProfileUseCase>(),
      ),
    );
    gh.factory<_i973.CustomerProfileBloc>(
      () => _i973.CustomerProfileBloc(
        gh<_i711.GetCustomerProfileUseCase>(),
        gh<_i708.SaveCustomerProfileUseCase>(),
        gh<_i712.GetSavedCustomerProfileUseCase>(),
        gh<_i709.RemoveCustomerProfileUseCase>(),
      ),
    );
    gh.factory<_i974.CustomerBookingInfoBloc>(
      () => _i974.CustomerBookingInfoBloc(
        gh<_i661.GetCustomerBookingInfoUseCase>(),
        gh<_i660.SaveCustomerBookingInfoUseCase>(),
        gh<_i663.GetSavedCustomerBookingInfoUseCase>(),
        gh<_i658.RemoveCustomerBookingInfoUseCase>(),
        gh<_i662.BookedServicesFetchCustomerBookingInfoUseCase>(),
        gh<_i657.SuggestServicesFetchCustomerBookingInfoUseCase>(),
        gh<_i659.ServiceDetailsLoadCustomerBookingInfoUseCase>(),
        gh<_i832.GetRoomListCustomerUseCase>(),
        gh<_i298.GetServiceInsideTicketUseCase>(),
      ),
    );
    gh.factory<_i975.AddTagsImageBloc>(
      () => _i975.AddTagsImageBloc(
        gh<_i629.GetAddTagsImageUseCase>(),
        gh<_i624.SaveAddTagsImageUseCase>(),
        gh<_i625.GetSavedAddTagsImageUseCase>(),
        gh<_i626.RemoveAddTagsImageUseCase>(),
        gh<_i630.GetImageListAddTagsImageUseCase>(),
        gh<_i832.GetRoomListCustomerUseCase>(),
        gh<_i755.GetCustomerListUseCase>(),
        gh<_i627.GetTagListAddTagsImageUseCase>(),
        gh<_i622.CreateImageTagAddTagsImageUseCase>(),
        gh<_i623.CreateMergeImageAddTagsImageUseCase>(),
      ),
    );
    gh.factory<_i976.RatingHumanBloc>(
      () => _i976.RatingHumanBloc(
        gh<_i717.GetRatingHumanUseCase>(),
        gh<_i715.GetQuestionDetailUseCase>(),
        gh<_i716.SaveRatingHumanUseCase>(),
        gh<_i714.SubmitRatingHumanUseCase>(),
      ),
    );
    gh.factory<_i977.ConsultationHistoryDetailBloc>(
      () => _i977.ConsultationHistoryDetailBloc(
        gh<_i707.CreateConsultationCustomerProfileUseCase>(),
        gh<_i713.UpdateConsultationCustomerProfileUseCase>(),
        gh<_i632.EmployeeGetBranchSelectionUseCase>(),
      ),
    );
    gh.factory<_i978.GeneralBloc>(
      () => _i978.GeneralBloc(
        gh<_i817.UploadRecordTakingCareCustomerUseCase>(),
        gh<_i793.CacheUserUseCase>(),
        gh<_i665.UploadFeedbackUseCase>(),
        gh<_i793.CacheUserUseCase>(),
        gh<_i743.SendFeedbackUseCase>(),
        gh<_i787.CacheQuickActionGetUseCase>(),
        gh<_i786.CacheQuickActionRemoveUseCase>(),
        gh<_i162.GetConversationByInviteIdChatListUseCase>(),
        gh<_i155.JoinGroupChatListUseCase>(),
        gh<_i372.UniversalQrScanUseCase>(),
      ),
    );
    gh.factory<_i979.PxUnasignedBloc>(
      () => _i979.PxUnasignedBloc(
        gh<_i760.GetPxCustomerListUseCase>(),
        gh<_i761.SavePxUnasignedUseCase>(),
        gh<_i762.GetSavedPxUnasignedUseCase>(),
        gh<_i759.RemovePxUnasignedUseCase>(),
      ),
    );
    gh.factory<_i980.DetailCrmCustomerBloc>(
      () => _i980.DetailCrmCustomerBloc(
        gh<_i793.CacheUserUseCase>(),
        gh<_i536.GetDetailCrmCustomerUseCase>(),
        gh<_i537.SaveDetailCrmCustomerUseCase>(),
        gh<_i542.GetSavedDetailCrmCustomerUseCase>(),
        gh<_i539.RemoveDetailCrmCustomerUseCase>(),
        gh<_i535.AdviceFetchDetailCrmCustomerUseCase>(),
        gh<_i534.ServiceFetchDetailCrmCustomerUseCase>(),
        gh<_i540.CallLogFetchDetailCrmCustomerUseCase>(),
        gh<_i549.MessageLogFetchDetailCrmCustomerUseCase>(),
        gh<_i543.BookingLogFetchDetailCrmCustomerUseCase>(),
        gh<_i547.AdviceTypeFetchDetailCrmCustomerUseCase>(),
        gh<_i546.AdviceUpdateDetailCrmCustomerUseCase>(),
        gh<_i541.BranchLoadDetailCrmCustomerUseCase>(),
        gh<_i533.PromotionLoadDetailCrmCustomerUseCase>(),
        gh<_i532.RoomLoadDetailCrmCustomerUseCase>(),
        gh<_i545.TimeLoadDetailCrmCustomerUseCase>(),
        gh<_i538.ServiceLoadDetailCrmCustomerUseCase>(),
        gh<_i550.NumberBookingLoadDetailCrmCustomerUseCase>(),
        gh<_i548.BookingDetailLoadDetailCrmCustomerUseCase>(),
        gh<_i544.BookDetailCrmCustomerUseCase>(),
        gh<_i551.BookingLoadDetailCrmCustomerUseCase>(),
      ),
    );
    gh.factory<_i981.FeedbackBloc>(
      () => _i981.FeedbackBloc(
        gh<_i781.SubmitFeedbackUseCase>(),
        gh<_i784.UploadUseCase>(),
        gh<_i793.CacheUserUseCase>(),
      ),
    );
    gh.factory<_i982.PxUnasignedUpdateBloc>(
      () => _i982.PxUnasignedUpdateBloc(
        gh<_i936.GetPxUnasignedUpdateUseCase>(),
        gh<_i937.SavePxUnasignedUpdateUseCase>(),
        gh<_i939.GetSavedPxUnasignedUpdateUseCase>(),
        gh<_i938.RemovePxUnasignedUpdateUseCase>(),
        gh<_i935.WorksFetchPxUnasignedUpdateUseCase>(),
        gh<_i933.EmployeesFetchPxUnasignedUpdateUseCase>(),
        gh<_i934.AssignPxUnasignedUpdateUseCase>(),
        gh<_i808.GetSectionTakingCareCustomerUseCase>(),
      ),
    );
    gh.factory<_i983.MedicalProductCreationBloc>(
      () => _i983.MedicalProductCreationBloc(
        gh<_i517.MedicalProductCreationUseCase>(),
        gh<_i516.SaveMedicalProductCreationUseCase>(),
        gh<_i514.GetSavedMedicalProductCreationUseCase>(),
        gh<_i515.RemoveMedicalProductCreationUseCase>(),
        gh<_i513.ProductsMedicalProductCreationUseCase>(),
        gh<_i906.GetServiceAndProductActionsUseCase>(),
      ),
    );
    gh.factory<_i984.ServiceDetailBloc>(
      () => _i984.ServiceDetailBloc(
        gh<_i808.GetSectionTakingCareCustomerUseCase>(),
        gh<_i324.GetTreatmentDetailUseCase>(),
        gh<_i304.CreateTreatmentDetailUseCase>(),
        gh<_i671.EmployeeFetchServiceDetailUseCase>(),
        gh<_i670.DoctorFetchServiceDetailUseCase>(),
        gh<_i309.GetTreatmentOMDetailUseCase>(),
        gh<_i296.CreateTreatmentOMDetailUseCase>(),
        gh<_i932.EmployeesInRoomUseCase>(),
        gh<_i306.GetTreatmentNoteUseCase>(),
        gh<_i316.UpdateTreatmentNoteUseCase>(),
        gh<_i310.GetResultOfFitUseCase>(),
        gh<_i318.UpdateResultOfFitUseCase>(),
        gh<_i297.DeleteResultOfFitUseCase>(),
        gh<_i936.GetPxUnasignedUpdateUseCase>(),
        gh<_i322.UpdateTreatmentDetailUseCase>(),
        gh<_i307.GetServiceUsageConsultationCustomerUseCase>(),
      ),
    );
    gh.factory<_i985.ActionAttendanceBloc>(
      () => _i985.ActionAttendanceBloc(
        gh<_i859.GetCheckinTypesUseCase>(),
        gh<_i857.RequestUpdateHistoryCheckinUseCase>(),
        gh<_i858.GetBranchesUseCase>(),
        gh<_i481.GetChatSelectBranchUseCase>(),
      ),
    );
    gh.factory<_i986.PxTaskListBloc>(
      () => _i986.PxTaskListBloc(
        gh<_i760.GetPxCustomerListUseCase>(),
        gh<_i524.SavePxTaskListUseCase>(),
        gh<_i523.GetSavedPxTaskListUseCase>(),
        gh<_i522.RemovePxTaskListUseCase>(),
        gh<_i808.GetSectionTakingCareCustomerUseCase>(),
        gh<_i899.AssignsFetchPxRecheckUseCase>(),
      ),
    );
    gh.factory<_i987.TagImageBloc>(
      () => _i987.TagImageBloc(
        gh<_i832.GetRoomListCustomerUseCase>(),
        gh<_i840.GetComboTagUsecase>(),
        gh<_i841.GetImageByComboTagUsecae>(),
        gh<_i839.DeleteImageByComboTagUsecase>(),
        gh<_i838.DeleteTagByComboTagUsecase>(),
      ),
    );
    gh.factory<_i988.CreateSupportRequestsBloc>(
      () => _i988.CreateSupportRequestsBloc(
        gh<_i946.SendSupportRequestUseCase>(),
      ),
    );
    gh.factory<_i989.MedicalLogDetailBloc>(
      () => _i989.MedicalLogDetailBloc(
        gh<_i893.GetMedicalLogDetailUseCase>(),
        gh<_i895.SaveMedicalLogDetailUseCase>(),
        gh<_i890.GetSavedMedicalLogDetailUseCase>(),
        gh<_i886.RemoveMedicalLogDetailUseCase>(),
        gh<_i891.CreateLogMedicalDetailUseCase>(),
        gh<_i885.UpdateLogMedicalDetailUseCase>(),
        gh<_i892.GetSkinMachineMedicalLogDetailUseCase>(),
        gh<_i894.GetPostSaiMedicalLogDetailUseCase>(),
        gh<_i784.UploadUseCase>(),
        gh<_i884.MedicineListGetMedicalLogDetailUseCase>(),
        gh<_i883.DosageListGetMedicalLogDetailUseCase>(),
        gh<_i880.HaPointListGetMedicalLogDetailUseCase>(),
        gh<_i888.KhacnhoListGetMedicalLogDetailUseCase>(),
        gh<_i632.EmployeeGetBranchSelectionUseCase>(),
        gh<_i881.DoctorListGetMedicalLogDetailUseCase>(),
        gh<_i889.GetOriginStatusMedicalLogDetailUseCase>(),
        gh<_i882.GetTattooColorMedicalLogDetailUseCase>(),
        gh<_i887.GetTattooTimeMedicalLogDetailUseCase>(),
        gh<_i721.CreateMedicineDetailUseCase>(),
        gh<_i722.UpdateMedicineDetailUseCase>(),
      ),
    );
    gh.factory<_i990.SettingBloc>(
      () => _i990.SettingBloc(
        gh<_i676.UserDeletionUseCase>(),
        gh<_i783.ChangePasswordUseCase>(),
        gh<_i669.UploadKYCUseCase>(),
        gh<_i672.SendKycPhotosSettingUseCase>(),
      ),
    );
    gh.factory<_i991.ConsultationCustomerBloc>(
      () => _i991.ConsultationCustomerBloc(
        gh<_i313.GetConsultationCustomerUseCase>(),
        gh<_i315.SaveConsultationCustomerUseCase>(),
        gh<_i311.GetSavedConsultationCustomerUseCase>(),
        gh<_i302.RemoveConsultationCustomerUseCase>(),
        gh<_i308.GetServiceConsultationCustomerUseCase>(),
        gh<_i317.GetActionConsultationCustomerUseCase>(),
        gh<_i320.CompleteConsultationCustomerUseCase>(),
        gh<_i303.ProductLoadConsultationCustomerUseCase>(),
        gh<_i296.CreateTreatmentOMDetailUseCase>(),
        gh<_i832.GetRoomListCustomerUseCase>(),
        gh<_i305.GetFitCustomerInfoUseCase>(),
        gh<_i314.UpdateFitCustomerInfoUseCase>(),
        gh<_i300.GetResultListOfFitUseCase>(),
        gh<_i298.GetServiceInsideTicketUseCase>(),
        gh<_i312.UpdateSkinCustomerInfoUseCase>(),
        gh<_i301.GetSkinCustomerInfoUseCase>(),
        gh<_i323.RemoveServiceConsultationCustomerUseCase>(),
        gh<_i321.EditServiceConsultationCustomerUseCase>(),
        gh<_i319.UpdateConsultationTTBDUseCase>(),
        gh<_i299.GetConsultationNDTVUseCase>(),
      ),
    );
    gh.factory<_i992.MedicalServiceCreationBloc>(
      () => _i992.MedicalServiceCreationBloc(
        gh<_i741.MedicalServiceCreationUseCase>(),
        gh<_i740.SaveMedicalServiceCreationUseCase>(),
        gh<_i739.GetSavedMedicalServiceCreationUseCase>(),
        gh<_i736.RemoveMedicalServiceCreationUseCase>(),
        gh<_i737.ServicesMedicalServiceCreationUseCase>(),
        gh<_i738.MethodsMedicalServiceCreationUseCase>(),
        gh<_i906.GetServiceAndProductActionsUseCase>(),
      ),
    );
    gh.factory<_i993.MoreBloc>(
      () => _i993.MoreBloc(
        gh<_i665.UploadFeedbackUseCase>(),
        gh<_i772.GetProfilesUseCase>(),
        gh<_i743.SendFeedbackUseCase>(),
      ),
    );
    gh.factory<_i994.PxRecheckBloc>(
      () => _i994.PxRecheckBloc(
        gh<_i760.GetPxCustomerListUseCase>(),
        gh<_i761.SavePxUnasignedUseCase>(),
        gh<_i762.GetSavedPxUnasignedUseCase>(),
        gh<_i759.RemovePxUnasignedUseCase>(),
        gh<_i899.AssignsFetchPxRecheckUseCase>(),
        gh<_i897.NoteFinishPxRecheckUseCase>(),
        gh<_i933.EmployeesFetchPxUnasignedUpdateUseCase>(),
        gh<_i865.AssignPxRecheckUpdateUseCase>(),
        gh<_i898.WorkStatusUpdatePxRecheckUseCase>(),
      ),
    );
    gh.factory<_i995.PxListBloc>(
      () => _i995.PxListBloc(
        gh<_i821.SavePxListUseCase>(),
        gh<_i819.GetSavedPxListUseCase>(),
        gh<_i822.RemovePxListUseCase>(),
      ),
    );
    gh.factory<_i996.HistoryCheckinBloc>(
      () => _i996.HistoryCheckinBloc(
        gh<_i857.RequestUpdateHistoryCheckinUseCase>(),
        gh<_i487.FetchHistoryCheckinUseCase>(),
      ),
    );
    gh.factory<_i997.SupportRequestsBloc>(
      () => _i997.SupportRequestsBloc(gh<_i945.GetListSupportRequestUseCase>()),
    );
    gh.factory<_i998.AssignTaskBloc>(
      () => _i998.AssignTaskBloc(
        gh<_i966.GetAssignTaskUseCase>(),
        gh<_i965.SaveAssignTaskUseCase>(),
        gh<_i963.GetSavedAssignTaskUseCase>(),
        gh<_i967.RemoveAssignTaskUseCase>(),
        gh<_i968.GetStaffAssignTaskUseCase>(),
        gh<_i964.CreateAssignTaskUseCase>(),
        gh<_i961.DeleteAssignTaskUseCase>(),
        gh<_i962.UpdateAssignTaskUseCase>(),
      ),
    );
    gh.factory<_i999.ServiceAndProductBloc>(
      () => _i999.ServiceAndProductBloc(
        gh<_i907.GetServiceAndProductUseCase>(),
        gh<_i908.SaveServiceAndProductUseCase>(),
        gh<_i912.GetSavedServiceAndProductUseCase>(),
        gh<_i911.RemoveServiceAndProductUseCase>(),
        gh<_i913.GetCategoryServiceAndProductUseCase>(),
        gh<_i909.ServicesGetServiceAndProductUseCase>(),
        gh<_i910.ProductsGetServiceAndProductUseCase>(),
      ),
    );
    gh.factory<_i1000.ConsultationManagerBloc>(
      () => _i1000.ConsultationManagerBloc(
        gh<_i921.GetConsultationManagerUseCase>(),
        gh<_i793.CacheUserUseCase>(),
        gh<_i820.GetPxListUseCase>(),
        gh<_i923.SaveConsultationManagerUseCase>(),
        gh<_i927.GetSavedConsultationManagerUseCase>(),
        gh<_i922.RemoveConsultationManagerUseCase>(),
        gh<_i930.GetCustomerConsultationManagerUseCase>(),
        gh<_i832.GetRoomListCustomerUseCase>(),
        gh<_i425.RoomChangeSelectPxRoomUseCase>(),
        gh<_i928.BedFetchConsultationManagerUseCase>(),
        gh<_i926.BedAssignConsultationManagerUseCase>(),
        gh<_i933.EmployeesFetchPxUnasignedUpdateUseCase>(),
        gh<_i925.AssignUpdateUseCase>(),
        gh<_i919.ListFetchByStaffConsultationManagerUseCase>(),
        gh<_i924.DeleteServiceAssignUseCase>(),
        gh<_i929.DeleteServiceCustomerUseCase>(),
      ),
    );
    return this;
  }
}

class _$RegisterModule extends _i1001.RegisterModule {}
