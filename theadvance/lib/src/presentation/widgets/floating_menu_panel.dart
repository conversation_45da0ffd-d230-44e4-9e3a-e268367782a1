// ignore_for_file: avoid_print

import 'package:ez_resources/ez_resources.dart';
import 'package:flutter/material.dart';

import '../../core/utils/screen_record_helper.dart';
import 'report_floating_action.dart';

enum PanelShape { rectangle, rounded }

enum DockType { inside, outside }

enum PanelState { open, closed }

class FloatingMenuPanel extends StatefulWidget {
  const FloatingMenuPanel({
    super.key,
    this.buttons,
    this.positionTop,
    this.positionLeft,
    this.borderColor,
    this.borderWidth,
    this.iconSize,
    this.panelIcon,
    this.size,
    this.borderRadius,
    this.panelState,
    this.panelOpenOffset,
    this.panelAnimDuration,
    this.panelAnimCurve,
    this.backgroundColor,
    this.contentColor,
    this.panelShape,
    this.dockType,
    this.dockOffset,
    this.dockAnimCurve,
    this.dockAnimDuration,
    required this.onPressed,
    //this.isOpen,
  });
  //final Function(bool) isOpen;
  final double? positionTop;
  final double? positionLeft;
  final Color? borderColor;
  final double? borderWidth;
  final double? size;
  final double? iconSize;
  final IconData? panelIcon;
  final BorderRadius? borderRadius;
  final Color? backgroundColor;
  final Color? contentColor;
  final PanelShape? panelShape;
  final PanelState? panelState;
  final double? panelOpenOffset;
  final int? panelAnimDuration;
  final Curve? panelAnimCurve;
  final DockType? dockType;
  final double? dockOffset;
  final int? dockAnimDuration;
  final Curve? dockAnimCurve;
  final List<Widget>? buttons;
  final Function(int) onPressed;

  @override
  // ignore: library_private_types_in_public_api
  _FloatBoxState createState() => _FloatBoxState();
}

class _FloatBoxState extends State<FloatingMenuPanel> {
  PanelState _panelState = PanelState.closed;

  // Default positions for the panel;
  double _positionTop = 0.0;
  double _positionLeft = 0.0;

  double _panOffsetTop = 0.0;
  double _panOffsetLeft = 0.0;

  // This is the animation duration for the panel movement, it's required to
  // dynamically change the speed depending on what the panel is being used for.
  // e.g: When panel opened or closed, the position should change in a different
  // speed than when the panel is being dragged;
  int _movementSpeed = 0;

  @override
  void initState() {
    _positionTop = widget.positionTop ?? 0;
    _positionLeft = widget.positionLeft ?? 0;

    super.initState();
  }

  final isRight = ValueNotifier(false);

  @override
  Widget build(final BuildContext context) {
    // Width and height of page is required for the dragging the panel;
    final double pageWidth = MediaQuery.of(context).size.width;
    final double pageHeight = MediaQuery.of(context).size.height;

    // Dock offset creates the boundary for the page depending on the DockType;
    final double dockOffset = widget.dockOffset ?? 20.0;

    // Widget size if the width of the panel;
    final double widgetSize = widget.size ?? 70.0;

    // **** METHODS ****

    // Dock boundary is calculated according to the dock offset and dock type.
    double dockBoundary() {
      if (widget.dockType != null && widget.dockType == DockType.inside) {
        // If it's an 'inside' type dock, dock offset will remain the same;
        return dockOffset;
      } else {
        // If it's an 'outside' type dock, dock offset will be inverted, hence
        // negative value;
        return -dockOffset;
      }
    }

    // border radius property of the WIDGET, else it will be set to the size of
    // widget to make all corners rounded.

    // Total buttons are required to calculate the height of the panel;
    double totalButtons() {
      if (widget.buttons == null) {
        return 0;
      } else {
        return widget.buttons!.length.toDouble();
      }
    }

    // Height of the panel according to the panel state;
    double panelHeight() {
      if (_panelState == PanelState.open) {
        // Panel height will be in multiple of total buttons, I have added "1"
        // digit height for each button to fix the overflow issue. Don't know
        // what's causing this, but adding "1" fixed the problem for now.
        return (widgetSize + (widgetSize + 1) * totalButtons()) +
            (widget.borderWidth ?? 0);
      } else {
        return widgetSize + (widget.borderWidth ?? 0) * 2;
      }
    }

    // Panel top needs to be recalculated while opening the panel, to make sure
    // the height doesn't exceed the bottom of the page;
    void calcPanelTop() {
      if (_positionTop + panelHeight() > pageHeight + dockBoundary()) {
        _positionTop = pageHeight - panelHeight() + dockBoundary();
      }
    }

    // Dock Left position when open;
    double openDockLeft() {
      if (_positionLeft < (pageWidth / 2)) {
        // If panel is docked to the left;
        isRight.value = false;
        return widget.panelOpenOffset ?? 30.0;
      } else {
        // If panel is docked to the right;
        isRight.value = true;
        return (pageWidth - widgetSize) - (widget.panelOpenOffset ?? 30.0);
      }
    }

    // Panel border is only enabled if the border width is greater than 0;

    // Force dock will dock the panel to it's nearest edge of the screen;
    void forceDock() {
      // Calculate the center of the panel;
      final double center = _positionLeft + (widgetSize / 2);

      // Set movement speed to the custom duration property or '300' default;
      _movementSpeed = widget.dockAnimDuration ?? 300;

      // Check if the position of center of the panel is less than half of the
      // page;
      if (center < pageWidth / 2) {
        // Dock to the left edge;
        _positionLeft = 32.0 + dockBoundary();
      } else {
        // Dock to the right edge;
        _positionLeft = (pageWidth - widgetSize) - dockBoundary()-32;
      }
    }

    return ValueListenableBuilder(
      valueListenable: isRight,
      builder: (final context, final vRight, final child) {
        return AnimatedPositioned(
          duration: Duration(milliseconds: _movementSpeed),
          top: _positionTop,
          left: _positionLeft - (vRight ? 82 : 0),
          curve: widget.dockAnimCurve ?? Curves.fastLinearToSlowEaseIn,
          child: Column(
            spacing: 8,
            crossAxisAlignment: vRight? CrossAxisAlignment.end 
            : CrossAxisAlignment.start,
            children: [
              Material(
                borderRadius: BorderRadius.circular(8),
                child: AnimatedSize(
                  duration: const Duration(milliseconds: 200),
                  child: _panelState == PanelState.open
                      ? SizedBox(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          child: Column(
                            spacing: 8,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: widget.buttons ?? []),
                        ),
                      )
                      : const SizedBox(),
                ),
              ),

              Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (_panelState != PanelState.open)
                    SizedBox(
                      width: 70,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          GestureDetector(
                            onTap: () {
                              ReportFloatingAction
                              .showVidRecControllerVN.value =
                                  false;
                            },
                            child: const Icon(Icons.cancel, color: Colors.grey),
                          ),
                        ],
                      ),
                    ),
                  ClipOval(
                    child: Container(
                      color: Theme.of(context).primaryColor,
                      width: widgetSize,
                      child: GestureDetector(
                        onPanEnd: (final event) {
                          setState(() {
                            forceDock();
                          });
                        },
                        onTapCancel: () {                           
                          isRight.value = false;
                        },
                        onPanStart: (final event) {
                          _panOffsetTop =
                              event.globalPosition.dy - _positionTop;
                          _panOffsetLeft =
                              event.globalPosition.dx - _positionLeft;
                        },
                        onPanUpdate: (final event) {
                          isRight.value = false;
                          setState(() {
                            // Close Panel if opened;
                            _panelState = PanelState.closed;

                            // Reset Movement Speed;
                            _movementSpeed = 0;

                            _positionTop =
                                event.globalPosition.dy - _panOffsetTop;
                            if (_positionTop < 0 + dockBoundary()) {
                              _positionTop = 0 + dockBoundary();
                           
                            }
                            if (_positionTop >
                                (pageHeight - panelHeight()) - dockBoundary()) {
                               
                              _positionTop =
                                  (pageHeight - panelHeight()) - dockBoundary();
                            }
                            _positionLeft =
                                event.globalPosition.dx - _panOffsetLeft;

                            if (_positionLeft < 0 + dockBoundary()) {
                            
                              _positionLeft = 0 + dockBoundary();
                            }
                            if (_positionLeft >
                                (pageWidth - widgetSize) - dockBoundary()) {
                              
                              _positionLeft =
                                  (pageWidth - widgetSize) - dockBoundary();
                            }
                          });
                        },
                        onTapDown: (final detail) {
                          setState(() {
                            // Set the animation speed to custom duration;
                            _movementSpeed = widget.panelAnimDuration ?? 200;

                            if (_panelState == PanelState.open) {
                              // If panel state is "open", set it to "closed";
                              _panelState = PanelState.closed;
                               isRight.value = false;
                              // Reset panel position, dock it to nearest edge;
                              forceDock();
                              //widget.isOpen(false);
                              //print("Float panel closed.");
                            } else {
                              // If panel state is "closed", set it to "open";
                              _panelState = PanelState.open;

                              // Set the left side position;
                              _positionLeft = openDockLeft();
                              //widget.isOpen(true);

                              calcPanelTop();
                            }
                          });
                        },
                        child: ValueListenableBuilder(
                          valueListenable:  ScreenRecordHelper
                          .instance.isRecordingVN,
                          builder: (final context, final vIsRecording, final child) {
                            return _FloatButton(
                              size: 70.0,
                              color: widget.contentColor ?? Colors.white,
                              iconSize: widget.iconSize ?? 24.0,
                              child:
                              vIsRecording ? Padding(
                                    padding: const EdgeInsets.all(8.0),
                                    child: EZResources.image(
                                        ImageParams(
                                          name: AppIcons.icCancel,
                                          color: Colors.white
                                        ),
                                      ),
                                  ): 
                               _panelState == PanelState.open
                                  ? Padding(
                                    padding: const EdgeInsets.all(8.0),
                                    child: EZResources.image(
                                        ImageParams(
                                          name: AppIcons.icCancel,
                                          color: Colors.white
                                        ),
                                      ),
                                  )
                                  : EZResources.image(
                                      ImageParams(
                                        name: AppIcons.icBugReport,
                                      ),
                                    ),
                            );
                          },
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }
}

class _FloatButton extends StatelessWidget {
  const _FloatButton({
    this.size,
    this.color,
    required this.child,
    this.iconSize,
  });
  final double? size;
  final Color? color;
  final Widget child;
  final double? iconSize;

  @override
  Widget build(final BuildContext context) {
    return Container(
      color: Colors.white.withValues(alpha: 0.0),
      width: size ?? 70.0,
      height: size ?? 70.0,
      child: child,
    );
  }
}
