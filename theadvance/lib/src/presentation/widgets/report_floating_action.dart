// Flutter imports:
// ignore_for_file: use_build_context_synchronously

// Dart imports:
import 'dart:io';

// Flutter imports:
import 'package:ez_resources/ez_resources.dart';
import 'package:flutter/material.dart';

// Package imports:
import 'package:ez_core/ez_core.dart';
import 'package:ez_intl/ez_intl.dart';
import 'package:video_compress/video_compress.dart';

// Project imports:
import '../../core/routes/app_router.dart';
import '../../core/utils/nd_utils.dart';
import '../../core/utils/screen_record_helper.dart';
import '../../injector/injector.dart';
import '../_blocs/general_bloc/general_bloc.dart';
import 'floating_menu_panel.dart';

class ReportFloatingAction extends StatelessWidget {
  const ReportFloatingAction({super.key});

  static ValueNotifier<bool> showVidRecControllerVN = ValueNotifier<bool>(
    false,
  );

  @override
  Widget build(final BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: showVidRecControllerVN,
      builder: (final _, final value, final ___) {
        if (!value) {
          return const SizedBox();
        }
        return ValueListenableBuilder(
          valueListenable: ScreenRecordHelper.instance.isRecordingVN,
          builder: (final _, final isScreenRecording, final __) {
            return FloatingMenuPanel(
              positionTop: MediaQuery.sizeOf(context).height * 0.35,
              backgroundColor: Theme.of(
                context,
              ).primaryColor.withValues(alpha: 0.8),
              panelIcon: isScreenRecording
                  ? Icons.video_camera_back_outlined
                  : Icons.bug_report_outlined,
              contentColor: isScreenRecording
                  ? Theme.of(context).colorScheme.error
                  : null,
              onPressed: (final index) async {},
              buttons: [
                GestureDetector(
                  onTap: ()async{
                    
                      final screenRecordHelper = ScreenRecordHelper.instance;
         
                  if (isScreenRecording) {
                    final path = await screenRecordHelper.stopRecord();
                    await Future.delayed(const Duration(milliseconds: 250));
                    if (path != null) {
                      inputVideoDesc(filePath: path);
                    }
                } else {
                    await screenRecordHelper.startRecord();
                  }
                  },
                  child: DecoratedBox(
                    decoration: BoxDecoration(
                      border: Border(
                        bottom: BorderSide(
                          color: Theme.of(context).dividerColor,
                        ),
                      ),
                    ),
                    child: SizedBox(
                      width: 170,
                      child: Padding(
                        padding: const EdgeInsets.only(left: 8, right: 8,
                         bottom: 8),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(context.l10n.recordScreen),
                            EZResources.image(
                              ImageParams(
                                name: AppIcons.icVideoCall,
                                size: const ImageSize.square(24),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
                GestureDetector(
                  onTap: (){
                    Alert.showPictureFeedback();
                  },
                  child: SizedBox(
                    width: 170,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(context.l10n.captionScreen),
                          EZResources.image(
                            ImageParams(
                              name: AppIcons.icCam,
                              size: const ImageSize.square(24),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Future<void> inputVideoDesc({required final String filePath}) async {
    final context = getIt<AppRouter>().navigatorKey.currentContext;
    if (context == null) {
      return;
    }

    final textController = TextEditingController();

    return showGeneralDialog<void>(
      context: context,
      pageBuilder: (final context, final _, final __) {
        return AlertDialog(
          title: Text(
            context.l10n.sendResponse,
            style: Theme.of(context).textTheme.titleMedium,
          ),
          content: TextField(
            controller: textController,
            autofocus: true,
            decoration: InputDecoration(
              hintText: context.l10n.pleaseInputDescription,
            ),
            style: Theme.of(context).textTheme.bodyLarge,
          ),
          actions: [
            TextButton(
              onPressed: () {
                Utils.deleteFileSync(filePath);
                Navigator.of(context).pop();
              },
              child: Text(context.l10n.cancel),
            ),
            TextButton(
              onPressed: () async {
                final compressedMedia = await VideoCompress.compressVideo(
                  filePath,
                  quality: VideoQuality.Res1920x1080Quality,
                );
                final file = compressedMedia?.file ?? File(filePath);
                context.read<GeneralBloc>().add(
                  GeneralSendFeedback(
                    isVideo: true,
                    description: textController.text,
                    bytes: file.readAsBytesSync(),
                    filePath: filePath,
                  ),
                );
                Navigator.of(context).pop();
              },
              child: Text(context.l10n.send),
            ),
          ],
        );
      },
    );
  }
}
