// Dart imports:
import 'dart:async';
import 'dart:io';
import 'dart:math';

// Flutter imports:
import 'package:cached_network_image/cached_network_image.dart';
import 'package:cached_video_player_plus/cached_video_player_plus.dart';
import 'package:flutter/material.dart';

// Package imports:
import 'package:audio_video_progress_bar/audio_video_progress_bar.dart';
import 'package:auto_route/auto_route.dart';
import 'package:ez_core/ez_core.dart';
import 'package:flick_video_player/flick_video_player.dart';
import 'package:video_player/video_player.dart';
import 'package:visibility_detector/visibility_detector.dart';

// Project imports:
import '../../../core/nd_progresshud/loading_widget.dart';
import '../../../core/routes/app_router.dart';
import '../../../core/utils/utils.dart';
import '../../../domain/entities/video_manager.dart';
import '../view/story_image_detail_page.dart';
import 'story_body.dart';

enum PlayerType { pause, play, buffering, completed }

class StoryVideo extends StatefulWidget {
  const StoryVideo({
    super.key,
    required this.urlVideo,
    required this.playerController,
    required this.idStory,
    required this.thumbnail,
    required this.fileName,
    this.isFullScreen = true,
    this.heightVideo,
    required this.originalWidth,
    required this.originalHeight,
    this.onStoryDetail,
  });
  final String idStory;
  final String urlVideo;
  final VideoPlayerController? playerController;
  final String thumbnail;
  final bool isFullScreen;
  final String fileName;
  final double? heightVideo;
  final int originalWidth;
  final int originalHeight;
  final GestureTapCallback? onStoryDetail;
  @override
  State<StoryVideo> createState() => _StoryVideoState();
}

class _StoryVideoState extends State<StoryVideo> {
  Timer? _time;
  final ValueNotifier<bool> isLoadingWhenBuffering = ValueNotifier(false);
  final ValueNotifier<PlayerType?> isLoadingWhenPlaying = ValueNotifier(null);
  final ValueNotifier<bool> _isPlayer = ValueNotifier(false);
  final ValueNotifier<bool> isLoadingBackground = ValueNotifier(false);
  final ValueNotifier<Duration> _position = ValueNotifier(Duration.zero);
  //late VideoPlayerController play2;
  bool isDispose = false;
  final ValueNotifier<double> aspectRatio = ValueNotifier(0);
  static const double iconSize = 40;
  late final CachedVideoPlayerPlus _playerCache;
  @override
  void initState() {
    super.initState();
    _playerCache = CachedVideoPlayerPlus.networkUrl(
      Uri.parse(widget.urlVideo),
      invalidateCacheIfOlderThan: const Duration(days: 14),
    );
    _playerCache.initialize().then((final value) {
      _playerCache.controller.addListener(_videoListener);
    });
    // _playerCache.controller = VideoPlayerController.file(File(''));
  }

  @override
  void dispose() {
    _playerCache.controller.removeListener(_videoListener);
    unawaited(_playerCache.controller.dispose());
    isDispose = true;
    _time?.cancel();
    if (Platform.isIOS) {
      if (!widget.isFullScreen) {
        StoryBody.listPlayer.removeLast();
      } else {
        StoryBody.listPlayer.removeWhere((final e) => e.id == widget.urlVideo);
      }
    }
    super.dispose();
  }

  void _videoListener() {
    _position.value = _playerCache.controller.value.position;
    if (_playerCache.controller.value.isCompleted) {
      isInitVideo.value = false;
      aspectRatio.value = 0;
      isLoadingWhenPlaying.value = PlayerType.completed;
    }
    isLoadingWhenBuffering.value = _playerCache.controller.value.isBuffering;

    if (_playerCache.controller.value.isPlaying) {
      isInitVideo.value = true;
      isLoadingWhenPlaying.value = PlayerType.play;
    }
    if (!_playerCache.controller.value.isPlaying) {
      isLoadingWhenPlaying.value = PlayerType.pause;
    }
  }

  ValueNotifier<bool> isInitVideo = ValueNotifier(false);
  @override
  Widget build(final BuildContext context) {
    return VisibilityDetector(
      key: Key(widget.idStory),
      onVisibilityChanged: (final info) {
        final visiblePercentage = info.visibleFraction * 100;
        if (_playerCache.isInitialized) {
          if (visiblePercentage <= 4 &&
              _playerCache.controller.value.isPlaying) {
            if (!isDispose) {
              _playerCache.controller.pause();
            }
          }
        }
      },
      child: Container(
        constraints: BoxConstraints(
          maxWidth: max(MediaQuery.sizeOf(context).width, 300),
          maxHeight: max(MediaQuery.sizeOf(context).height, 500),
        ),
        child: AspectRatio(
          aspectRatio: (widget.originalWidth / widget.originalHeight) > 0
              ? (widget.originalWidth / widget.originalHeight)
              : 1,
          child: GestureDetector(
            onTap: () async {
              if (_playerCache.controller.value.isPlaying) {
                openStoryVideoDetial();
              }
            },
            child: ValueListenableBuilder(
              valueListenable: aspectRatio,
              builder: (final context, final vAs, final child) {
                return vAs != 0
                    ? Stack(
                        children: [
                          Hero(
                            tag: widget.urlVideo,
                            child: FlickNativeVideoPlayer(
                              fit: BoxFit.cover,
                              videoPlayerController: _playerCache.controller,
                              aspectRatioWhenLoading:
                                  widget.originalWidth / widget.originalHeight,
                            ),
                          ),
                          if (widget.isFullScreen) _buildIsInitVideo(),
                          if (widget.isFullScreen)
                            ValueListenableBuilder(
                              valueListenable: StoryBody.isMute,
                              builder:
                                  (final context, final vMute, final child) {
                                    return Align(
                                      alignment: Alignment.bottomRight,
                                      child: IconButton(
                                        style: IconButton.styleFrom(
                                          shape: const CircleBorder(),
                                          backgroundColor: Colors.black
                                              .withValues(alpha: .2),
                                        ),
                                        onPressed: () async {
                                          StoryBody.isMute.value = !vMute;
                                          _playerCache.controller.setVolume(
                                            !StoryBody.isMute.value ? 1.0 : 0.0,
                                          );
                                        },
                                        icon: Icon(
                                          vMute
                                              ? Icons.volume_off
                                              : Icons.volume_up,
                                          color: Colors.white,
                                        ),
                                      ),
                                    );
                                  },
                            ),
                          ValueListenableBuilder<PlayerType?>(
                            valueListenable: isLoadingWhenPlaying,
                            builder:
                                (final context, final vIsPlaying, final child) {
                                  return vIsPlaying == PlayerType.completed
                                      ? Center(
                                          child: IconButton(
                                            onPressed: () {
                                              replayClickThumnail();
                                            },
                                            icon: const Icon(
                                              Icons.replay,
                                              color: Colors.white,
                                              size: iconSize,
                                            ),
                                          ),
                                        )
                                      : vIsPlaying == PlayerType.pause
                                      ? Center(
                                          child: IconButton(
                                            onPressed: () {
                                              if (Utils.isIOS) {
                                                pauseVideoPlaying();
                                              }
                                              _playerCache.controller.setVolume(
                                                StoryBody.isMute.value
                                                    ? 0
                                                    : 1.0,
                                              );
                                              _playerCache.controller.play();
                                            },
                                            icon: const Icon(
                                              Icons.play_circle,
                                              color: Colors.white,
                                              size: iconSize,
                                            ),
                                          ),
                                        )
                                      : const SizedBox();
                                },
                          ),
                        ],
                      )
                    : LayoutBuilder(
                        builder: (final context, final constraint) {
                          return Container(
                            constraints: BoxConstraints(
                              maxWidth: max(
                                MediaQuery.sizeOf(context).width,
                                300,
                              ),
                              maxHeight: max(
                                MediaQuery.sizeOf(context).height,
                                500,
                              ),
                            ),
                            child: AspectRatio(
                              aspectRatio:
                                  (widget.originalWidth /
                                          widget.originalHeight) >
                                      0
                                  ? (widget.originalWidth /
                                        widget.originalHeight)
                                  : 1,
                              child: Stack(
                                children: [
                                  Positioned.fill(
                                    child: GestureDetector(
                                      onTap: () async {
                                        {
                                          isLoadingBackground.value = true;
                                          if (_playerCache.isInitialized) {
                                            _playerCache.controller.setVolume(
                                              StoryBody.isMute.value ? 0 : 1,
                                            );
                                            aspectRatio.value = _playerCache
                                                .controller
                                                .value
                                                .aspectRatio;
                                            if (Utils.isIOS) {
                                              StoryBody.listPlayer.add(
                                                VideoManager(
                                                  widget.urlVideo,
                                                  _playerCache.controller,
                                                ),
                                              );
                                              pauseVideoPlaying();
                                            }

                                            isLoadingBackground.value = false;

                                            _playerCache.controller.play();
                                          }

                                          isInitVideo.value = true;
                                          isLoadingWhenPlaying.value =
                                              PlayerType.play;
                                        }
                                      },
                                      child: CachedNetworkImage(
                                        imageUrl: widget.thumbnail,
                                        fit: EzCoreUtils.isTablet
                                            ? BoxFit.contain
                                            : BoxFit.cover,
                                        errorWidget: (_, _, _) => const Center(
                                          child: Icon(Icons.error),
                                        ),
                                      ),
                                    ),
                                  ),
                                  Positioned.fill(
                                    child: Center(
                                      child: IconButton(
                                        onPressed: () async {
                                          isLoadingBackground.value = true;
                                          _playerCache =
                                              CachedVideoPlayerPlus.networkUrl(
                                                Uri.parse(widget.urlVideo),
                                                invalidateCacheIfOlderThan:
                                                    const Duration(days: 14),
                                              );
                                          _playerCache.controller.setVolume(
                                            StoryBody.isMute.value ? 0 : 1,
                                          );

                                          _playerCache.controller.addListener(
                                            _videoListener,
                                          );
                                          _playerCache.controller
                                              .initialize()
                                              .then((final value) {
                                                aspectRatio.value = _playerCache
                                                    .controller
                                                    .value
                                                    .aspectRatio;
                                                _playerCache.controller.play();
                                              })
                                              .whenComplete(() async {
                                                if (Utils.isIOS) {
                                                  StoryBody.listPlayer.add(
                                                    VideoManager(
                                                      widget.urlVideo,
                                                      _playerCache.controller,
                                                    ),
                                                  );
                                                  pauseVideoPlaying();
                                                }
                                                isLoadingBackground.value =
                                                    false;
                                                _playerCache.controller.play();
                                              });
                                          isInitVideo.value = true;
                                        },
                                        icon: const Icon(
                                          Icons.play_circle,
                                          color: Colors.white,
                                          size: iconSize,
                                        ),
                                      ),
                                    ),
                                  ),
                                  ValueListenableBuilder(
                                    valueListenable: isLoadingBackground,
                                    builder:
                                        (
                                          final context,
                                          final vIsLoading,
                                          final child,
                                        ) {
                                          return vIsLoading
                                              ? Positioned.fill(
                                                  child: ColoredBox(
                                                    color: Colors.black
                                                        .withValues(alpha: .3),
                                                    child: const Center(
                                                      child: LoadingWidget(),
                                                    ),
                                                  ),
                                                )
                                              : const SizedBox();
                                        },
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                      );
              },
            ),
          ),
        ),
      ),
    );
  }

  ValueListenableBuilder<bool> _buildIsInitVideo() {
    return ValueListenableBuilder(
      valueListenable: isInitVideo,
      builder: (final context, final vInit, final child) {
        return Positioned.fill(
          child: Center(
            child: !vInit
                ? _buildButtonReplay()
                : ValueListenableBuilder(
                    valueListenable: _isPlayer,
                    builder: (final context, final isPlayer, final child) {
                      return isPlayer
                          ? InkWell(
                              onTap: () {
                                _time?.cancel();
                                _isPlayer.value = !_isPlayer.value;
                                _time = Timer(
                                  const Duration(milliseconds: 3000),
                                  () {
                                    StoryImageDetailPage.isOpenOption.value =
                                        true;
                                    _isPlayer.value = false;
                                  },
                                );
                                if (!_isPlayer.value) {
                                  _time?.cancel();
                                }
                              },
                              child: _buildBody(),
                            )
                          : const SizedBox();
                    },
                  ),
          ),
        );
      },
    );
  }

  Widget _buildBody() {
    return SizedBox.expand(
      child: ColoredBox(
        color: Colors.black.withValues(alpha: .5),
        child: Stack(
          children: [
            Center(child: buildControllerVideo()),
            if (!widget.isFullScreen) buildVideoProgress(),
          ],
        ),
      ),
    );
  }

  Widget buildControllerVideo() {
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 300),
      transitionBuilder: (final child, final animation) {
        return FadeTransition(opacity: animation, child: child);
      },
      child: ValueListenableBuilder<bool>(
        valueListenable: isLoadingWhenBuffering,
        builder: (final context, final vIsLoading, final child) {
          return vIsLoading
              ? CircularProgressIndicator(color: Theme.of(context).primaryColor)
              : ValueListenableBuilder<PlayerType?>(
                  valueListenable: isLoadingWhenPlaying,
                  builder: (final context, final vIsPlaying, final child) {
                    return IconButton(
                      onPressed: () async {
                        if (vIsPlaying == PlayerType.play) {
                          _playerCache.controller.pause();
                        } else if (vIsPlaying == PlayerType.pause) {
                          if (Utils.isIOS) {
                            pauseVideoPlaying();
                          }
                          _playerCache.controller.setVolume(
                            StoryBody.isMute.value ? 0 : 1.0,
                          );
                          _playerCache.controller.play();
                        } else {
                          replayClickThumnail();
                        }
                      },
                      icon: vIsPlaying == PlayerType.play
                          ? const Icon(
                              Icons.pause_circle,
                              color: Colors.white,
                              size: iconSize,
                            )
                          : vIsPlaying == PlayerType.pause
                          ? const Icon(
                              Icons.play_circle,
                              color: Colors.white,
                              size: iconSize,
                            )
                          : const Icon(
                              Icons.replay,
                              color: Colors.white,
                              size: iconSize,
                            ),
                    );
                  },
                );
        },
      ),
    );
  }

  Future<void> replayClickThumnail() async {
    _playerCache.controller
        .initialize()
        .then((final value) {
          aspectRatio.value = _playerCache.controller.value.aspectRatio;
        })
        .whenComplete(() async {
          if (Utils.isIOS) {
            pauseVideoPlaying();
          }
          _playerCache.controller.play();
          isLoadingWhenPlaying.value = PlayerType.play;
        });
  }

  Widget _buildButtonReplay() {
    return ColoredBox(
      color: Colors.black.withValues(alpha: .5),
      child: Center(
        child: IconButton(
          onPressed: () async {
            _playerCache.controller
                .initialize()
                .then((final value) {
                  aspectRatio.value = _playerCache.controller.value.aspectRatio;

                  _isPlayer.value = true;
                  StoryImageDetailPage.isOpenOption.value = false;
                  _time?.cancel();
                  _time = Timer(const Duration(milliseconds: 3000), () {
                    StoryImageDetailPage.isOpenOption.value = true;
                    _isPlayer.value = false;
                  });
                })
                .whenComplete(() async {
                  if (Utils.isIOS) {
                    pauseVideoPlaying();
                  }
                  _playerCache.controller.play();
                });
            isInitVideo.value = true;
          },
          icon: const Icon(
            Icons.replay_outlined,
            color: Colors.white,
            size: iconSize,
          ),
        ),
      ),
    );
  }

  Widget buildVideoProgress() {
    return Align(
      alignment: Alignment.bottomCenter,
      child: ValueListenableBuilder(
        valueListenable: _position,
        builder: (final context, final vPosition, final child) {
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: ProgressBar(
              progress: vPosition,
              total: _playerCache.controller.value.duration,
              timeLabelPadding: 6,
              timeLabelTextStyle: Theme.of(
                context,
              ).textTheme.bodySmall?.copyWith(color: Colors.white),
              timeLabelLocation: TimeLabelLocation.sides,
              timeLabelType: TimeLabelType.totalTime,
              onSeek: (final val) async {
                _playerCache.controller.seekTo(
                  Duration(milliseconds: val.inMilliseconds),
                );
              },
              onDragStart: (final val) {
                _time?.cancel();
              },
              onDragEnd: () {
                _time = Timer(const Duration(milliseconds: 3000), () {
                  _isPlayer.value = false;
                });
              },
            ),
          );
        },
      ),
    );
  }

  Future<void> pauseVideoPlaying() async {
    if (StoryBody.listPlayer.length > 1) {
      final iWhere = StoryBody.listPlayer.indexWhere(
        (final e) => e.controller.value.isPlaying,
      );
      if (iWhere != -1) {
        if (StoryBody.listPlayer[iWhere].controller.value.isPlaying) {
          StoryBody.listPlayer[iWhere].controller.pause();
        }
      }
    }
  }

  Future<void> openStoryVideoDetial() async {
    context.router.push(
      StoryVideoDetailRoute(
        play2: _playerCache.controller,
        tag: 'storyVideoTag-${widget.idStory}',
        duration: _playerCache.controller.value.duration,
        thumbnail: widget.thumbnail,
        url: widget.urlVideo,
        fileName: widget.fileName,
        originalWidth: widget.originalWidth,
        originalHeight: widget.originalHeight,
        isLoadingWhenPlaying: isLoadingWhenPlaying,
      ),
    );
  }
}
