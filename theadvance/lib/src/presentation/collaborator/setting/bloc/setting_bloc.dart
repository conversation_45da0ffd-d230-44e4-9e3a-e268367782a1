// Dart imports:
import 'dart:io';

// Flutter imports:
import 'package:flutter/foundation.dart';

// Package imports:
import 'package:ez_core/ez_core.dart';
import 'package:ez_login_authentication/ez_login_authentication.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:injectable/injectable.dart';
import 'package:shorebird_code_push/shorebird_code_push.dart';

// Project imports:
import '../../../../core/params/request_params.dart';
import '../../../../core/utils/nd_utils.dart';
import '../../../../data/datasources/local/cache/hive/ez_cache.dart';
import '../../../../data/models/nd_models.dart';
import '../../../../domain/entities/media_upload_record.dart';
import '../../../../domain/usecases/media/upload_kyc_usecase.dart';
import '../../../../domain/usecases/user/change_password_usecase.dart';
import '../../../../domain/usecases/user/send_kyc_photos_setting_usecase.dart';
import '../../../../domain/usecases/user/user_deletion_usecase.dart';

// Project imports:

part 'setting_event.dart';
part 'setting_state.dart';

@injectable
class SettingBloc extends Bloc<SettingEvent, SettingState> {
  SettingBloc(
    this._userDeletionUseCase,
    this._changePasswordUseCase,
    this._uploadKYCUseCase,
    this._sendKycPhotosSettingUseCase,
  ) : super(SettingInitial()) {
    on<SettingStarted>((final event, final emit) async {
      try {
        final packageInfo = await PackageInfo.fromPlatform();
        final activeBiometrics =
            await EZSecureStorage.storage.read(
              key: KeyStorage.biometricsAuthen,
            ) !=
            null;
        final listBiometrics = await EZAuthentication.getAvailableBiometrics();
        final accountMenus = await EZCache.shared.accountMenus;
        emit(
          SettingLoadSuccess(
            activeBiometrics: activeBiometrics,
            version: '${packageInfo.version} (${packageInfo.buildNumber})',
            listBiometrics: listBiometrics,
            accountMenus: accountMenus,
          ),
        );
      } catch (_) {
        emit(SettingLoadFailure());
      }
    });

    on<SettingSwitched>((final event, final emit) async {
      try {
        if (event.currentValue) {
          await EZSecureStorage.storage.delete(
            key: KeyStorage.biometricsAuthen,
          );
        } else {
          await EZSecureStorage.storage.write(
            key: KeyStorage.biometricsAuthen,
            value: 'enable',
          );
        }
        emit(SettingSwitchSuccess(activeBiometrics: event.currentValue));
      } catch (e) {
        emit(SettingLoadFailure());
      }
    });

    on<SettingUserRemoved>(_removeUser);

    on<SettingChangedPassword>(_changedPassword);

    on<SettingHotfixChecked>(_onHotfixChecked);

    on<SettingUploadedKYC>(_onUploadedKYC);
  }

  final UserDeletionUseCase _userDeletionUseCase;
  final ChangePasswordUseCase _changePasswordUseCase;
  final UploadKYCUseCase _uploadKYCUseCase;
  final SendKycPhotosSettingUseCase _sendKycPhotosSettingUseCase;
  Future<void> _onHotfixChecked(
    final SettingHotfixChecked event,
    final Emitter<SettingState> emit,
  ) async {
    if (!kIsWeb) {
      emit(SettingInProgress());

      final shorebirdCodePush = ShorebirdCodePush();

      await shorebirdCodePush.downloadUpdateIfAvailable();

      // Check whether a patch is available to install.
      final isUpdateAvailable = await shorebirdCodePush
          .isNewPatchReadyToInstall();

      if (isUpdateAvailable) {
        emit(SettingShowHotfixDialog());
      } else {
        emit(SettingUpToDate());
      }
    }
  }

  Future<void> _onUploadedKYC(
    final SettingUploadedKYC event,
    final Emitter<SettingState> emit,
  ) async {
    emit(SettingInProgress());

    final List<MediaUpload?> uploadFileList = [];
    for (final element in event.paths) {
      final compressBytes = await FlutterImageCompress.compressWithList(
        File(element).readAsBytesSync(),
        quality: 70,
        minWidth: 960,
        minHeight: 1280,
      );
      final dataState = await _uploadKYCUseCase(
        params: MediaUploadRequestParams(
          MultipartFile.fromBytes(
            compressBytes,
            filename: element.split('/').lastOrNull,
          ),
        ),
      );
      if (dataState is DataSuccess) {
        uploadFileList.add(dataState.data);
      }
      if (dataState is DataFailure) {
        return;
      }
    }
    if (uploadFileList.length == event.paths.length) {
      final dataState = await _sendKycPhotosSettingUseCase(
        params: SettingSendKycPhotosRequestParams(
          uploadFileList.map((final e) => e?.link).toList(),
        ),
      );

      if (dataState is DataSuccess) {
        emit(SettingUploadKYCSuccess());
      }
      if (dataState is DataFailure) {
        emit(SettingFailure(ApiError(message: dataState.error?.message)));
      }
    } else {
      emit(SettingFailure(const ApiError()));
    }
  }

  Future<void> _removeUser(
    final SettingUserRemoved event,
    final Emitter<SettingState> emit,
  ) async {
    emit(SettingInProgress());
    final dataState = await _userDeletionUseCase();
    if (dataState is DataSuccess) {
      final res = dataState.data;
      if (res?.errorCode == ErrorCodes.success) {
        emit(SettingUserRemovedSuccess(msg: res?.errorMessage));
      } else {
        emit(SettingFailure(ApiError(message: res?.errorMessage)));
      }
    }
    if (dataState is DataFailure) {
      emit(SettingFailure(ApiError(message: dataState.error?.message)));
    }
  }

  Future<void> _changedPassword(
    final SettingChangedPassword event,
    final Emitter<SettingState> emit,
  ) async {
    emit(SettingInProgress());
    final dataState = await _changePasswordUseCase(
      params: ChangePasswordRequestParams(
        phone: EZCache.shared.getUserProfile()?.phone,
        newPassword: Utils.encryptPassword(event.newPassword),
        currentPassword: Utils.encryptPassword(event.currentPassword),
      ),
    );
    if (dataState is DataSuccess) {
      final res = dataState.data;
      if (res?.errorCode == ErrorCodes.success) {
        await EZSecureStorage.storage.write(
          key: KeyStorage.password,
          value: event.newPassword,
        );
        emit(SettingChangePasswordSuccess());
      } else {
        emit(SettingFailure(ApiError(message: res?.errorMessage)));
      }
    }
    if (dataState is DataFailure) {
      emit(SettingFailure(ApiError(message: dataState.error?.message)));
    }
  }
}
