// ignore_for_file: public_member_api_docs, sort_constructors_first
// Dart imports:
import 'dart:async';
import 'dart:io';

// Flutter imports:
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

// Package imports:
import 'package:auto_route/auto_route.dart';
import 'package:ez_core/ez_core.dart';
import 'package:ez_intl/ez_intl.dart';
import 'package:ez_login_authentication/ez_login_authentication.dart';
import 'package:ez_resources/ez_resources.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:flutter_html_iframe/shims/dart_ui.dart';
import 'package:livelyness_detection/livelyness_detection.dart' hide Utils;
import 'package:path_provider/path_provider.dart';

// Project imports:
import '../../../../core/nd_constants/strings.dart';
import '../../../../core/nd_progresshud/loading_widget.dart';
import '../../../../core/params/request_params.dart';
import '../../../../core/routes/app_router.dart';
import '../../../../core/routes/routes.dart';
import '../../../../core/utils/api_error_dialog.dart';
import '../../../../core/utils/nd_utils.dart';
import '../../../../core/utils/screen_record_helper.dart';
import '../../../../data/datasources/ez_datasources.dart';
import '../../../../data/models/nd_models.dart';
import '../../../../domain/usecases/user/get_enable_online_logger_usecase.dart';
import '../../../../domain/usecases/user/save_enable_online_logger_usecase.dart';
import '../../../../injector/injector.dart';
import '../../../_blocs/authentication/authentication_bloc.dart';
import '../../../product_confirm/widgets/base_layout.dart';
import '../../../settings/fonts/fonts_bloc.dart';
import '../../../widgets/widgets.dart';
import '../../more/widgets/menu_setting_item.dart';
import '../bloc/setting_bloc.dart';
import '../widgets/account_menus.dart';
import '../widgets/biometrics_bottom_sheet.dart';

@RoutePage()
class SettingScreen extends StatelessWidget {
  const SettingScreen({final Key? key, this.isShowKYC = false})
    : super(key: key);

  final bool isShowKYC;

  @override
  Widget build(final BuildContext context) {
    return BlocProvider(
      create: (final context) => getIt<SettingBloc>()..add(SettingStarted()),
      child: SettingView(isShowKYC: isShowKYC),
    );
  }
}

class SettingView extends StatefulWidget {
  const SettingView({final Key? key, this.isShowKYC = false}) : super(key: key);

  final bool isShowKYC;

  @override
  SettingViewState createState() => SettingViewState();
}

class SettingViewState extends State<SettingView> {
  String version = '';
  bool activeBiometrics = false;
  List<BiometricType> listBiometrics = [];
  List<AccountMenusModel?> accountMenus = [];

  var enableLoggerCount = 0;
  bool enableLogger = false;
  bool enableBugReport = false;
  UserModel? user;
  String cacheSize = '';

  @override
  void initState() {
    super.initState();
    Timer.run(() async {
      enableLogger = await getIt<GetEnableOnlineLoggerUseCase>()();
      final config = await EZCache.shared.configurations;
      user = EZCache.shared.getUserProfile();
      enableBugReport = config?.reportBug ?? false;
      await showCacheSize();
      setState(() {});
    });
    WidgetsBinding.instance.addPostFrameCallback((final _) {
      Future.delayed(const Duration(milliseconds: 500), () async {
        if (widget.isShowKYC) {
          _onStartLivelyness();
        }
      });
    });
  }

  @override
  Widget build(final BuildContext context) {
    return BlocConsumer<SettingBloc, SettingState>(
      listener: (final context, final state) {
        if (state is SettingLoadSuccess) {
          version = state.version;
          activeBiometrics = state.activeBiometrics;
          listBiometrics = state.listBiometrics;
        }
        if (state is SettingLoadFailure) {
          unawaited(
            Alert.showAlert(
              AlertParams(
                context,
                context.l10n.unknown,
                acceptButton: context.l10n.accept,
              ),
            ),
          );
        }
        if (state is SettingSwitchSuccess) {
          activeBiometrics = state.activeBiometrics;
        }
        if (state is SettingUserRemovedSuccess) {
          context.read<AuthenticationBloc>().add(AuthenticationLoggedOut());
        }
        if (state is SettingChangePasswordSuccess) {
          Navigator.of(context).pop();
          unawaited(
            Alert.showAlert(
              AlertParams(
                context,
                context.l10n.changePasswordSuccess,
                acceptButton: context.l10n.accept,
              ),
            ),
          );
        }
        if (state is SettingShowHotfixDialog) {
          unawaited(
            Alert.showAlertConfirm(
              AlertConfirmParams(
                context,
                message: context.l10n.warningHotfixUpdate,
                onPressed: () {
                  Navigator.of(context).pop();
                  Utils.quitAppWithNotification();
                },
                confirmText: context.l10n.accept,
                cancelButton: context.l10n.skip,
              ),
            ),
          );
        }
        if (state is SettingUpToDate) {
          unawaited(EzToast.showToast(message: context.l10n.noAvailableUpdate));
        }
        if (state is SettingUploadKYCSuccess) {
          unawaited(
            Alert.showAlert(AlertParams(context, context.l10n.updateSuccess)),
          );
        }
        if (state is SettingFailure) {
          unawaited(ApiErrorDialog.show(ApiErrorParams(context, state.error)));
        }
      },
      builder: (final context, final state) {
        return Stack(
          children: [
            BaseLayout(
              title: Text(
                context.l10n.settings,
                style: Theme.of(context).textTheme.labelLarge?.copyWith(
                  fontWeight: FontWeight.w500,
                  fontSize: 20,
                ),
              ),
              body: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    spacing: 16,
                    children: [
                      DecoratedBox(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: SizedBox(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              buildItem(
                                iconPath: AppIcons.icLock,
                                title: context.l10n.changePassword,
                                onTap: () {
                                  context.router
                                      .push(const ChangePasswordRoute())
                                      .then((final val) {
                                        if (val != null &&
                                            val is UpdatePassWordParam) {
                                          if (context.mounted) {
                                            context.read<SettingBloc>().add(
                                              SettingChangedPassword(
                                                currentPassword:
                                                    val.currentPassword,
                                                newPassword: val.newPassword,
                                              ),
                                            );
                                          }
                                        }
                                      });
                                },
                              ),
                              buildItem(
                                iconPath: AppIcons.icFingerprint,
                                title: context.l10n.biometricVerification,
                                onTap: () {
                                  context.router
                                      .push(
                                        BiometricRoute(
                                          isSwitched: activeBiometrics,
                                        ),
                                      )
                                      .then((final val) {
                                        if (val != null && val is bool) {
                                          if (context.mounted) {
                                            context.read<SettingBloc>().add(
                                              SettingSwitched(
                                                currentValue: val,
                                              ),
                                            );
                                          }
                                        }
                                      });
                                },
                                // showBiometricsBottomSheet,
                              ),
                              buildItem(
                                iconPath: AppIcons.icFaceVerify,
                                title: context.l10n.faceVerify,
                                onTap: () {
                                  if (user?.isKyc ?? false) {
                                    Alert.showAlert(
                                      AlertParams(
                                        context,
                                        context.l10n.alreadyKYCMessage,
                                      ),
                                    );
                                  } else {
                                    _onStartLivelyness();
                                  }
                                },
                                isLast: true,
                              ),
                            ],
                          ),
                        ),
                      ),
                      DecoratedBox(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: SizedBox(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              buildItem(
                                iconPath: AppIcons.icFontSize,
                                title: context.l10n.fontSize,
                                onTap: () {
                                  final scale = context
                                      .read<FontsBloc>()
                                      .state
                                      .textScale;
                                  context.router
                                      .push(FontSizeRoute(scale: scale))
                                      .then((final val) {
                                        if (val != null && val is double) {
                                          if (context.mounted) {
                                            context.read<FontsBloc>().add(
                                              FontSizeChanged(scale: val),
                                            );
                                          }
                                        }
                                      });
                                },
                              ),
                              buildItem(
                                iconPath: AppIcons.icBugReport,
                                title: context.l10n.bugReport,
                                onTap: onFeedbackHandler,
                              ),
                              buildItem(
                                iconPath: AppIcons.icClearCache,
                                title: cacheSize.isEmpty
                                    ? context.l10n.clearCache
                                    : '${context.l10n.clearCache} ($cacheSize)',
                                onTap: () async => Alert.showAlertConfirm(
                                  AlertConfirmParams(
                                    context,
                                    message: context.l10n.clearCacheMessage,
                                    cancelButton: context.l10n.cancel,
                                    confirmText: context.l10n.confirm,
                                    onPressed: () {
                                      clearCached().then(
                                        (final _) => setState(() {
                                          showCacheSize();
                                        }),
                                      );
                                      Navigator.of(context).pop();
                                    },
                                  ),
                                ),
                              ),
                              buildItem(
                                iconPath: AppIcons.icSetting,
                                title: context.l10n.checkUpdate,
                                onTap: () async => context
                                    .read<SettingBloc>()
                                    .add(SettingHotfixChecked()),
                                isLast: true,
                              ),

                              // const SizedBox(height: 50),
                              // Center(child:
                              // buildRemoveAccountButton(context)),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            if (state is SettingInProgress) const LoadingWidget(),
          ],
        );
      },
    );
  }

  Widget buildMenu() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: ListView(
        physics: const BouncingScrollPhysics(),
        shrinkWrap: true,
        children: [
          if (!kIsWeb)
            buildMenuItem(
              title: context.l10n.biometricVerification,
              icon: EZResources.image(
                ImageParams(
                  color: Theme.of(context).primaryColor,
                  name: AppIcons.icMenuFingerprint,
                  size: const ImageSize(15, 20),
                ),
              ),
              onTap: showBiometricsBottomSheet,
            ),
          // if (!kIsWeb)
          //   buildMenuItem(
          //     title: context.l10n.fontSize,
          //     icon: Icon(
          //       Icons.format_size_rounded,
          //       size: 20,
          //       color: Theme.of(context).primaryColor,
          //     ),
          //     onTap: showTextScaler,
          //   ),
          AccountMenusWidget(accountMenus: accountMenus),
          if (enableLogger)
            buildMenuItem(
              title: context.l10n.devFeatures,
              icon: Icon(
                Icons.developer_mode_rounded,
                color: Theme.of(context).primaryColor,
                size: 20,
              ),
              onTap: () async => AutoRouter.of(context).pushNamed(Routes.dev),
            ),
          if (!kIsWeb)
            buildMenuItem(
              title: context.l10n.faceVerify,
              icon: EZResources.image(
                ImageParams(
                  name: AppIcons.icFaceId,
                  size: const ImageSize.square(20),
                  color: Theme.of(context).primaryColor,
                ),
              ),
              onTap: () async {
                if (user?.isKyc ?? false) {
                  Alert.showAlert(
                    AlertParams(context, context.l10n.alreadyKYCMessage),
                  );
                } else {
                  _onStartLivelyness();
                }
              },
            ),
          if (Utils.isMobile && enableBugReport)
            buildMenuItem(
              title: context.l10n.bugReport,
              icon: Icon(
                Icons.bug_report_outlined,
                size: 20,
                color: Theme.of(context).primaryColor,
              ),
              onTap: onFeedbackHandler,
            ),
          if (!kIsWeb)
            buildMenuItem(
              title: cacheSize.isEmpty
                  ? context.l10n.clearCache
                  : '${context.l10n.clearCache} ($cacheSize)',
              icon: EZResources.image(
                ImageParams(
                  name: AppIcons.icDelete,
                  size: const ImageSize.square(20),
                  color: Theme.of(context).primaryColor,
                ),
              ),
              onTap: () async => Alert.showAlertConfirm(
                AlertConfirmParams(
                  context,
                  message: context.l10n.clearCacheMessage,
                  cancelButton: context.l10n.cancel,
                  confirmText: context.l10n.confirm,
                  onPressed: () {
                    clearCached().then(
                      (final _) => setState(() {
                        showCacheSize();
                      }),
                    );
                    Navigator.of(context).pop();
                  },
                ),
              ),
            ),
          if (!kIsWeb)
            buildMenuItem(
              title: context.l10n.checkUpdate,
              icon: EZResources.image(
                ImageParams(
                  name: AppIcons.icClockHistory,
                  size: const ImageSize.square(20),
                  color: Theme.of(context).primaryColor,
                ),
              ),
              onTap: () async =>
                  context.read<SettingBloc>().add(SettingHotfixChecked()),
            ),
          //============= Language option =============
          // buildMenuItem(
          //   title: context.l10n.language,
          //   icon: EZResources.image(ImageParams(
          //       color: AppColors.mainColor,
          //       name: AppIcons.icLanguage,
          //       width: 20,
          //       height: 20),
          //   onTap: () => AutoRouter.of(context).pushNamed(Routes.language),
          // ),
          const SizedBox(height: 20),
          GestureDetector(
            onDoubleTap: () async {
              if (enableLoggerCount < 2) {
                enableLoggerCount++;
              } else {
                enableLogger = !enableLogger;
                getIt<SaveEnableOnlineLoggerUseCase>()(params: enableLogger);
                EzToast.showToast(message: 'Online logger $enableLogger');
                setState(() {});
              }
            },
            child: Text('${context.l10n.version} $version'),
          ),
          const SizedBox(height: 50),
          Center(child: buildRemoveAccountButton(context)),
        ],
      ),
    );
  }

  Future<void> _onStartLivelyness() async {
    final List<CapturedImage?> response = await LivelynessDetection.instance
        .detectLivelyness(
          context,
          config: DetectionConfig(
            steps: [
              LivelynessStepItem(
                step: LivelynessStep.lookStraight,
                title: context.l10n.lookStraight,
                isCompleted: false,
              ),
              LivelynessStepItem(
                step: LivelynessStep.blink,
                title: context.l10n.lookStraightAndBlink,
                isCompleted: false,
              ),
              LivelynessStepItem(
                step: LivelynessStep.smile,
                title: context.l10n.lookStraightAndSmile,
                isCompleted: false,
              ),
              LivelynessStepItem(
                step: LivelynessStep.turnLeft,
                title: context.l10n.turnLeft,
                isCompleted: false,
              ),
              LivelynessStepItem(
                step: LivelynessStep.turnRight,
                title: context.l10n.turnRight,
                isCompleted: false,
              ),
            ],
            startWithInfoScreen: true,
            maxSecToDetect: 300,
            allowAfterMaxSec: true,
          ),
        );
    if (response.isEmpty) {
      return;
    }
    if (mounted) {
      context.read<SettingBloc>().add(
        SettingUploadedKYC(
          paths: response.map((final e) => e?.imgPath ?? '').take(3).toList(),
        ),
      );
    }
  }

  Widget buildMenuItem({
    required final String title,
    required final Widget icon,
    required final Function() onTap,
  }) {
    return Column(
      children: [
        InkWell(
          onTap: () => onTap(),
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 20),
            child: MenuSettingItem(image: icon, title: title),
          ),
        ),
        Container(height: 1, color: Theme.of(context).dividerColor),
      ],
    );
  }

  Future<void> showBiometricsBottomSheet() async {
    return showBaseBottomSheet(
      context: context,
      title: context.l10n.biometricVerification,
      height: 300,
      child: BiometricsBottomSheet(
        currentValue: activeBiometrics,
        context: context,
        listBiometrics: listBiometrics,
      ),
    );
  }

  Future<void> clearCached() async {
    try {
      final cacheDir = await getTemporaryDirectory();

      if (cacheDir.existsSync()) {
        cacheDir.listSync().forEach((final file) {
          if (file is File) {
            file.delete();
          } else if (file is Directory) {
            file.delete(recursive: true);
          }
        });
      }
      await DefaultCacheManager().emptyCache();
      if (mounted) {
        await EzToast.showToast(message: context.l10n.success);
      }
    } catch (_) {}
  }

  Future<void> showCacheSize() async {
    final int sizeInBytes = await getCacheSize();
    final double sizeInMB = sizeInBytes / (1024 * 1024);
    if (sizeInMB >= 1) {
      cacheSize = '${sizeInMB.toStringAsFixed(0)} MB';
    } else {
      cacheSize = '';
    }
  }

  /// Get the total cache size of the app
  Future<int> getCacheSize() async {
    try {
      final tempDir = await getTemporaryDirectory();
      return await _getDirectorySize(tempDir);
    } catch (e) {
      return 0;
    }
  }

  /// Helper function to calculate directory size
  Future<int> _getDirectorySize(final Directory directory) async {
    int totalSize = 0;
    try {
      if (directory.existsSync()) {
        final List<FileSystemEntity> files = directory.listSync(
          recursive: true,
        );
        for (final FileSystemEntity file in files) {
          if (file is File) {
            totalSize += await file.length();
          }
        }
      }
    } catch (_) {}
    return totalSize;
  }

  Widget buildRemoveAccountButton(final BuildContext context) {
    if (kIsWeb) {
      return const SizedBox();
    }

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
      child: InkWell(
        onTap: () async {
          Alert.showAlertConfirm(
            AlertConfirmParams(
              context,
              title: context.l10n.removeUserAccount,
              message: context.l10n.removeUserAccountMessage,
              cancelButton: context.l10n.cancel,
              confirmText: context.l10n.confirm,
              onPressed: () {
                context.read<SettingBloc>().add(SettingUserRemoved());
              },
            ),
          );
        },
        child: Text(
          context.l10n.removeUserAccount,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w400,
            color: Theme.of(context).colorScheme.error,
          ),
        ),
      ),
    );
  }

  Future<void> onFeedbackHandler() async {
    ScreenRecordHelper.instance.isDebugVN.value = true;
    ReportFloatingAction.showVidRecControllerVN.value =
        !ReportFloatingAction.showVidRecControllerVN.value;
  }

  // Future<void> showTextScaler() async {
  //   const trackDivision = 8;
  //   const stepValue = 16 / 16;
  //   const minValue = 12 / 16;
  //   const maxValue = 20 / 16;
  //   final scaleVN = ValueNotifier(context.read<FontsBloc>().state.textScale);

  //   return showBaseBottomSheet(
  //     context: context,
  //     title: context.l10n.fontSize,
  //     child: ValueListenableBuilder(
  //       valueListenable: scaleVN,
  //       builder: (final _, final scale, final __) {
  //         return Padding(
  //           padding: const EdgeInsets.all(16),
  //           child: Column(
  //             mainAxisAlignment: MainAxisAlignment.center,
  //             children: [
  //               Stack(
  //                 alignment: Alignment.center,
  //                 children: [
  //                   SizedBox(
  //                     width: double.maxFinite,
  //                     child: Row(
  //                       mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //                       children: [
  //                         textButton(
  //                           text: context.l10n.smallSize,
  //                           onPressed: () {
  //                             final scaleValue = scaleVN.value - stepValue;
  //                             scaleVN.value = scaleValue < minValue
  //                                 ? minValue
  //                                 : scaleValue;
  //                             context.read<FontsBloc>().add(
  //                               FontSizeChanged(scale: scaleVN.value),
  //                             );
  //                           },
  //                         ),
  //                         textButton(
  //                           text: context.l10n.bigSize,
  //                           onPressed: () {
  //                             final scaleValue = scaleVN.value + stepValue;
  //                             scaleVN.value = scaleValue > maxValue
  //                                 ? maxValue
  //                                 : scaleValue;
  //                             context.read<FontsBloc>().add(
  //                               FontSizeChanged(scale: scaleVN.value),
  //                             );
  //                           },
  //                         ),
  //                       ],
  //                     ),
  //                   ),
  //                 ],
  //               ),
  //               const SizedBox(height: 12),
  //               SliderTheme(
  //                 data: const SliderThemeData(
  //                   trackShape: RoundedRectSliderTrackShape(),
  //                   tickMarkShape: RoundSliderTickMarkShape(tickMarkRadius: 4),
  //                 ),
  //                 child: Slider(
  //                   value: scaleVN.value,
  //                   min: minValue,
  //                   max: maxValue,
  //                   label: (scaleVN.value * 16).toString(),
  //                   divisions: trackDivision,
  //                   onChanged: (final val) {
  //                     scaleVN.value = val;
  //                     context.read<FontsBloc>().add(
  //                       FontSizeChanged(scale: scaleVN.value),
  //                     );
  //                   },
  //                 ),
  //               ),
  //               LayoutBuilder(
  //                 builder: (final _, final contraints) {
  //                   return Container(
  //                     width: double.maxFinite,
  //                     padding: EdgeInsets.only(
  //                       left: (contraints.maxWidth / 2) - (40 * scaleVN.value),
  //                     ),
  //                     alignment: Alignment.centerLeft,
  //                     child: textButton(
  //                       text: context.l10n.defaultText,
  //                       textColor: Theme.of(context).primaryColor,
  //                       onPressed: () {
  //                         scaleVN.value = 1;
  //                         context.read<FontsBloc>().add(
  //                           FontSizeChanged(scale: scaleVN.value),
  //                         );
  //                       },
  //                     ),
  //                   );
  //                 },
  //               ),
  //             ],
  //           ),
  //         );
  //       },
  //     ),
  //   );
  // }

  Widget buildItem({
    final String? iconPath,
    final bool isLast = false,
    final String title = Strings.empty,
    required final Function() onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: DecoratedBox(
        decoration: BoxDecoration(
          border: Border(
            bottom: isLast
                ? BorderSide.none
                : const BorderSide(color: Color(0xffDEE3ED)),
          ),
        ),
        child: SizedBox(
          width: double.maxFinite,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              spacing: 8,
              children: [
                if (iconPath != null)
                  EZResources.image(ImageParams(name: iconPath)),
                Expanded(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(title),
                      EZResources.image(
                        ImageParams(
                          name: AppIcons.icForward,
                          size: const ImageSize.square(24),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget textButton({
    required final String text,
    final Color? textColor,
    required final VoidCallback onPressed,
  }) {
    return TextButton(
      style: TextButton.styleFrom(
        padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 12),
      ),
      onPressed: onPressed,
      child: Text(
        text,
        style: Theme.of(
          context,
        ).textTheme.bodyMedium?.copyWith(color: textColor),
      ),
    );
  }
}

class UpdatePassWordParam {
  const UpdatePassWordParam({this.currentPassword, this.newPassword});
  final String? currentPassword;
  final String? newPassword;

  UpdatePassWordParam copyWith({
    final String? currentPassword,
    final String? newPassword,
  }) {
    return UpdatePassWordParam(
      currentPassword: currentPassword ?? this.currentPassword,
      newPassword: newPassword ?? this.newPassword,
    );
  }
}
