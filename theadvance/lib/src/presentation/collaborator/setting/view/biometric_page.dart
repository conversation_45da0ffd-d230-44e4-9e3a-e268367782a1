import 'package:auto_route/auto_route.dart';
import 'package:ez_intl/ez_intl.dart';
import 'package:ez_resources/ez_resources.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../../product_confirm/widgets/base_layout.dart';

@RoutePage()
class BiometricPage extends StatefulWidget {
  const BiometricPage({super.key, this.isSwitched = false});
  final bool isSwitched;
  @override
  State<BiometricPage> createState() => _BiometricPageState();
}

class _BiometricPageState extends State<BiometricPage> {
  bool isSwitched = false;
  @override
  void initState() {
    isSwitched = widget.isSwitched;
    super.initState();
  }

  @override
  Widget build(final BuildContext context) {
    return BaseLayout(
      leading: IconButton(
        onPressed: () {
          context.router.popForced(isSwitched);
        },
        icon: EZResources.image(
          ImageParams(
            name: AppIcons.icBack,
            color: Theme.of(context).primaryColor,
          ),
        ),
      ),
      title: Text(
        context.l10n.biometricVerification,
        style: Theme.of(context).textTheme.labelLarge?.copyWith(
          fontWeight: FontWeight.w500,
          fontSize: 20,
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: DecoratedBox(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(
                    vertical: 4,
                    horizontal: 8,
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: Text(
                          context.l10n.biometricVerification,
                          style: Theme.of(context).textTheme.titleSmall
                              ?.copyWith(
                                color: Theme.of(context).primaryColorDark,
                                fontWeight: FontWeight.w500,
                              ),
                        ),
                      ),
                      CupertinoSwitch(
                        value: isSwitched,
                        onChanged: (final value) {
                          isSwitched = value;
                          setState(() {});
                        },
                        inactiveTrackColor: Theme.of(
                          context,
                        ).primaryColor.withValues(alpha: 0.5),
                        activeTrackColor: Theme.of(context).primaryColor,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
