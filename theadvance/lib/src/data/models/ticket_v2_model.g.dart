// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ticket_v2_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TicketV2Model _$TicketV2ModelFromJson(Map<String, dynamic> json) =>
    TicketV2Model(
      totalDocs: double.tryParse(json['totalDocs'].toString())?.toInt(),
      limit: double.tryParse(json['limit'].toString())?.toInt(),
      page: double.tryParse(json['page'].toString())?.toInt(),
      docs: (json['docs'] is List)
          ? (json['docs'] as List<dynamic>)
                .map(
                  (e) => e == null || e is! Map
                      ? null
                      : TicketV2ItemsModel.fromJson(e as Map<String, dynamic>),
                )
                .toList()
          : [],
    );

Map<String, dynamic> _$TicketV2ModelToJson(TicketV2Model instance) =>
    <String, dynamic>{
      'totalDocs': instance.totalDocs,
      'limit': instance.limit,
      'page': instance.page,
      'docs': instance.docs,
    };

TicketV2ItemsModel _$TicketV2ItemsModelFromJson(Map<String, dynamic> json) =>
    TicketV2ItemsModel(
      id: json['id']?.toString(),
      content: json['content']?.toString(),
      type: json['type']?.toString(),
      typeDisplay: json['typeDisplay']?.toString(),
      group: json['group']?.toString(),
      groupDisplay: json['groupDisplay']?.toString(),
      state: json['state']?.toString(),
      stateDisplay: json['stateDisplay']?.toString(),
      createdAt: json['createdAt']?.toString(),
      createdBy: json['createdBy']?.toString(),
      avatar: json['avatar']?.toString(),
      operateAt: json['operateAt']?.toString(),
      operateBy: json['operateBy']?.toString(),
      owner: json['owner']?.toString(),
      mentions: (json['mentions'] is List)
          ? (json['mentions'] as List<dynamic>?)
                ?.map((e) => MentionModel.fromJson(e as Map<String, dynamic>))
                .toList()
          : [],
    );

Map<String, dynamic> _$TicketV2ItemsModelToJson(TicketV2ItemsModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'content': instance.content,
      'type': instance.type,
      'typeDisplay': instance.typeDisplay,
      'group': instance.group,
      'groupDisplay': instance.groupDisplay,
      'state': instance.state,
      'stateDisplay': instance.stateDisplay,
      'createdAt': instance.createdAt,
      'createdBy': instance.createdBy,
      'avatar': instance.avatar,
      'operateAt': instance.operateAt,
      'operateBy': instance.operateBy,
      'owner': instance.owner,
      'mentions': instance.mentions,
    };
