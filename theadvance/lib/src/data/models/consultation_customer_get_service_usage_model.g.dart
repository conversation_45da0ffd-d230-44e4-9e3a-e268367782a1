// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'consultation_customer_get_service_usage_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ConsultationCustomerGetServiceUsageModel
_$ConsultationCustomerGetServiceUsageModelFromJson(
  Map<String, dynamic> json,
) => ConsultationCustomerGetServiceUsageModel(
  serviceInfo: json['ServiceInfo'] == null || json['ServiceInfo'] is! Map
      ? null
      : ConsultationCustomerGetServiceUsageServiceInfoModel.fromJson(
          json['ServiceInfo'] as Map<String, dynamic>,
        ),
  serviceDetail: (json['ServiceDetail'] is List)
      ? (json['ServiceDetail'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : ConsultationCustomerGetServiceUsageServiceDetailModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
);

Map<String, dynamic> _$ConsultationCustomerGetServiceUsageModelToJson(
  ConsultationCustomerGetServiceUsageModel instance,
) => <String, dynamic>{
  'ServiceInfo': instance.serviceInfo,
  'ServiceDetail': instance.serviceDetail,
};

ConsultationCustomerGetServiceUsageServiceInfoModel
_$ConsultationCustomerGetServiceUsageServiceInfoModelFromJson(
  Map<String, dynamic> json,
) => ConsultationCustomerGetServiceUsageServiceInfoModel(
  usageID: json['UsageID']?.toString(),
  partnerID: json['PartnerID']?.toString(),
  partnerName: json['PartnerName']?.toString(),
  itemID: json['ItemID']?.toString(),
  itemName: json['ItemName']?.toString(),
  customerID: double.tryParse(json['CustomerID'].toString())?.toInt(),
  lSServiceID: double.tryParse(json['LSServiceID'].toString())?.toInt(),
  originalTransactionNo: json['OriginalTransactionNo']?.toString(),
  quantity: double.tryParse(json['Quantity'].toString())?.toInt(),
  transactionDate: json['TransactionDate']?.toString(),
  reExamDate: json['ReExamDate']?.toString(),
  checkEmployeeID: json['CheckEmployeeID']?.toString(),
  skillEmployeeID: json['SkillEmployeeID']?.toString(),
  doctorEmployeeID: json['DoctorEmployeeID']?.toString(),
  note: json['Note']?.toString(),
  originalComboNo: json['OriginalComboNo']?.toString(),
  isEffective: bool.tryParse(json['IsEffective'].toString()),
  userLogin: json['UserLogin']?.toString(),
  isEndOfTreatment: bool.tryParse(json['IsEndOfTreatment'].toString()),
  initialConditionInfo: json['InitialCondition']?.toString(),
);

Map<String, dynamic>
_$ConsultationCustomerGetServiceUsageServiceInfoModelToJson(
  ConsultationCustomerGetServiceUsageServiceInfoModel instance,
) => <String, dynamic>{
  'UsageID': instance.usageID,
  'PartnerID': instance.partnerID,
  'PartnerName': instance.partnerName,
  'ItemID': instance.itemID,
  'ItemName': instance.itemName,
  'CustomerID': instance.customerID,
  'LSServiceID': instance.lSServiceID,
  'OriginalTransactionNo': instance.originalTransactionNo,
  'Quantity': instance.quantity,
  'TransactionDate': instance.transactionDate,
  'ReExamDate': instance.reExamDate,
  'CheckEmployeeID': instance.checkEmployeeID,
  'SkillEmployeeID': instance.skillEmployeeID,
  'DoctorEmployeeID': instance.doctorEmployeeID,
  'Note': instance.note,
  'OriginalComboNo': instance.originalComboNo,
  'IsEffective': instance.isEffective,
  'UserLogin': instance.userLogin,
  'IsEndOfTreatment': instance.isEndOfTreatment,
  'InitialCondition': instance.initialConditionInfo,
};

ConsultationCustomerGetServiceUsageServiceDetailModel
_$ConsultationCustomerGetServiceUsageServiceDetailModelFromJson(
  Map<String, dynamic> json,
) => ConsultationCustomerGetServiceUsageServiceDetailModel(
  serviceID: json['ServiceID']?.toString(),
  serviceName: json['ServiceName']?.toString(),
  serviceItemID: json['ServiceItemID']?.toString(),
  serviceItemName: json['ServiceItemName']?.toString(),
  reasonType: json['ReasonType']?.toString(),
  note: json['Note']?.toString(),
  propertyInfos: (json['PropertyInfos'] is List)
      ? (json['PropertyInfos'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : ConsultationCustomerGetServiceUsageServiceDetailPropertyInfosModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
);

Map<String, dynamic>
_$ConsultationCustomerGetServiceUsageServiceDetailModelToJson(
  ConsultationCustomerGetServiceUsageServiceDetailModel instance,
) => <String, dynamic>{
  'ServiceID': instance.serviceID,
  'ServiceName': instance.serviceName,
  'ServiceItemID': instance.serviceItemID,
  'ServiceItemName': instance.serviceItemName,
  'ReasonType': instance.reasonType,
  'Note': instance.note,
  'PropertyInfos': instance.propertyInfos,
};

ConsultationCustomerGetServiceUsageServiceDetailPropertyInfosModel
_$ConsultationCustomerGetServiceUsageServiceDetailPropertyInfosModelFromJson(
  Map<String, dynamic> json,
) => ConsultationCustomerGetServiceUsageServiceDetailPropertyInfosModel(
  propertiesID: json['PropertiesID']?.toString(),
  propertiesValue: json['PropertiesValue']?.toString(),
);

Map<String, dynamic>
_$ConsultationCustomerGetServiceUsageServiceDetailPropertyInfosModelToJson(
  ConsultationCustomerGetServiceUsageServiceDetailPropertyInfosModel instance,
) => <String, dynamic>{
  'PropertiesID': instance.propertiesID,
  'PropertiesValue': instance.propertiesValue,
};
