// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ticket_user_info_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TicketUserInfoModel _$TicketUserInfoModelFromJson(Map<String, dynamic> json) =>
    TicketUserInfoModel(
      uId: double.tryParse(json['u_id'].toString())?.toInt(),
      uName: json['u_name']?.toString(),
      uRoom: double.tryParse(json['u_room'].toString())?.toInt(),
      uGroup: double.tryParse(json['u_group'].toString())?.toInt(),
      levelId: double.tryParse(json['level_id'].toString())?.toInt(),
      stringeeGroup: json['stringee_group']?.toString(),
      stringeeId: json['stringee_id']?.toString(),
      uCode: json['u_code']?.toString(),
      userIdErp: double.tryParse(json['user_id_erp'].toString())?.toInt(),
      ipPhone: json['ip_phone']?.toString(),
      stringeeToken: json['tokens'] == null || json['tokens'] is! Map
          ? null
          : StringeeTokenModel.fromJson(json['tokens'] as Map<String, dynamic>),
      hotline: (json['hotline'] is List)
          ? (json['hotline'] as List<dynamic>?)
                ?.map((e) => e?.toString())
                .toList()
          : [],
    );

StringeeTokenModel _$StringeeTokenModelFromJson(Map<String, dynamic> json) =>
    StringeeTokenModel(
      token3C: json['token_3C']?.toString(),
      stringeeToken: json['stringee_token']?.toString(),
      stringeeCallToken: json['stringee_call_token']?.toString(),
    );
