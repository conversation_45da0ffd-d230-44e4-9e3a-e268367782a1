// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'story_rule_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

StoryRuleModel _$StoryRuleModelFromJson(Map<String, dynamic> json) =>
    StoryRuleModel(
      items: (json['items'] is List)
          ? (json['items'] as List<dynamic>)
                .map(
                  (e) => e == null || e is! Map
                      ? null
                      : StoryRuleItemsModel.fromJson(e as Map<String, dynamic>),
                )
                .toList()
          : [],
    );

StoryRuleItemsModel _$StoryRuleItemsModelFromJson(Map<String, dynamic> json) =>
    StoryRuleItemsModel(
      title: json['title']?.toString(),
      content: json['content']?.toString(),
      type: json['type']?.toString(),
    );
