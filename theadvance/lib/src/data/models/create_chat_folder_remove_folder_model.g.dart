// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'create_chat_folder_remove_folder_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CreateChatFolderRemoveFolderModel _$CreateChatFolderRemoveFolderModelFromJson(
  Map<String, dynamic> json,
) => CreateChatFolderRemoveFolderModel(
  folder: json['folder'] == null || json['folder'] is! Map
      ? null
      : CreateChatFolderRemoveFolderFolderModel.fromJson(
          json['folder'] as Map<String, dynamic>,
        ),
);

Map<String, dynamic> _$CreateChatFolderRemoveFolderModelToJson(
  CreateChatFolderRemoveFolderModel instance,
) => <String, dynamic>{'folder': instance.folder};

CreateChatFolderRemoveFolderFolderModel
_$CreateChatFolderRemoveFolderFolderModelFromJson(Map<String, dynamic> json) =>
    CreateChatFolderRemoveFolderFolderModel(
      name: json['name']?.toString(),
      username: json['username']?.toString(),
      icon: json['icon']?.toString(),
      conversations: (json['conversations'] is List)
          ? (json['conversations'] as List<dynamic>)
                .map((e) => e?.toString())
                .toList()
          : [],
      id: json['id']?.toString(),
      createdAt: json['createdAt']?.toString(),
      updatedAt: json['updatedAt']?.toString(),
    );

Map<String, dynamic> _$CreateChatFolderRemoveFolderFolderModelToJson(
  CreateChatFolderRemoveFolderFolderModel instance,
) => <String, dynamic>{
  'name': instance.name,
  'username': instance.username,
  'icon': instance.icon,
  'conversations': instance.conversations,
  'id': instance.id,
  'createdAt': instance.createdAt,
  'updatedAt': instance.updatedAt,
};
