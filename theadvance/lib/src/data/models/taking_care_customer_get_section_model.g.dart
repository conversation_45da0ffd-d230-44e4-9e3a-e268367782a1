// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'taking_care_customer_get_section_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TakingCareCustomerGetSectionModel _$TakingCareCustomerGetSectionModelFromJson(
  Map<String, dynamic> json,
) => TakingCareCustomerGetSectionModel(
  serviceMaster: json['ServiceMaster'] == null || json['ServiceMaster'] is! Map
      ? null
      : TakingCareCustomerGetSectionServiceMasterModel.fromJson(
          json['ServiceMaster'] as Map<String, dynamic>,
        ),
  serviceDetail: json['ServiceDetail'] == null || json['ServiceDetail'] is! Map
      ? null
      : TakingCareCustomerGetSectionServiceDetailModel.fromJson(
          json['ServiceDetail'] as Map<String, dynamic>,
        ),
  serviceStep: json['ServiceStep'] == null || json['ServiceStep'] is! Map
      ? null
      : TakingCareCustomerGetSectionServiceStepModel.fromJson(
          json['ServiceStep'] as Map<String, dynamic>,
        ),
  initialConditionInfo: (json['InitialConditionInfo'] is List)
      ? (json['InitialConditionInfo'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : InitialConditionInfoModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
);

Map<String, dynamic> _$TakingCareCustomerGetSectionModelToJson(
  TakingCareCustomerGetSectionModel instance,
) => <String, dynamic>{
  'ServiceMaster': instance.serviceMaster,
  'ServiceDetail': instance.serviceDetail,
  'ServiceStep': instance.serviceStep,
  'InitialConditionInfo': instance.initialConditionInfo,
};

InitialConditionInfoModel _$InitialConditionInfoModelFromJson(
  Map<String, dynamic> json,
) => InitialConditionInfoModel(
  initialCondition: json['InitialCondition']?.toString(),
  rank: json['Rank']?.toString(),
);

Map<String, dynamic> _$InitialConditionInfoModelToJson(
  InitialConditionInfoModel instance,
) => <String, dynamic>{
  'InitialCondition': instance.initialCondition,
  'Rank': instance.rank,
};

TakingCareCustomerGetSectionServiceMasterModel
_$TakingCareCustomerGetSectionServiceMasterModelFromJson(
  Map<String, dynamic> json,
) => TakingCareCustomerGetSectionServiceMasterModel(
  itemID: json['ItemID']?.toString(),
  itemName: json['ItemName']?.toString(),
  itemGroupID: json['ItemGroupID']?.toString(),
  itemGroupName: json['ItemGroupName']?.toString(),
  suggestedRetailPrice: double.tryParse(
    json['SuggestedRetailPrice'].toString(),
  )?.toInt(),
  transactionDate: json['TransactionDate']?.toString(),
  productConversion: double.tryParse(
    json['ProductConversion'].toString(),
  )?.toInt(),
  isAllowedSupport: double.tryParse(
    json['IsAllowedSupport'].toString(),
  )?.toInt(),
  numberOfReExamDate: double.tryParse(
    json['NumberOfReExamDate'].toString(),
  )?.toInt(),
);

Map<String, dynamic> _$TakingCareCustomerGetSectionServiceMasterModelToJson(
  TakingCareCustomerGetSectionServiceMasterModel instance,
) => <String, dynamic>{
  'ItemID': instance.itemID,
  'ItemName': instance.itemName,
  'ItemGroupID': instance.itemGroupID,
  'ItemGroupName': instance.itemGroupName,
  'SuggestedRetailPrice': instance.suggestedRetailPrice,
  'TransactionDate': instance.transactionDate,
  'ProductConversion': instance.productConversion,
  'IsAllowedSupport': instance.isAllowedSupport,
  'NumberOfReExamDate': instance.numberOfReExamDate,
};

TakingCareCustomerGetSectionServiceDetailModel
_$TakingCareCustomerGetSectionServiceDetailModelFromJson(
  Map<String, dynamic> json,
) => TakingCareCustomerGetSectionServiceDetailModel(
  infos: (json['Infos'] is List)
      ? (json['Infos'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : TakingCareCustomerGetSectionServiceDetailInfosModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
  items: (json['Items'] is List)
      ? (json['Items'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : TakingCareCustomerGetSectionServiceDetailItemsModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
  itemProps: (json['ItemProps'] is List)
      ? (json['ItemProps'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : TakingCareCustomerGetSectionServiceDetailItemPropsModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
);

Map<String, dynamic> _$TakingCareCustomerGetSectionServiceDetailModelToJson(
  TakingCareCustomerGetSectionServiceDetailModel instance,
) => <String, dynamic>{
  'Infos': instance.infos,
  'Items': instance.items,
  'ItemProps': instance.itemProps,
};

TakingCareCustomerGetSectionServiceStepModel
_$TakingCareCustomerGetSectionServiceStepModelFromJson(
  Map<String, dynamic> json,
) => TakingCareCustomerGetSectionServiceStepModel(
  masterInfo: (json['MasterInfo'] is List)
      ? (json['MasterInfo'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : TakingCareCustomerGetSectionServiceStepMasterInfoModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
  detailInfo: (json['DetailInfo'] is List)
      ? (json['DetailInfo'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : TakingCareCustomerGetSectionServiceStepDetailInfoModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
);

Map<String, dynamic> _$TakingCareCustomerGetSectionServiceStepModelToJson(
  TakingCareCustomerGetSectionServiceStepModel instance,
) => <String, dynamic>{
  'MasterInfo': instance.masterInfo,
  'DetailInfo': instance.detailInfo,
};

TakingCareCustomerGetSectionServiceDetailInfosModel
_$TakingCareCustomerGetSectionServiceDetailInfosModelFromJson(
  Map<String, dynamic> json,
) => TakingCareCustomerGetSectionServiceDetailInfosModel(
  itemID: json['ItemID']?.toString(),
  serviceID: json['ServiceID']?.toString(),
  serviceName: json['ServiceName']?.toString(),
  description: json['Description']?.toString(),
  parent: json['Parent']?.toString(),
  children: (json['Children'] is List)
      ? (json['Children'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : TakingCareCustomerGetSectionServiceDetailInfosModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
);

Map<String, dynamic>
_$TakingCareCustomerGetSectionServiceDetailInfosModelToJson(
  TakingCareCustomerGetSectionServiceDetailInfosModel instance,
) => <String, dynamic>{
  'ItemID': instance.itemID,
  'ServiceID': instance.serviceID,
  'ServiceName': instance.serviceName,
  'Description': instance.description,
  'Parent': instance.parent,
  'Children': instance.children,
};

TakingCareCustomerGetSectionServiceDetailItemsModel
_$TakingCareCustomerGetSectionServiceDetailItemsModelFromJson(
  Map<String, dynamic> json,
) => TakingCareCustomerGetSectionServiceDetailItemsModel(
  itemID: json['ItemID']?.toString(),
  serviceID: json['ServiceID']?.toString(),
  serviceItemID: json['ServiceItemID']?.toString(),
  serviceItemName: json['ServiceItemName']?.toString(),
  serviceRank: double.tryParse(json['ServiceRank'].toString())?.toInt(),
);

Map<String, dynamic>
_$TakingCareCustomerGetSectionServiceDetailItemsModelToJson(
  TakingCareCustomerGetSectionServiceDetailItemsModel instance,
) => <String, dynamic>{
  'ItemID': instance.itemID,
  'ServiceID': instance.serviceID,
  'ServiceItemID': instance.serviceItemID,
  'ServiceItemName': instance.serviceItemName,
  'ServiceRank': instance.serviceRank,
};

TakingCareCustomerGetSectionServiceDetailItemPropsModel
_$TakingCareCustomerGetSectionServiceDetailItemPropsModelFromJson(
  Map<String, dynamic> json,
) => TakingCareCustomerGetSectionServiceDetailItemPropsModel(
  itemID: json['ItemID']?.toString(),
  serviceID: json['ServiceID']?.toString(),
  serviceItemID: json['ServiceItemID']?.toString(),
  propertiesID: json['PropertiesID']?.toString(),
  propertiesName: json['PropertiesName']?.toString(),
  propertiesValue: json['PropertiesValue']?.toString(),
);

Map<String, dynamic>
_$TakingCareCustomerGetSectionServiceDetailItemPropsModelToJson(
  TakingCareCustomerGetSectionServiceDetailItemPropsModel instance,
) => <String, dynamic>{
  'ItemID': instance.itemID,
  'ServiceID': instance.serviceID,
  'ServiceItemID': instance.serviceItemID,
  'PropertiesID': instance.propertiesID,
  'PropertiesName': instance.propertiesName,
  'PropertiesValue': instance.propertiesValue,
};

TakingCareCustomerGetSectionServiceStepMasterInfoModel
_$TakingCareCustomerGetSectionServiceStepMasterInfoModelFromJson(
  Map<String, dynamic> json,
) => TakingCareCustomerGetSectionServiceStepMasterInfoModel(
  stepMasterID: double.tryParse(json['StepMasterID'].toString())?.toInt(),
  stepConfigID: double.tryParse(json['StepConfigID'].toString())?.toInt(),
  stepConfigName: json['StepConfigName']?.toString(),
  stepMasterRank: double.tryParse(json['StepMasterRank'].toString())?.toInt(),
  totalTime: double.tryParse(json['TotalTime'].toString())?.toInt(),
);

Map<String, dynamic>
_$TakingCareCustomerGetSectionServiceStepMasterInfoModelToJson(
  TakingCareCustomerGetSectionServiceStepMasterInfoModel instance,
) => <String, dynamic>{
  'StepMasterID': instance.stepMasterID,
  'StepConfigID': instance.stepConfigID,
  'StepConfigName': instance.stepConfigName,
  'StepMasterRank': instance.stepMasterRank,
  'TotalTime': instance.totalTime,
};

TakingCareCustomerGetSectionServiceStepDetailInfoModel
_$TakingCareCustomerGetSectionServiceStepDetailInfoModelFromJson(
  Map<String, dynamic> json,
) => TakingCareCustomerGetSectionServiceStepDetailInfoModel(
  stepMasterID: double.tryParse(json['StepMasterID'].toString())?.toInt(),
  stepDetailID: double.tryParse(json['StepDetailID'].toString())?.toInt(),
  stepDetailName: json['StepDetailName']?.toString(),
  detailTime: double.tryParse(json['DetailTime'].toString())?.toInt(),
  stepDetailRank: double.tryParse(json['StepDetailRank'].toString())?.toInt(),
);

Map<String, dynamic>
_$TakingCareCustomerGetSectionServiceStepDetailInfoModelToJson(
  TakingCareCustomerGetSectionServiceStepDetailInfoModel instance,
) => <String, dynamic>{
  'StepMasterID': instance.stepMasterID,
  'StepDetailID': instance.stepDetailID,
  'StepDetailName': instance.stepDetailName,
  'DetailTime': instance.detailTime,
  'StepDetailRank': instance.stepDetailRank,
};
