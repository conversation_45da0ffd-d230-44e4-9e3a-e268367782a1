// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'question_list_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

QuestionListModel _$QuestionListModelFromJson(Map<String, dynamic> json) =>
    QuestionListModel(
      items: (json['items'] is List)
          ? (json['items'] as List<dynamic>?)
                ?.map(
                  (e) => QuestionItemModel.fromJson(e as Map<String, dynamic>),
                )
                .toList()
          : [],
    );

QuestionItemModel _$QuestionItemModelFromJson(Map<String, dynamic> json) =>
    QuestionItemModel(
      questionSeq: double.tryParse(json['QuestionSeq'].toString())?.toInt(),
      rowKey: double.tryParse(json['RowKey'].toString())?.toInt(),
      questionName: json['QuestionName']?.toString(),
      status: bool.tryParse(json['Status'].toString()),
    );
