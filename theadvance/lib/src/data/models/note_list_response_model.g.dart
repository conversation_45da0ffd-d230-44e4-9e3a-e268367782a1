// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'note_list_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

NoteListResponseModel _$NoteListResponseModelFromJson(
  Map<String, dynamic> json,
) => NoteListResponseModel(
  totalNew: double.tryParse(json['ticket_new_count'].toString())?.toInt(),
  newList: (json['ticket_new_list'] is List)
      ? (json['ticket_new_list'] as List<dynamic>?)
            ?.map((e) => NoteWriteModel.fromJson(e as Map<String, dynamic>))
            .toList()
      : [],
  totalPending: double.tryParse(
    json['ticket_pending_count'].toString(),
  )?.toInt(),
  pendingList: (json['ticket_pending_list'] is List)
      ? (json['ticket_pending_list'] as List<dynamic>?)
            ?.map((e) => NoteWriteModel.fromJson(e as Map<String, dynamic>))
            .toList()
      : [],
  totalSuccess: double.tryParse(
    json['ticket_success_count'].toString(),
  )?.toInt(),
  successList: (json['ticket_success_list'] is List)
      ? (json['ticket_success_list'] as List<dynamic>?)
            ?.map((e) => NoteWriteModel.fromJson(e as Map<String, dynamic>))
            .toList()
      : [],
);
