// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'medical_log_detail_dosage_list_get_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class MedicalLogDetailDosageListGetModelAdapter
    extends TypeAdapter<MedicalLogDetailDosageListGetModel> {
  @override
  final int typeId = 136;

  @override
  MedicalLogDetailDosageListGetModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return MedicalLogDetailDosageListGetModel()
      ..items = (fields[0] as List?)?.cast<CatalogModel?>();
  }

  @override
  void write(BinaryWriter writer, MedicalLogDetailDosageListGetModel obj) {
    writer
      ..writeByte(1)
      ..writeByte(0)
      ..write(obj.items);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MedicalLogDetailDosageListGetModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MedicalLogDetailDosageListGetModel _$MedicalLogDetailDosageListGetModelFromJson(
  Map<String, dynamic> json,
) => MedicalLogDetailDosageListGetModel()
  ..items = (json['items'] is List)
      ? (json['items'] as List<dynamic>?)
            ?.map(
              (e) => e == null || e is! Map
                  ? null
                  : CatalogModel.fromJson(e as Map<String, dynamic>),
            )
            .toList()
      : [];

Map<String, dynamic> _$MedicalLogDetailDosageListGetModelToJson(
  MedicalLogDetailDosageListGetModel instance,
) => <String, dynamic>{'items': instance.items};
