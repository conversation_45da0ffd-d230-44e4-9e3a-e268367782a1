// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'branch_selection_get_bed_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class BranchSelectionGetBedModelAdapter
    extends TypeAdapter<BranchSelectionGetBedModel> {
  @override
  final int typeId = 126;

  @override
  BranchSelectionGetBedModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return BranchSelectionGetBedModel()
      ..items = (fields[0] as List?)?.cast<BedModel?>();
  }

  @override
  void write(BinaryWriter writer, BranchSelectionGetBedModel obj) {
    writer
      ..writeByte(1)
      ..writeByte(0)
      ..write(obj.items);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BranchSelectionGetBedModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BranchSelectionGetBedModel _$BranchSelectionGetBedModelFromJson(
  Map<String, dynamic> json,
) => BranchSelectionGetBedModel()
  ..items = (json['items'] is List)
      ? (json['items'] as List<dynamic>?)
            ?.map(
              (e) => e == null || e is! Map
                  ? null
                  : BedModel.fromJson(e as Map<String, dynamic>),
            )
            .toList()
      : [];

Map<String, dynamic> _$BranchSelectionGetBedModelToJson(
  BranchSelectionGetBedModel instance,
) => <String, dynamic>{'items': instance.items};

BedModel _$BedModelFromJson(Map<String, dynamic> json) => BedModel(
  text: json['text']?.toString(),
  id: json['id']?.toString(),
  code: json['code']?.toString(),
  available: bool.tryParse(json['available'].toString()),
);

Map<String, dynamic> _$BedModelToJson(BedModel instance) => <String, dynamic>{
  'text': instance.text,
  'code': instance.code,
  'id': instance.id,
  'available': instance.available,
};
