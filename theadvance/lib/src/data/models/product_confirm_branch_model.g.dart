// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'product_confirm_branch_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ProductConfirmBranchModel _$ProductConfirmBranchModelFromJson(
  Map<String, dynamic> json,
) => ProductConfirmBranchModel(
  items: (json['Items'] is List)
      ? (json['Items'] as List<dynamic>?)
            ?.map(
              (e) => ProductConfirmBranchItemsModel.fromJson(
                e as Map<String, dynamic>,
              ),
            )
            .toList()
      : [],
  totalRow: double.tryParse(json['TotalRow'].toString())?.toInt(),
);

ProductConfirmBranchItemsModel _$ProductConfirmBranchItemsModelFromJson(
  Map<String, dynamic> json,
) => ProductConfirmBranchItemsModel(
  id: json['Id']?.toString(),
  name: json['Name']?.toString(),
  groupID: json['GroupID']?.toString(),
);
