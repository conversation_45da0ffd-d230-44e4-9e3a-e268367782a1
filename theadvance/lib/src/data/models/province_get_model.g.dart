// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'province_get_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ProvinceGetModelAdapter extends TypeAdapter<ProvinceGetModel> {
  @override
  final int typeId = 123;

  @override
  ProvinceGetModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ProvinceGetModel()
      ..items = (fields[0] as List?)?.cast<ProvinceModel?>();
  }

  @override
  void write(BinaryWriter writer, ProvinceGetModel obj) {
    writer
      ..writeByte(1)
      ..writeByte(0)
      ..write(obj.items);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ProvinceGetModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ProvinceGetModel _$ProvinceGetModelFromJson(Map<String, dynamic> json) =>
    ProvinceGetModel()
      ..items = (json['items'] is List)
          ? (json['items'] as List<dynamic>?)
                ?.map(
                  (e) => e == null || e is! Map
                      ? null
                      : ProvinceModel.fromJson(e as Map<String, dynamic>),
                )
                .toList()
          : [];

Map<String, dynamic> _$ProvinceGetModelToJson(ProvinceGetModel instance) =>
    <String, dynamic>{'items': instance.items};

ProvinceModel _$ProvinceModelFromJson(Map<String, dynamic> json) =>
    ProvinceModel(
      province: json['province']?.toString(),
      total: double.tryParse(json['total'].toString())?.toInt(),
    );

Map<String, dynamic> _$ProvinceModelToJson(ProvinceModel instance) =>
    <String, dynamic>{'province': instance.province, 'total': instance.total};
