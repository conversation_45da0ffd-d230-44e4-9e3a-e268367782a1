// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ticket_type_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TicketTypeModel _$TicketTypeModelFromJson(Map<String, dynamic> json) =>
    TicketTypeModel(
      items: (json['items'] is List)
          ? (json['items'] as List<dynamic>)
                .map(
                  (e) => e == null || e is! Map
                      ? null
                      : TicketTypeItemsModel.fromJson(
                          e as Map<String, dynamic>,
                        ),
                )
                .toList()
          : [],
    );

Map<String, dynamic> _$TicketTypeModelToJson(TicketTypeModel instance) =>
    <String, dynamic>{'items': instance.items};

TicketTypeItemsModel _$TicketTypeItemsModelFromJson(
  Map<String, dynamic> json,
) => TicketTypeItemsModel(
  id: json['id']?.toString(),
  name: json['name']?.toString(),
);

Map<String, dynamic> _$TicketTypeItemsModelToJson(
  TicketTypeItemsModel instance,
) => <String, dynamic>{'id': instance.id, 'name': instance.name};
