// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'result_of_fit_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ResultOfFitModel _$ResultOfFitModelFromJson(Map<String, dynamic> json) =>
    ResultOfFitModel(
      items: (json['items'] is List)
          ? (json['items'] as List<dynamic>)
                .map(
                  (e) => e == null || e is! Map
                      ? null
                      : ResultOfFitItemsModel.fromJson(
                          e as Map<String, dynamic>,
                        ),
                )
                .toList()
          : [],
    );

Map<String, dynamic> _$ResultOfFitModelToJson(ResultOfFitModel instance) =>
    <String, dynamic>{'items': instance.items};

ResultOfFitItemsModel _$ResultOfFitItemsModelFromJson(
  Map<String, dynamic> json,
) => ResultOfFitItemsModel(
  rowID: double.tryParse(json['RowID'].toString())?.toInt(),
  customerID: double.tryParse(json['CustomerID'].toString())?.toInt(),
  serviceName: json['ServiceName']?.toString(),
  lSServiceID: double.tryParse(json['LSServiceID'].toString())?.toInt(),
  resultNo: double.tryParse(json['ResultNo'].toString())?.toInt(),
  resultDate: json['ResultDate']?.toString(),
  resultStatus: json['ResultStatus']?.toString(),
  treatmentArea: json['TreatmentArea']?.toString(),
  mhs: json['MHS']?.toString(),
  mHSEmpID: double.tryParse(json['MHSEmpID'].toString())?.toInt(),
  mHSByEmpName: json['MHSByEmpName']?.toString(),
  weight: json['Weight']?.toString(),
  result: json['Result']?.toString(),
  detail: json['Detail']?.toString(),
  method: double.tryParse(json['Method'].toString())?.toInt(),
  loseWeight: json['LoseWeight']?.toString(),
);

Map<String, dynamic> _$ResultOfFitItemsModelToJson(
  ResultOfFitItemsModel instance,
) => <String, dynamic>{
  'RowID': instance.rowID,
  'CustomerID': instance.customerID,
  'ServiceName': instance.serviceName,
  'LSServiceID': instance.lSServiceID,
  'ResultNo': instance.resultNo,
  'ResultDate': instance.resultDate,
  'ResultStatus': instance.resultStatus,
  'TreatmentArea': instance.treatmentArea,
  'MHS': instance.mhs,
  'MHSEmpID': instance.mHSEmpID,
  'MHSByEmpName': instance.mHSByEmpName,
  'Weight': instance.weight,
  'Result': instance.result,
  'Detail': instance.detail,
  'Method': instance.method,
  'LoseWeight': instance.loseWeight,
};
