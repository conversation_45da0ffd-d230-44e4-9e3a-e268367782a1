// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'branch_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BranchModelV2 _$BranchModelV2FromJson(Map<String, dynamic> json) =>
    BranchModelV2(
      items: (json['items'] is List)
          ? (json['items'] as List<dynamic>)
                .map(
                  (e) => e == null || e is! Map
                      ? null
                      : BranchItemsV2Model.fromJson(e as Map<String, dynamic>),
                )
                .toList()
          : [],
    );

Map<String, dynamic> _$BranchModelV2ToJson(BranchModelV2 instance) =>
    <String, dynamic>{'items': instance.items};

BranchItemsV2Model _$BranchItemsV2ModelFromJson(Map<String, dynamic> json) =>
    BranchItemsV2Model(
      departmentID: json['DepartmentID']?.toString(),
      departmentName: json['DepartmentName']?.toString(),
      isParent: double.tryParse(json['IsParent'].toString())?.toInt(),
      parentId: json['ParentId']?.toString(),
      nameCompare: json['NameCompare']?.toString(),
      pathOrgCode: json['PathOrgCode']?.toString(),
      searchPathOrgCode: json['SearchPathOrgCode']?.toString(),
      pathOrgName: json['PathOrgName']?.toString(),
      organizationID: json['OrganizationID']?.toString(),
      status: double.tryParse(json['Status'].toString())?.toInt(),
      levelId: double.tryParse(json['LevelId'].toString())?.toInt(),
      rootLevel: double.tryParse(json['RootLevel'].toString())?.toInt(),
      level0ID: json['Level0ID']?.toString(),
      level1ID: json['Level1ID']?.toString(),
      level2ID: json['Level2ID']?.toString(),
      level3ID: json['Level3ID']?.toString(),
      level4ID: json['Level4ID']?.toString(),
      branchIDDepartmentID: json['BranchID_DepartmentID']?.toString(),
      index: double.tryParse(json['Index'].toString())?.toInt(),
      departmentLevelID: json['DepartmentLevelID']?.toString(),
      groupID: json['GroupID']?.toString(),
    );

Map<String, dynamic> _$BranchItemsV2ModelToJson(BranchItemsV2Model instance) =>
    <String, dynamic>{
      'DepartmentID': instance.departmentID,
      'DepartmentName': instance.departmentName,
      'IsParent': instance.isParent,
      'ParentId': instance.parentId,
      'NameCompare': instance.nameCompare,
      'PathOrgCode': instance.pathOrgCode,
      'SearchPathOrgCode': instance.searchPathOrgCode,
      'PathOrgName': instance.pathOrgName,
      'OrganizationID': instance.organizationID,
      'Status': instance.status,
      'LevelId': instance.levelId,
      'RootLevel': instance.rootLevel,
      'Level0ID': instance.level0ID,
      'Level1ID': instance.level1ID,
      'Level2ID': instance.level2ID,
      'Level3ID': instance.level3ID,
      'Level4ID': instance.level4ID,
      'BranchID_DepartmentID': instance.branchIDDepartmentID,
      'Index': instance.index,
      'DepartmentLevelID': instance.departmentLevelID,
      'GroupID': instance.groupID,
    };
