// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'important_notes_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ImportantNotesModel _$ImportantNotesModelFromJson(Map<String, dynamic> json) =>
    ImportantNotesModel()
      ..listNoteCategory = (json['items'] is List)
          ? (json['items'] as List<dynamic>?)
                ?.map(
                  (e) => e == null || e is! Map
                      ? null
                      : ImportantNoteCategoryModel.fromJson(
                          e as Map<String, dynamic>,
                        ),
                )
                .toList()
          : [];

Map<String, dynamic> _$ImportantNotesModelToJson(
  ImportantNotesModel instance,
) => <String, dynamic>{'items': instance.listNoteCategory};

ImportantNoteCategoryModel _$ImportantNoteCategoryModelFromJson(
  Map<String, dynamic> json,
) => ImportantNoteCategoryModel(
  code: json['code']?.toString(),
  codeName: json['code_name']?.toString(),
  detail:
      ((json['detail'] is List)
          ? (json['detail'] as List<dynamic>?)
                ?.map(
                  (e) => e == null || e is! Map
                      ? null
                      : ImportantNoteModel.fromJson(e as Map<String, dynamic>),
                )
                .toList()
          : []) ??
      [],
);

Map<String, dynamic> _$ImportantNoteCategoryModelToJson(
  ImportantNoteCategoryModel instance,
) => <String, dynamic>{
  'code': instance.code,
  'code_name': instance.codeName,
  'detail': instance.detail,
};

ImportantNoteModel _$ImportantNoteModelFromJson(Map<String, dynamic> json) =>
    ImportantNoteModel(
      date: json['date']?.toString(),
      note: json['note']?.toString(),
      treatment: json['treatment']?.toString(),
      id: json['id']?.toString(),
      images:
          ((json['images'] is List)
              ? (json['images'] as List<dynamic>?)
                    ?.map((e) => e?.toString())
                    .toList()
              : []) ??
          [],
    );

Map<String, dynamic> _$ImportantNoteModelToJson(ImportantNoteModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'date': instance.date,
      'note': instance.note,
      'treatment': instance.treatment,
      'images': instance.images,
    };

ImageModel _$ImageModelFromJson(Map<String, dynamic> json) =>
    ImageModel(name: json['name']?.toString(), link: json['link']?.toString());

Map<String, dynamic> _$ImageModelToJson(ImageModel instance) =>
    <String, dynamic>{'name': instance.name, 'link': instance.link};
