// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'create_customer_survey_load_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CreateCustomerSurveyLoadModel _$CreateCustomerSurveyLoadModelFromJson(
  Map<String, dynamic> json,
) => CreateCustomerSurveyLoadModel(
  items: (json['items'] is List)
      ? (json['items'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : CreateCustomerSurveyLoadItemsModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
);

Map<String, dynamic> _$CreateCustomerSurveyLoadModelToJson(
  CreateCustomerSurveyLoadModel instance,
) => <String, dynamic>{'items': instance.items};

CreateCustomerSurveyLoadItemsModel _$CreateCustomerSurveyLoadItemsModelFromJson(
  Map<String, dynamic> json,
) => CreateCustomerSurveyLoadItemsModel(
  visitReasonID: double.tryParse(json['VisitReasonID'].toString())?.toInt(),
  visitReasonName: json['VisitReasonName']?.toString(),
);

Map<String, dynamic> _$CreateCustomerSurveyLoadItemsModelToJson(
  CreateCustomerSurveyLoadItemsModel instance,
) => <String, dynamic>{
  'VisitReasonID': instance.visitReasonID,
  'VisitReasonName': instance.visitReasonName,
};
