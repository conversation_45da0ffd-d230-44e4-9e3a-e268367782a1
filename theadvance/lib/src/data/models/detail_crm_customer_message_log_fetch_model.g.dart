// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'detail_crm_customer_message_log_fetch_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DetailCrmCustomerMessageLogFetchModel
_$DetailCrmCustomerMessageLogFetchModelFromJson(Map<String, dynamic> json) =>
    DetailCrmCustomerMessageLogFetchModel(
      items: (json['items'] is List)
          ? (json['items'] as List<dynamic>)
                .map(
                  (e) => e == null || e is! Map
                      ? null
                      : DetailCrmMessageLogItemModel.fromJson(
                          e as Map<String, dynamic>,
                        ),
                )
                .toList()
          : [],
    );

Map<String, dynamic> _$DetailCrmCustomerMessageLogFetchModelToJson(
  DetailCrmCustomerMessageLogFetchModel instance,
) => <String, dynamic>{'items': instance.items};

DetailCrmMessageLogItemModel _$DetailCrmMessageLogItemModelFromJson(
  Map<String, dynamic> json,
) => DetailCrmMessageLogItemModel(
  status: json['status']?.toString(),
  message: json['message']?.toString(),
  createDate: json['create_date']?.toString(),
  type: json['type']?.toString(),
  employeeName: json['employee_name']?.toString(),
  desc: json['desc']?.toString(),
);

Map<String, dynamic> _$DetailCrmMessageLogItemModelToJson(
  DetailCrmMessageLogItemModel instance,
) => <String, dynamic>{
  'status': instance.status,
  'message': instance.message,
  'create_date': instance.createDate,
  'type': instance.type,
  'employee_name': instance.employeeName,
  'desc': instance.desc,
};
