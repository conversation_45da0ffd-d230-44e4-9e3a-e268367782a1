// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'medical_department_list_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class MedicalDepartmentListModelAdapter
    extends TypeAdapter<MedicalDepartmentListModel> {
  @override
  final int typeId = 130;

  @override
  MedicalDepartmentListModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return MedicalDepartmentListModel()
      ..items = (fields[0] as List?)?.cast<MedicalDepartmentModel?>();
  }

  @override
  void write(BinaryWriter writer, MedicalDepartmentListModel obj) {
    writer
      ..writeByte(1)
      ..writeByte(0)
      ..write(obj.items);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MedicalDepartmentListModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MedicalDepartmentListModel _$MedicalDepartmentListModelFromJson(
  Map<String, dynamic> json,
) => MedicalDepartmentListModel()
  ..items = (json['items'] is List)
      ? (json['items'] as List<dynamic>?)
            ?.map(
              (e) => e == null || e is! Map
                  ? null
                  : MedicalDepartmentModel.fromJson(e as Map<String, dynamic>),
            )
            .toList()
      : [];

Map<String, dynamic> _$MedicalDepartmentListModelToJson(
  MedicalDepartmentListModel instance,
) => <String, dynamic>{'items': instance.items};

MedicalDepartmentModel _$MedicalDepartmentModelFromJson(
  Map<String, dynamic> json,
) => MedicalDepartmentModel(
  code: json['code']?.toString(),
  name: json['name']?.toString(),
);

Map<String, dynamic> _$MedicalDepartmentModelToJson(
  MedicalDepartmentModel instance,
) => <String, dynamic>{'code': instance.code, 'name': instance.name};
