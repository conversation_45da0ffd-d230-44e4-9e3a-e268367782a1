// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'story_list_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

StoryListModel _$StoryListModelFromJson(Map<String, dynamic> json) =>
    StoryListModel(
      total: double.tryParse(json['total'].toString())?.toInt(),
      limit: double.tryParse(json['limit'].toString())?.toInt(),
      page: double.tryParse(json['page'].toString())?.toInt(),
      items: (json['items'] is List)
          ? (json['items'] as List<dynamic>)
                .map(
                  (e) => e == null || e is! Map
                      ? null
                      : StoryListItemsModel.fromJson(e as Map<String, dynamic>),
                )
                .toList()
          : [],
    );

Map<String, dynamic> _$StoryListModelToJson(StoryListModel instance) =>
    <String, dynamic>{
      'total': instance.total,
      'limit': instance.limit,
      'page': instance.page,
      'items': instance.items,
    };

StoryListItemsModel _$StoryListItemsModelFromJson(Map<String, dynamic> json) =>
    StoryListItemsModel(
      id: json['id']?.toString(),
      content: json['content']?.toString(),
      attachment: (json['attachment'] is List)
          ? (json['attachment'] as List<dynamic>)
                .map(
                  (e) => e == null || e is! Map
                      ? null
                      : AttachmentModel.fromJson(e as Map<String, dynamic>),
                )
                .toList()
          : [],
      location: json['location'] == null || json['location'] is! Map
          ? null
          : StoryListItemsLocationModel.fromJson(
              json['location'] as Map<String, dynamic>,
            ),
      tags: (json['tags'] is List)
          ? (json['tags'] as List<dynamic>).map((e) => e as String).toList()
          : [],
      theme: (json['theme'] is List)
          ? (json['theme'] as List<dynamic>?)?.map((e) => e as String).toList()
          : [],
      createdBy: json['createdBy']?.toString(),
      createdAt: json['createdAt']?.toString(),
      updatedAt: json['updatedAt']?.toString(),
      status: json['status']?.toString(),
      createdByInfo:
          json['createdByInfo'] == null || json['createdByInfo'] is! Map
          ? null
          : CreatedByInfoModel.fromJson(
              json['createdByInfo'] as Map<String, dynamic>,
            ),
      reactCount: double.tryParse(json['reactCount'].toString())?.toInt(),
      commentCount: double.tryParse(json['commentCount'].toString())?.toInt(),
      reactInfo: json['reactInfo'] == null || json['reactInfo'] is! Map
          ? null
          : ReactInfoModel.fromJson(json['reactInfo'] as Map<String, dynamic>),
      tagsInfo: (json['tagsInfo'] is List)
          ? (json['tagsInfo'] as List<dynamic>?)
                ?.map((e) => TagsInfoModel.fromJson(e as Map<String, dynamic>))
                .toList()
          : [],
      pollUser: (json['pollUser'] is List)
          ? (json['pollUser'] as List<dynamic>?)
                ?.map((e) => PollInfoModel.fromJson(e as Map<String, dynamic>))
                .toList()
          : [],
      mention: (json['mention'] is List)
          ? (json['mention'] as List<dynamic>?)
                ?.map((e) => MentionModel.fromJson(e as Map<String, dynamic>))
                .toList()
          : [],
      emoji: json['emoji'] == null || json['emoji'] is! Map
          ? null
          : StoryListEmojiModel.fromJson(json['emoji'] as Map<String, dynamic>),
      poll: json['poll'] == null || json['poll'] is! Map
          ? null
          : PollModel.fromJson(json['poll'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$StoryListItemsModelToJson(
  StoryListItemsModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'content': instance.content,
  'attachment': instance.attachment,
  'location': instance.location,
  'tags': instance.tags,
  'theme': instance.theme,
  'createdBy': instance.createdBy,
  'createdAt': instance.createdAt,
  'updatedAt': instance.updatedAt,
  'status': instance.status,
  'createdByInfo': instance.createdByInfo,
  'reactCount': instance.reactCount,
  'commentCount': instance.commentCount,
  'reactInfo': instance.reactInfo,
  'pollUser': instance.pollUser,
  'tagsInfo': instance.tagsInfo,
  'mention': instance.mention,
  'emoji': instance.emoji,
  'poll': instance.poll,
};

StoryListItemsLocationModel _$StoryListItemsLocationModelFromJson(
  Map<String, dynamic> json,
) => StoryListItemsLocationModel(
  long: json['long']?.toString(),
  lat: json['lat']?.toString(),
  name: json['name']?.toString(),
  address: json['address']?.toString(),
);

Map<String, dynamic> _$StoryListItemsLocationModelToJson(
  StoryListItemsLocationModel instance,
) => <String, dynamic>{
  'long': instance.long,
  'lat': instance.lat,
  'name': instance.name,
  'address': instance.address,
};

StoryListEmojiModel _$StoryListEmojiModelFromJson(Map<String, dynamic> json) =>
    StoryListEmojiModel(
      text: json['text']?.toString(),
      icon: json['icon']?.toString(),
    );

Map<String, dynamic> _$StoryListEmojiModelToJson(
  StoryListEmojiModel instance,
) => <String, dynamic>{'text': instance.text, 'icon': instance.icon};
