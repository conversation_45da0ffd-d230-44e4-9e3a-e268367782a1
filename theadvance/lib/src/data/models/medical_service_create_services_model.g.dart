// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'medical_service_create_services_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class MedicalServiceCreationServicesModelAdapter
    extends TypeAdapter<MedicalServiceCreationServicesModel> {
  @override
  final int typeId = 144;

  @override
  MedicalServiceCreationServicesModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return MedicalServiceCreationServicesModel()
      ..items = (fields[0] as List?)
          ?.cast<MedicalServiceCreationServiceModel?>();
  }

  @override
  void write(BinaryWriter writer, MedicalServiceCreationServicesModel obj) {
    writer
      ..writeByte(1)
      ..writeByte(0)
      ..write(obj.items);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MedicalServiceCreationServicesModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MedicalServiceCreationServicesModel
_$MedicalServiceCreationServicesModelFromJson(Map<String, dynamic> json) =>
    MedicalServiceCreationServicesModel()
      ..items = (json['items'] is List)
          ? (json['items'] as List<dynamic>?)
                ?.map(
                  (e) => e == null || e is! Map
                      ? null
                      : MedicalServiceCreationServiceModel.fromJson(
                          e as Map<String, dynamic>,
                        ),
                )
                .toList()
          : [];

Map<String, dynamic> _$MedicalServiceCreationServicesModelToJson(
  MedicalServiceCreationServicesModel instance,
) => <String, dynamic>{'items': instance.items};

MedicalServiceCreationServiceModel _$MedicalServiceCreationServiceModelFromJson(
  Map<String, dynamic> json,
) => MedicalServiceCreationServiceModel(
  lsServiceId: json['ls_service_id']?.toString(),
  lsServiceCode: json['ls_service_code']?.toString(),
  service: json['service']?.toString(),
  departmentCode: json['department_code']?.toString(),
);

Map<String, dynamic> _$MedicalServiceCreationServiceModelToJson(
  MedicalServiceCreationServiceModel instance,
) => <String, dynamic>{
  'ls_service_id': instance.lsServiceId,
  'ls_service_code': instance.lsServiceCode,
  'service': instance.service,
  'department_code': instance.departmentCode,
};
