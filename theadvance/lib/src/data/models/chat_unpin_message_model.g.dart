// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'chat_unpin_message_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ChatUnpinMessageModel _$ChatUnpinMessageModelFromJson(
  Map<String, dynamic> json,
) => ChatUnpinMessageModel(
  items: (json['items'] is List)
      ? (json['items'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : ChatUnpinMessageItemsModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
);

Map<String, dynamic> _$ChatUnpinMessageModelToJson(
  ChatUnpinMessageModel instance,
) => <String, dynamic>{'items': instance.items};

ChatUnpinMessageItemsModel _$ChatUnpinMessageItemsModelFromJson(
  Map<String, dynamic> json,
) => ChatUnpinMessageItemsModel(id: json['id']?.toString());

Map<String, dynamic> _$ChatUnpinMessageItemsModelToJson(
  ChatUnpinMessageItemsModel instance,
) => <String, dynamic>{'id': instance.id};
