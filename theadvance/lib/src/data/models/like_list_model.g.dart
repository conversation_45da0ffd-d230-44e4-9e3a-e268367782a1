// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'like_list_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LikeListModel _$LikeListModelFromJson(Map<String, dynamic> json) =>
    LikeListModel(
      total: double.tryParse(json['total'].toString())?.toInt(),
      limit: double.tryParse(json['limit'].toString())?.toInt(),
      page: double.tryParse(json['page'].toString())?.toInt(),
      items: (json['items'] is List)
          ? (json['items'] as List<dynamic>)
                .map(
                  (e) => e == null || e is! Map
                      ? null
                      : LikeListItemsModel.fromJson(e as Map<String, dynamic>),
                )
                .toList()
          : [],
    );

Map<String, dynamic> _$LikeListModelToJson(LikeListModel instance) =>
    <String, dynamic>{
      'total': instance.total,
      'limit': instance.limit,
      'page': instance.page,
      'items': instance.items,
    };

LikeListItemsModel _$LikeListItemsModelFromJson(Map<String, dynamic> json) =>
    LikeListItemsModel(
      id: json['_id']?.toString(),
      postId: json['postId']?.toString(),
      reactType: json['reactType']?.toString(),
      createdBy: json['createdBy']?.toString(),
      createdAt: json['createdAt']?.toString(),
      updatedAt: json['updatedAt']?.toString(),
      createdByInfo:
          json['createdByInfo'] == null || json['createdByInfo'] is! Map
          ? null
          : CreatedByInfoModel.fromJson(
              json['createdByInfo'] as Map<String, dynamic>,
            ),
    );

Map<String, dynamic> _$LikeListItemsModelToJson(LikeListItemsModel instance) =>
    <String, dynamic>{
      '_id': instance.id,
      'postId': instance.postId,
      'reactType': instance.reactType,
      'createdBy': instance.createdBy,
      'createdAt': instance.createdAt,
      'updatedAt': instance.updatedAt,
      'createdByInfo': instance.createdByInfo,
    };
