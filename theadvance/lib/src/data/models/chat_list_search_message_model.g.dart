// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'chat_list_search_message_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ChatListSearchMessageModel _$ChatListSearchMessageModelFromJson(
  Map<String, dynamic> json,
) => ChatListSearchMessageModel(
  total: double.tryParse(json['total'].toString())?.toInt(),
  items: (json['items'] is List)
      ? (json['items'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : ChatListSearchMessageItemsModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
);

Map<String, dynamic> _$ChatListSearchMessageModelToJson(
  ChatListSearchMessageModel instance,
) => <String, dynamic>{'total': instance.total, 'items': instance.items};

ChatListSearchMessageItemsModel _$ChatListSearchMessageItemsModelFromJson(
  Map<String, dynamic> json,
) => ChatListSearchMessageItemsModel(
  content: json['content']?.toString(),
  id: json['id']?.toString(),
  createdAt: json['createdAt']?.toString(),
  createdByInfo: json['createdByInfo'] == null || json['createdByInfo'] is! Map
      ? null
      : ChatListSearchMessageItemsCreatedByInfoModel.fromJson(
          json['createdByInfo'] as Map<String, dynamic>,
        ),
  conversation: json['conversation'] == null || json['conversation'] is! Map
      ? null
      : ChatListSearchMessageItemsConversationModel.fromJson(
          json['conversation'] as Map<String, dynamic>,
        ),
  parseMode: json['parseMode']?.toString(),
);

Map<String, dynamic> _$ChatListSearchMessageItemsModelToJson(
  ChatListSearchMessageItemsModel instance,
) => <String, dynamic>{
  'content': instance.content,
  'id': instance.id,
  'createdAt': instance.createdAt,
  'createdByInfo': instance.createdByInfo,
  'conversation': instance.conversation,
  'parseMode': instance.parseMode,
};

ChatListSearchMessageItemsCreatedByInfoModel
_$ChatListSearchMessageItemsCreatedByInfoModelFromJson(
  Map<String, dynamic> json,
) => ChatListSearchMessageItemsCreatedByInfoModel(
  username: json['username']?.toString(),
  avatar: json['avatar']?.toString(),
  createdAt: json['createdAt']?.toString(),
  departmentName: json['departmentName']?.toString(),
  id: json['id']?.toString(),
  isOnline: bool.tryParse(json['isOnline'].toString()),
  name: json['name']?.toString(),
  phone: json['phone']?.toString(),
  product: json['product']?.toString(),
  takeCareGroupId: json['takeCareGroupId']?.toString(),
  type: json['type']?.toString(),
  updatedAt: json['updatedAt']?.toString(),
);

Map<String, dynamic> _$ChatListSearchMessageItemsCreatedByInfoModelToJson(
  ChatListSearchMessageItemsCreatedByInfoModel instance,
) => <String, dynamic>{
  'username': instance.username,
  'avatar': instance.avatar,
  'createdAt': instance.createdAt,
  'departmentName': instance.departmentName,
  'id': instance.id,
  'isOnline': instance.isOnline,
  'name': instance.name,
  'phone': instance.phone,
  'product': instance.product,
  'takeCareGroupId': instance.takeCareGroupId,
  'type': instance.type,
  'updatedAt': instance.updatedAt,
};

ChatListSearchMessageItemsConversationModel
_$ChatListSearchMessageItemsConversationModelFromJson(
  Map<String, dynamic> json,
) => ChatListSearchMessageItemsConversationModel(
  id: json['id']?.toString(),
  avatar: json['avatar']?.toString(),
  name: json['name']?.toString(),
  isGroup: bool.tryParse(json['isGroup'].toString()),
  members: (json['members'] is List)
      ? (json['members'] as List<dynamic>).map((e) => e?.toString()).toList()
      : [],
);

Map<String, dynamic> _$ChatListSearchMessageItemsConversationModelToJson(
  ChatListSearchMessageItemsConversationModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'avatar': instance.avatar,
  'name': instance.name,
  'isGroup': instance.isGroup,
  'members': instance.members,
};
