// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'detail_crm_customer_advice_fetch_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DetailCrmCustomerAdviceFetchModel _$DetailCrmCustomerAdviceFetchModelFromJson(
  Map<String, dynamic> json,
) => DetailCrmCustomerAdviceFetchModel(
  items: (json['items'] is List)
      ? (json['items'] as List<dynamic>?)
            ?.map(
              (e) =>
                  DetailCrmAdviceItemModel.fromJson(e as Map<String, dynamic>),
            )
            .toList()
      : [],
);

Map<String, dynamic> _$DetailCrmCustomerAdviceFetchModelToJson(
  DetailCrmCustomerAdviceFetchModel instance,
) => <String, dynamic>{'items': instance.items};

DetailCrmAdviceItemModel _$DetailCrmAdviceItemModelFromJson(
  Map<String, dynamic> json,
) => DetailCrmAdviceItemModel(
  id: double.tryParse(json['id'].toString())?.toInt(),
  note: json['note']?.toString(),
  noteHtml: json['note_html']?.toString(),
  createdDate: json['created_date']?.toString(),
  workStatus: json['work_status']?.toString(),
  ticketId: json['ticket_id'],
  ticketTeam: json['ticket_team'],
);

Map<String, dynamic> _$DetailCrmAdviceItemModelToJson(
  DetailCrmAdviceItemModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'note': instance.note,
  'note_html': instance.noteHtml,
  'created_date': instance.createdDate,
  'work_status': instance.workStatus,
  'ticket_id': instance.ticketId,
  'ticket_team': instance.ticketTeam,
};
