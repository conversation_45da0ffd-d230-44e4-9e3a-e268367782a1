// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_check_permission_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserCheckPermissionModel _$UserCheckPermissionModelFromJson(
  Map<String, dynamic> json,
) => UserCheckPermissionModel(
  permission: json['permission'] == null || json['permission'] is! Map
      ? null
      : UserCheckPermissionPermissionModel.fromJson(
          json['permission'] as Map<String, dynamic>,
        ),
  permissionBySegment:
      json['permissionBySegment'] == null || json['permissionBySegment'] is! Map
      ? null
      : PermissionBySegmentModel.fromJson(
          json['permissionBySegment'] as Map<String, dynamic>,
        ),
);

Map<String, dynamic> _$UserCheckPermissionModelToJson(
  UserCheckPermissionModel instance,
) => <String, dynamic>{
  'permission': instance.permission,
  'permissionBySegment': instance.permissionBySegment,
};

PermissionBySegmentModel _$PermissionBySegmentModelFromJson(
  Map<String, dynamic> json,
) => PermissionBySegmentModel(
  accessConsultationTab: bool.tryParse(
    json['accessConsultationTab'].toString(),
  ),
  accessConsultationContentTab: bool.tryParse(
    json['accessConsultationContentTab'].toString(),
  ),
);

Map<String, dynamic> _$PermissionBySegmentModelToJson(
  PermissionBySegmentModel instance,
) => <String, dynamic>{
  'accessConsultationTab': instance.accessConsultationTab,
  'accessConsultationContentTab': instance.accessConsultationContentTab,
};

UserCheckPermissionPermissionModel _$UserCheckPermissionPermissionModelFromJson(
  Map<String, dynamic> json,
) => UserCheckPermissionPermissionModel(
  scanMealQRCode: bool.tryParse(json['scanMealQRCode'].toString()),
  checkIn: json['checkIn'] == null || json['checkIn'] is! Map
      ? null
      : UserCheckPermissionPermissionCheckInModel.fromJson(
          json['checkIn'] as Map<String, dynamic>,
        ),
  screenCapture: bool.tryParse(json['screenCapture'].toString()),
  accessGlobalDomain: bool.tryParse(json['accessGlobalDomain'].toString()),
);

Map<String, dynamic> _$UserCheckPermissionPermissionModelToJson(
  UserCheckPermissionPermissionModel instance,
) => <String, dynamic>{
  'scanMealQRCode': instance.scanMealQRCode,
  'checkIn': instance.checkIn,
  'screenCapture': instance.screenCapture,
  'accessGlobalDomain': instance.accessGlobalDomain,
};

UserCheckPermissionPermissionCheckInModel
_$UserCheckPermissionPermissionCheckInModelFromJson(
  Map<String, dynamic> json,
) => UserCheckPermissionPermissionCheckInModel(
  isCheckFace: bool.tryParse(json['isCheckFace'].toString()),
);

Map<String, dynamic> _$UserCheckPermissionPermissionCheckInModelToJson(
  UserCheckPermissionPermissionCheckInModel instance,
) => <String, dynamic>{'isCheckFace': instance.isCheckFace};
