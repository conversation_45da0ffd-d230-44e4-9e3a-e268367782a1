// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'group_chat_detail_get_user_exception_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GroupChatDetailGetUserExceptionResponseModel
_$GroupChatDetailGetUserExceptionResponseModelFromJson(
  Map<String, dynamic> json,
) =>
    GroupChatDetailGetUserExceptionResponseModel(
        data: json['data'] == null || json['data'] is! Map
            ? null
            : GroupChatDetailGetUserExceptionModel.fromJson(
                json['data'] as Map<String, dynamic>,
              ),
      )
      ..errorCode = double.tryParse(json['code'].toString())?.toInt()
      ..errorMessage = json['message']?.toString();
