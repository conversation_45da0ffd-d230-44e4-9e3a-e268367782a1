// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'chat_get_pin_list_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ChatGetPinListModel _$ChatGetPinListModelFromJson(Map<String, dynamic> json) =>
    ChatGetPinListModel(
      messages: json['messages'] == null || json['messages'] is! Map
          ? null
          : ChatGetPinListMessagesModel.fromJson(
              json['messages'] as Map<String, dynamic>,
            ),
    );

Map<String, dynamic> _$ChatGetPinListModelToJson(
  ChatGetPinListModel instance,
) => <String, dynamic>{'messages': instance.messages};

ChatGetPinListMessagesModel _$ChatGetPinListMessagesModelFromJson(
  Map<String, dynamic> json,
) => ChatGetPinListMessagesModel(
  total: double.tryParse(json['total'].toString())?.toInt(),
  items: (json['items'] is List)
      ? (json['items'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : ChatItemsModel.fromJson(e as Map<String, dynamic>),
            )
            .toList()
      : [],
  limit: double.tryParse(json['limit'].toString())?.toInt(),
);

Map<String, dynamic> _$ChatGetPinListMessagesModelToJson(
  ChatGetPinListMessagesModel instance,
) => <String, dynamic>{
  'total': instance.total,
  'items': instance.items,
  'limit': instance.limit,
};
