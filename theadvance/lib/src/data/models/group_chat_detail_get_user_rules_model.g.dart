// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'group_chat_detail_get_user_rules_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GroupChatDetailGetUserRulesModel _$GroupChatDetailGetUserRulesModelFromJson(
  Map<String, dynamic> json,
) => GroupChatDetailGetUserRulesModel(
  role: json['role'] == null || json['role'] is! Map
      ? null
      : GroupChatDetailGetUserRulesRoleModel.fromJson(
          json['role'] as Map<String, dynamic>,
        ),
);

Map<String, dynamic> _$GroupChatDetailGetUserRulesModelToJson(
  GroupChatDetailGetUserRulesModel instance,
) => <String, dynamic>{'role': instance.role};

GroupChatDetailGetUserRulesRoleModel
_$GroupChatDetailGetUserRulesRoleModelFromJson(Map<String, dynamic> json) =>
    GroupChatDetailGetUserRulesRoleModel(
      name: json['name']?.toString(),
      roleText: json['roleText']?.toString(),
      rules: (json['rules'] is List)
          ? (json['rules'] as List<dynamic>)
                .map(
                  (e) => e == null || e is! Map
                      ? null
                      : GroupChatDetailGetUserRulesRoleRulesModel.fromJson(
                          e as Map<String, dynamic>,
                        ),
                )
                .toList()
          : [],
    );

Map<String, dynamic> _$GroupChatDetailGetUserRulesRoleModelToJson(
  GroupChatDetailGetUserRulesRoleModel instance,
) => <String, dynamic>{
  'name': instance.name,
  'roleText': instance.roleText,
  'rules': instance.rules,
};

GroupChatDetailGetUserRulesRoleRulesModel
_$GroupChatDetailGetUserRulesRoleRulesModelFromJson(
  Map<String, dynamic> json,
) => GroupChatDetailGetUserRulesRoleRulesModel(
  id: json['id']?.toString(),
  name: json['name']?.toString(),
  actions: json['actions']?.toString(),
  subject: json['subject']?.toString(),
  createdAt: json['createdAt']?.toString(),
  updatedAt: json['updatedAt']?.toString(),
);

Map<String, dynamic> _$GroupChatDetailGetUserRulesRoleRulesModelToJson(
  GroupChatDetailGetUserRulesRoleRulesModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'actions': instance.actions,
  'subject': instance.subject,
  'createdAt': instance.createdAt,
  'updatedAt': instance.updatedAt,
};
