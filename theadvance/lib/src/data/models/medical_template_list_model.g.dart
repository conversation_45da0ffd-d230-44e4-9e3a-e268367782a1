// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'medical_template_list_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class MedicalTemplateListModelAdapter
    extends TypeAdapter<MedicalTemplateListModel> {
  @override
  final int typeId = 140;

  @override
  MedicalTemplateListModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return MedicalTemplateListModel()
      ..items = (fields[0] as List?)?.cast<CatalogModel?>();
  }

  @override
  void write(BinaryWriter writer, MedicalTemplateListModel obj) {
    writer
      ..writeByte(1)
      ..writeByte(0)
      ..write(obj.items);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MedicalTemplateListModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MedicalTemplateListModel _$MedicalTemplateListModelFromJson(
  Map<String, dynamic> json,
) => MedicalTemplateListModel()
  ..items = (json['items'] is List)
      ? (json['items'] as List<dynamic>?)
            ?.map(
              (e) => e == null || e is! Map
                  ? null
                  : CatalogModel.fromJson(e as Map<String, dynamic>),
            )
            .toList()
      : [];

Map<String, dynamic> _$MedicalTemplateListModelToJson(
  MedicalTemplateListModel instance,
) => <String, dynamic>{'items': instance.items};
