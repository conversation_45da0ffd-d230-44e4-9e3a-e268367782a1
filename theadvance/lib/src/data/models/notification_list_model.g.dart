// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'notification_list_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

NotificationListModel _$NotificationListModelFromJson(
  Map<String, dynamic> json,
) => NotificationListModel(
  totalPages: double.tryParse(json['totalPages'].toString())?.toInt(),
  nextPage: bool.tryParse(json['nextPage'].toString()),
  prevPage: bool.tryParse(json['prevPage'].toString()),
  limit: double.tryParse(json['limit'].toString())?.toInt(),
  page: double.tryParse(json['page'].toString())?.toInt(),
  total: double.tryParse(json['total'].toString())?.toInt(),
  items: (json['items'] is List)
      ? (json['items'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : NotificationListItemsModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
);

Map<String, dynamic> _$NotificationListModelToJson(
  NotificationListModel instance,
) => <String, dynamic>{
  'totalPages': instance.totalPages,
  'nextPage': instance.nextPage,
  'prevPage': instance.prevPage,
  'limit': instance.limit,
  'page': instance.page,
  'total': instance.total,
  'items': instance.items,
};

NotificationListItemsModel _$NotificationListItemsModelFromJson(
  Map<String, dynamic> json,
) => NotificationListItemsModel(
  title: json['title']?.toString(),
  content: json['content']?.toString(),
  isRead: bool.tryParse(json['isRead'].toString()),
  id: json['id']?.toString(),
  createdAt: json['createdAt']?.toString(),
  createdByInfo: json['createdByInfo'] == null || json['createdByInfo'] is! Map
      ? null
      : CreatedByInfoModel.fromJson(
          json['createdByInfo'] as Map<String, dynamic>,
        ),
  detail: json['detail'] == null || json['detail'] is! Map
      ? null
      : NotificationListDetailModel.fromJson(
          json['detail'] as Map<String, dynamic>,
        ),
);

Map<String, dynamic> _$NotificationListItemsModelToJson(
  NotificationListItemsModel instance,
) => <String, dynamic>{
  'title': instance.title,
  'content': instance.content,
  'isRead': instance.isRead,
  'id': instance.id,
  'createdAt': instance.createdAt,
  'createdByInfo': instance.createdByInfo,
  'detail': instance.detail,
};

NotificationListDetailModel _$NotificationListDetailModelFromJson(
  Map<String, dynamic> json,
) => NotificationListDetailModel(
  type: json['type']?.toString(),
  commentId: json['commentId']?.toString(),
  postId: json['postId']?.toString(),
);

Map<String, dynamic> _$NotificationListDetailModelToJson(
  NotificationListDetailModel instance,
) => <String, dynamic>{
  'type': instance.type,
  'commentId': instance.commentId,
  'postId': instance.postId,
};
