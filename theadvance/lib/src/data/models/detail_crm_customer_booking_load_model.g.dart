// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'detail_crm_customer_booking_load_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DetailCrmCustomerBookingLoadModel _$DetailCrmCustomerBookingLoadModelFromJson(
  Map<String, dynamic> json,
) => DetailCrmCustomerBookingLoadModel(
  booking: json['booking'] == null || json['booking'] is! Map
      ? null
      : DetailCrmCustomerBookingLoadBookingModel.fromJson(
          json['booking'] as Map<String, dynamic>,
        ),
  bookingDetails: (json['booking_details'] is List)
      ? (json['booking_details'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : DetailCrmCustomerBookingDetailLoadItemsModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
);

Map<String, dynamic> _$DetailCrmCustomerBookingLoadModelToJson(
  DetailCrmCustomerBookingLoadModel instance,
) => <String, dynamic>{
  'booking': instance.booking,
  'booking_details': instance.bookingDetails,
};

DetailCrmCustomerBookingLoadBookingModel
_$DetailCrmCustomerBookingLoadBookingModelFromJson(Map<String, dynamic> json) =>
    DetailCrmCustomerBookingLoadBookingModel(
      customerCrmId: double.tryParse(
        json['customer_crm_id'].toString(),
      )?.toInt(),
      bookingId: double.tryParse(json['booking_id'].toString())?.toInt(),
      bookingDate: json['booking_date']?.toString(),
      bookingTime: json['booking_time']?.toString(),
      bookingType: json['booking_type']?.toString(),
      departmentCode: json['department_code']?.toString(),
      branchId: double.tryParse(json['branch_id'].toString())?.toInt(),
      note: json['note']?.toString(),
      isSms: bool.tryParse(json['is_sms'].toString()),
      isZns: bool.tryParse(json['is_zns'].toString()),
      isNotiApp: bool.tryParse(json['is_noti_app'].toString()),
      isCall: bool.tryParse(json['is_call'].toString()),
      isCancel: bool.tryParse(json['is_cancel'].toString()),
      resource: json['resource']?.toString(),
      promotionId: double.tryParse(json['promotion_id'].toString())?.toInt(),
    );

Map<String, dynamic> _$DetailCrmCustomerBookingLoadBookingModelToJson(
  DetailCrmCustomerBookingLoadBookingModel instance,
) => <String, dynamic>{
  'customer_crm_id': instance.customerCrmId,
  'booking_id': instance.bookingId,
  'booking_date': instance.bookingDate,
  'booking_time': instance.bookingTime,
  'booking_type': instance.bookingType,
  'department_code': instance.departmentCode,
  'branch_id': instance.branchId,
  'promotion_id': instance.promotionId,
  'note': instance.note,
  'is_sms': instance.isSms,
  'is_zns': instance.isZns,
  'is_noti_app': instance.isNotiApp,
  'is_call': instance.isCall,
  'is_cancel': instance.isCancel,
  'resource': instance.resource,
};
