// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'medical_log_detail_ha_point_list_get_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class MedicalLogDetailHaPointListGetModelAdapter
    extends TypeAdapter<MedicalLogDetailHaPointListGetModel> {
  @override
  final int typeId = 137;

  @override
  MedicalLogDetailHaPointListGetModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return MedicalLogDetailHaPointListGetModel()
      ..items = (fields[0] as List?)?.cast<CatalogModel>();
  }

  @override
  void write(BinaryWriter writer, MedicalLogDetailHaPointListGetModel obj) {
    writer
      ..writeByte(1)
      ..writeByte(0)
      ..write(obj.items);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MedicalLogDetailHaPointListGetModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MedicalLogDetailHaPointListGetModel
_$MedicalLogDetailHaPointListGetModelFromJson(Map<String, dynamic> json) =>
    MedicalLogDetailHaPointListGetModel()
      ..items = (json['items'] is List)
          ? (json['items'] as List<dynamic>?)
                ?.map((e) => CatalogModel.fromJson(e as Map<String, dynamic>))
                .toList()
          : [];

Map<String, dynamic> _$MedicalLogDetailHaPointListGetModelToJson(
  MedicalLogDetailHaPointListGetModel instance,
) => <String, dynamic>{'items': instance.items};
