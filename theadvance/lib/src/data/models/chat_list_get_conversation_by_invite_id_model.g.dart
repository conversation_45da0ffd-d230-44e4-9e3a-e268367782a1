// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'chat_list_get_conversation_by_invite_id_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ChatListGetConversationByInviteIdModel
_$ChatListGetConversationByInviteIdModelFromJson(
  Map<String, dynamic> json,
) => ChatListGetConversationByInviteIdModel(
  id: json['id']?.toString(),
  avatar: json['avatar']?.toString(),
  name: json['name']?.toString(),
  members: (json['members'] is List)
      ? (json['members'] as List<dynamic>).map((e) => e?.toString()).toList()
      : [],
  description: json['description']?.toString(),
  takeCareGroupId: json['takeCareGroupId']?.toString(),
  type: json['type']?.toString(),
  lastMessage: json['lastMessage']?.toString(),
  isGroup: bool.tryParse(json['isGroup'].toString()),
  createdBy: json['createdBy']?.toString(),
  createdAt: json['createdAt']?.toString(),
  updatedAt: json['updatedAt']?.toString(),
  inviteId: json['inviteId']?.toString(),
  membersInfo: (json['membersInfo'] is List)
      ? (json['membersInfo'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : ChatListGetConversationByInviteIdMembersInfoModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
);

Map<String, dynamic> _$ChatListGetConversationByInviteIdModelToJson(
  ChatListGetConversationByInviteIdModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'avatar': instance.avatar,
  'name': instance.name,
  'members': instance.members,
  'description': instance.description,
  'takeCareGroupId': instance.takeCareGroupId,
  'type': instance.type,
  'lastMessage': instance.lastMessage,
  'isGroup': instance.isGroup,
  'createdBy': instance.createdBy,
  'createdAt': instance.createdAt,
  'updatedAt': instance.updatedAt,
  'inviteId': instance.inviteId,
  'membersInfo': instance.membersInfo,
};

ChatListGetConversationByInviteIdMembersInfoModel
_$ChatListGetConversationByInviteIdMembersInfoModelFromJson(
  Map<String, dynamic> json,
) => ChatListGetConversationByInviteIdMembersInfoModel(
  username: json['username']?.toString(),
  avatar: json['avatar']?.toString(),
  id: json['id']?.toString(),
  name: json['name']?.toString(),
  type: json['type']?.toString(),
  departmentName: json['departmentName']?.toString(),
  phone: json['phone']?.toString(),
);

Map<String, dynamic> _$ChatListGetConversationByInviteIdMembersInfoModelToJson(
  ChatListGetConversationByInviteIdMembersInfoModel instance,
) => <String, dynamic>{
  'username': instance.username,
  'avatar': instance.avatar,
  'id': instance.id,
  'name': instance.name,
  'type': instance.type,
  'departmentName': instance.departmentName,
  'phone': instance.phone,
};
