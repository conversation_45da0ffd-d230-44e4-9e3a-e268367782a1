// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'customer_relationship_list_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CustomerRelationShipListModel _$CustomerRelationShipListModelFromJson(
  Map<String, dynamic> json,
) => CustomerRelationShipListModel(
  items: (json['items'] is List)
      ? (json['items'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : CustomerRelationShipListItemModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
);

CustomerRelationShipListItemModel _$CustomerRelationShipListItemModelFromJson(
  Map<String, dynamic> json,
) => CustomerRelationShipListItemModel(
  customerCode: json['CustomerCode']?.toString(),
  customerName: json['CustomerName']?.toString(),
  occupation: json['Occupation']?.toString(),
  relationShip: json['Relationship']?.toString(),
  descriptions: (json['Descriptions'] is List)
      ? (json['Descriptions'] as List<dynamic>?)
            ?.map(
              (e) =>
                  CustomerDescriptionsModel.fromJson(e as Map<String, dynamic>),
            )
            .toList()
      : [],
);

CustomerDescriptionsModel _$CustomerDescriptionsModelFromJson(
  Map<String, dynamic> json,
) => CustomerDescriptionsModel(
  id: json['Id']?.toString(),
  name: json['Name']?.toString(),
);
