// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'taking_care_customer_bot_type_load_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TakingCareCustomerBotTypeLoadModel _$TakingCareCustomerBotTypeLoadModelFromJson(
  Map<String, dynamic> json,
) => TakingCareCustomerBotTypeLoadModel(
  items: (json['items'] is List)
      ? (json['items'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : TakingCareCustomerBotTypeLoadItemsModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
);

Map<String, dynamic> _$TakingCareCustomerBotTypeLoadModelToJson(
  TakingCareCustomerBotTypeLoadModel instance,
) => <String, dynamic>{'items': instance.items};

TakingCareCustomerBotTypeLoadItemsModel
_$TakingCareCustomerBotTypeLoadItemsModelFromJson(Map<String, dynamic> json) =>
    TakingCareCustomerBotTypeLoadItemsModel(
      value: double.tryParse(json['value'].toString())?.toInt(),
      label: json['label']?.toString(),
    );

Map<String, dynamic> _$TakingCareCustomerBotTypeLoadItemsModelToJson(
  TakingCareCustomerBotTypeLoadItemsModel instance,
) => <String, dynamic>{'value': instance.value, 'label': instance.label};
