// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'login_social_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LoginSocialModel _$LoginSocialModelFromJson(Map<String, dynamic> json) =>
    LoginSocialModel(
      accessToken: json['accessToken']?.toString(),
      user: json['user'] == null || json['user'] is! Map
          ? null
          : LoginSocialUserModel.fromJson(json['user'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$LoginSocialModelToJson(LoginSocialModel instance) =>
    <String, dynamic>{
      'accessToken': instance.accessToken,
      'user': instance.user,
    };

LoginSocialUserModel _$LoginSocialUserModelFromJson(
  Map<String, dynamic> json,
) => LoginSocialUserModel(
  id: json['_id']?.toString(),
  username: json['username']?.toString(),
  avatar: json['avatar']?.toString(),
  bio: json['bio']?.toString(),
  coverImage: json['coverImage']?.toString(),
  createdAt: json['createdAt']?.toString(),
  name: json['name']?.toString(),
  updatedAt: json['updatedAt']?.toString(),
  departmentName: json['departmentName']?.toString(),
  phone: json['phone']?.toString(),
);

Map<String, dynamic> _$LoginSocialUserModelToJson(
  LoginSocialUserModel instance,
) => <String, dynamic>{
  '_id': instance.id,
  'username': instance.username,
  'avatar': instance.avatar,
  'bio': instance.bio,
  'coverImage': instance.coverImage,
  'createdAt': instance.createdAt,
  'name': instance.name,
  'updatedAt': instance.updatedAt,
  'departmentName': instance.departmentName,
  'phone': instance.phone,
};
