// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'branch_selection_get_floor_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class BranchSelectionGetFloorModelAdapter
    extends TypeAdapter<BranchSelectionGetFloorModel> {
  @override
  final int typeId = 124;

  @override
  BranchSelectionGetFloorModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return BranchSelectionGetFloorModel()
      ..items = (fields[0] as List?)?.cast<FloorModel?>();
  }

  @override
  void write(BinaryWriter writer, BranchSelectionGetFloorModel obj) {
    writer
      ..writeByte(1)
      ..writeByte(0)
      ..write(obj.items);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BranchSelectionGetFloorModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BranchSelectionGetFloorModel _$BranchSelectionGetFloorModelFromJson(
  Map<String, dynamic> json,
) => BranchSelectionGetFloorModel()
  ..items = (json['items'] is List)
      ? (json['items'] as List<dynamic>?)
            ?.map(
              (e) => e == null || e is! Map
                  ? null
                  : FloorModel.fromJson(e as Map<String, dynamic>),
            )
            .toList()
      : [];

Map<String, dynamic> _$BranchSelectionGetFloorModelToJson(
  BranchSelectionGetFloorModel instance,
) => <String, dynamic>{'items': instance.items};

FloorModel _$FloorModelFromJson(Map<String, dynamic> json) => FloorModel(
  text: json['text']?.toString(),
  id: json['id']?.toString(),
  code: json['code']?.toString(),
);

Map<String, dynamic> _$FloorModelToJson(FloorModel instance) =>
    <String, dynamic>{
      'text': instance.text,
      'code': instance.code,
      'id': instance.id,
    };
