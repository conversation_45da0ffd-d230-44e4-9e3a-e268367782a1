// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'chat_list_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ChatListModel _$ChatListModelFromJson(Map<String, dynamic> json) =>
    ChatListModel(
      total: double.tryParse(json['total'].toString())?.toInt(),
      limit: double.tryParse(json['limit'].toString())?.toInt(),
      items: (json['items'] is List)
          ? (json['items'] as List<dynamic>)
                .map(
                  (e) => e == null || e is! Map
                      ? null
                      : ChatListItemsModel.fromJson(e as Map<String, dynamic>),
                )
                .toList()
          : [],
    );

Map<String, dynamic> _$ChatListModelToJson(ChatListModel instance) =>
    <String, dynamic>{
      'total': instance.total,
      'limit': instance.limit,
      'items': instance.items,
    };

ChatListItemsModel _$ChatListItemsModelFromJson(
  Map<String, dynamic> json,
) => ChatListItemsModel(
  id: json['id']?.toString(),
  inviteLink: json['inviteLink']?.toString(),
  avatar: json['avatar']?.toString(),
  name: json['name']?.toString(),
  description: json['description']?.toString(),
  unreadCount: double.tryParse(json['unreadCount'].toString())?.toInt(),
  membersInfo: (json['membersInfo'] is List)
      ? (json['membersInfo'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : ChatListItemsMembersInfoModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
  members: (json['members'] is List)
      ? (json['members'] as List<dynamic>).map((e) => e?.toString()).toList()
      : [],
  lastMessageInfo:
      json['lastMessageInfo'] == null || json['lastMessageInfo'] is! Map
      ? null
      : ChatItemsModel.fromJson(
          json['lastMessageInfo'] as Map<String, dynamic>,
        ),
  updatedAt: json['updatedAt']?.toString(),
  isGroup: bool.tryParse(json['isGroup'].toString()),
  conversationDetails:
      json['conversationDetails'] == null || json['conversationDetails'] is! Map
      ? null
      : ChatListItemsConversationDetailsModel.fromJson(
          json['conversationDetails'] as Map<String, dynamic>,
        ),
  type: json['type']?.toString(),
  isSavedMessage: bool.tryParse(json['isSavedMessage'].toString()),
  memberRules: (json['memberRules'] is List)
      ? (json['memberRules'] as List<dynamic>?)
            ?.map(
              (e) => e == null || e is! Map
                  ? null
                  : GroupChatDetailGetRuleByRoleRoleRulesModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
  seen: bool.tryParse(json['seen'].toString()),
);

Map<String, dynamic> _$ChatListItemsModelToJson(ChatListItemsModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'inviteLink': instance.inviteLink,
      'avatar': instance.avatar,
      'name': instance.name,
      'description': instance.description,
      'unreadCount': instance.unreadCount,
      'membersInfo': instance.membersInfo,
      'members': instance.members,
      'lastMessageInfo': instance.lastMessageInfo,
      'updatedAt': instance.updatedAt,
      'isGroup': instance.isGroup,
      'conversationDetails': instance.conversationDetails,
      'type': instance.type,
      'isSavedMessage': instance.isSavedMessage,
      'memberRules': instance.memberRules,
      'seen': instance.seen,
    };

ChatListItemsMembersInfoModel _$ChatListItemsMembersInfoModelFromJson(
  Map<String, dynamic> json,
) => ChatListItemsMembersInfoModel(
  username: json['username']?.toString(),
  avatar: json['avatar']?.toString(),
  id: json['id']?.toString(),
  name: json['name']?.toString(),
  departmentName: json['departmentName']?.toString(),
  phone: json['phone']?.toString(),
);

Map<String, dynamic> _$ChatListItemsMembersInfoModelToJson(
  ChatListItemsMembersInfoModel instance,
) => <String, dynamic>{
  'username': instance.username,
  'avatar': instance.avatar,
  'id': instance.id,
  'name': instance.name,
  'departmentName': instance.departmentName,
  'phone': instance.phone,
};

ChatListItemsLastMessageInfoModel _$ChatListItemsLastMessageInfoModelFromJson(
  Map<String, dynamic> json,
) => ChatListItemsLastMessageInfoModel(
  id: json['id']?.toString(),
  conversationId: json['conversationId']?.toString(),
  content: json['content']?.toString(),
  type: json['type']?.toString(),
  replyMarkup: json['replyMarkup'] == null || json['replyMarkup'] is! Map
      ? null
      : ChatListItemsLastMessageInfoReplyMarkupModel.fromJson(
          json['replyMarkup'] as Map<String, dynamic>,
        ),
  createdBy: json['createdBy']?.toString(),
  createdAt: json['createdAt']?.toString(),
  updatedAt: json['updatedAt']?.toString(),
  reactions: json['reactions'] as List<dynamic>,
  createdByInfo: json['createdByInfo'] == null || json['createdByInfo'] is! Map
      ? null
      : ChatListItemsLastMessageInfoCreatedByInfoModel.fromJson(
          json['createdByInfo'] as Map<String, dynamic>,
        ),
);

Map<String, dynamic> _$ChatListItemsLastMessageInfoModelToJson(
  ChatListItemsLastMessageInfoModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'conversationId': instance.conversationId,
  'content': instance.content,
  'type': instance.type,
  'replyMarkup': instance.replyMarkup,
  'createdBy': instance.createdBy,
  'createdAt': instance.createdAt,
  'updatedAt': instance.updatedAt,
  'reactions': instance.reactions,
  'createdByInfo': instance.createdByInfo,
};

ChatListItemsConversationDetailsModel
_$ChatListItemsConversationDetailsModelFromJson(Map<String, dynamic> json) =>
    ChatListItemsConversationDetailsModel(
      username: json['username']?.toString(),
      lastSeenAt: json['lastSeenAt']?.toString(),
      lastSeenMessageId: json['lastSeenMessageId']?.toString(),
      isMute: bool.tryParse(json['isMute'].toString()),
      role: json['role']?.toString(),
      mentionMessages: (json['mentionMessages'] is List)
          ? (json['mentionMessages'] as List<dynamic>?)
                ?.map(
                  (e) => e == null || e is! Map
                      ? null
                      : MentionMessageModel.fromJson(e as Map<String, dynamic>),
                )
                .toList()
          : [],
    );

Map<String, dynamic> _$ChatListItemsConversationDetailsModelToJson(
  ChatListItemsConversationDetailsModel instance,
) => <String, dynamic>{
  'username': instance.username,
  'lastSeenAt': instance.lastSeenAt,
  'lastSeenMessageId': instance.lastSeenMessageId,
  'isMute': instance.isMute,
  'role': instance.role,
  'mentionMessages': instance.mentionMessages,
};

ChatListItemsLastMessageInfoReplyMarkupModel
_$ChatListItemsLastMessageInfoReplyMarkupModelFromJson(
  Map<String, dynamic> json,
) => ChatListItemsLastMessageInfoReplyMarkupModel(
  inlineKeyboard: json['inlineKeyboard'] as List<dynamic>,
);

Map<String, dynamic> _$ChatListItemsLastMessageInfoReplyMarkupModelToJson(
  ChatListItemsLastMessageInfoReplyMarkupModel instance,
) => <String, dynamic>{'inlineKeyboard': instance.inlineKeyboard};

ChatListItemsLastMessageInfoCreatedByInfoModel
_$ChatListItemsLastMessageInfoCreatedByInfoModelFromJson(
  Map<String, dynamic> json,
) => ChatListItemsLastMessageInfoCreatedByInfoModel(
  username: json['username']?.toString(),
  avatar: json['avatar']?.toString(),
  id: json['id']?.toString(),
  name: json['name']?.toString(),
);

Map<String, dynamic> _$ChatListItemsLastMessageInfoCreatedByInfoModelToJson(
  ChatListItemsLastMessageInfoCreatedByInfoModel instance,
) => <String, dynamic>{
  'username': instance.username,
  'avatar': instance.avatar,
  'id': instance.id,
  'name': instance.name,
};

MentionMessageModel _$MentionMessageModelFromJson(Map<String, dynamic> json) =>
    MentionMessageModel(
      id: json['id']?.toString(),
      createdAt: json['createdAt']?.toString(),
    );

Map<String, dynamic> _$MentionMessageModelToJson(
  MentionMessageModel instance,
) => <String, dynamic>{'id': instance.id, 'createdAt': instance.createdAt};
