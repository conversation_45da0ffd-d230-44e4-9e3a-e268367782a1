// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'create_customer_customer_search_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CreateCustomerCustomerSearchModel _$CreateCustomerCustomerSearchModelFromJson(
  Map<String, dynamic> json,
) => CreateCustomerCustomerSearchModel(
  items: (json['items'] is List)
      ? (json['items'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : CreateCustomerCustomerSearchItemsModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
);

Map<String, dynamic> _$CreateCustomerCustomerSearchModelToJson(
  CreateCustomerCustomerSearchModel instance,
) => <String, dynamic>{'items': instance.items};

CreateCustomerCustomerSearchItemsModel
_$CreateCustomerCustomerSearchItemsModelFromJson(Map<String, dynamic> json) =>
    CreateCustomerCustomerSearchItemsModel(
      customerID: double.tryParse(json['CustomerID'].toString())?.toInt(),
      customerName: json['CustomerName']?.toString(),
    );

Map<String, dynamic> _$CreateCustomerCustomerSearchItemsModelToJson(
  CreateCustomerCustomerSearchItemsModel instance,
) => <String, dynamic>{
  'CustomerID': instance.customerID,
  'CustomerName': instance.customerName,
};
