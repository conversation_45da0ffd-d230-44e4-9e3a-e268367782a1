// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'kpi_employee_detail_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

KpiEmployeeDetailModel _$KpiEmployeeDetailModelFromJson(
  Map<String, dynamic> json,
) => KpiEmployeeDetailModel(
  revenueMaster: json['RevenueMaster'] == null || json['RevenueMaster'] is! Map
      ? null
      : RevenueMasterModel.fromJson(
          json['RevenueMaster'] as Map<String, dynamic>,
        ),
  revenueDetails: (json['RevenueDetails'] is List)
      ? (json['RevenueDetails'] as List<dynamic>?)
            ?.map((e) => RevenueDetailModel.fromJson(e as Map<String, dynamic>))
            .toList()
      : [],
);

RevenueDetailModel _$RevenueDetailModelFromJson(Map<String, dynamic> json) =>
    RevenueDetailModel(
      partnerId: json['PartnerID']?.toString(),
      partnerName: json['PartnerName']?.toString(),
      items: (json['Items'] is List)
          ? (json['Items'] as List<dynamic>?)
                ?.map(
                  (e) => RevenueDetailItemsModel.fromJson(
                    e as Map<String, dynamic>,
                  ),
                )
                .toList()
          : [],
    );

RevenueDetailItemsModel _$RevenueDetailItemsModelFromJson(
  Map<String, dynamic> json,
) => RevenueDetailItemsModel(
  itemId: json['ItemID']?.toString(),
  itemName: json['ItemName']?.toString(),
  isSupportEmployee: bool.tryParse(json['IsSupportEmployee'].toString()),
  revenueValueDetail: double.tryParse(json['RevenueValueDetail'].toString()),
  createdTime: json['CreatedTime']?.toString(),
);

RevenueMasterModel _$RevenueMasterModelFromJson(Map<String, dynamic> json) =>
    RevenueMasterModel(
      employeeId: json['EmployeeID']?.toString(),
      createdDate: json['CreatedDate']?.toString(),
      revenueValue: double.tryParse(json['RevenueValue'].toString())?.toInt(),
    );
