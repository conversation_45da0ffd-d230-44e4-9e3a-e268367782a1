// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'create_chat_folder_conversation_load_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CreateChatFolderConversationLoadModel
_$CreateChatFolderConversationLoadModelFromJson(Map<String, dynamic> json) =>
    CreateChatFolderConversationLoadModel(
      folder: json['folder'] == null || json['folder'] is! Map
          ? null
          : CreateChatFolderConversationLoadFolderModel.fromJson(
              json['folder'] as Map<String, dynamic>,
            ),
      conversations:
          json['conversations'] == null || json['conversations'] is! Map
          ? null
          : CreateChatFolderConversationLoadConversationsModel.fromJson(
              json['conversations'] as Map<String, dynamic>,
            ),
    );

Map<String, dynamic> _$CreateChatFolderConversationLoadModelToJson(
  CreateChatFolderConversationLoadModel instance,
) => <String, dynamic>{
  'folder': instance.folder,
  'conversations': instance.conversations,
};

CreateChatFolderConversationLoadFolderModel
_$CreateChatFolderConversationLoadFolderModelFromJson(
  Map<String, dynamic> json,
) => CreateChatFolderConversationLoadFolderModel(
  name: json['name']?.toString(),
  username: json['username']?.toString(),
  icon: json['icon']?.toString(),
  conversations: (json['conversations'] is List)
      ? (json['conversations'] as List<dynamic>)
            .map((e) => e?.toString())
            .toList()
      : [],
  id: json['id']?.toString(),
  createdAt: json['createdAt']?.toString(),
  updatedAt: json['updatedAt']?.toString(),
);

Map<String, dynamic> _$CreateChatFolderConversationLoadFolderModelToJson(
  CreateChatFolderConversationLoadFolderModel instance,
) => <String, dynamic>{
  'name': instance.name,
  'username': instance.username,
  'icon': instance.icon,
  'conversations': instance.conversations,
  'id': instance.id,
  'createdAt': instance.createdAt,
  'updatedAt': instance.updatedAt,
};

CreateChatFolderConversationLoadConversationsModel
_$CreateChatFolderConversationLoadConversationsModelFromJson(
  Map<String, dynamic> json,
) => CreateChatFolderConversationLoadConversationsModel(
  docs: (json['docs'] is List)
      ? (json['docs'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : ChatListItemsModel.fromJson(e as Map<String, dynamic>),
            )
            .toList()
      : [],
  totalDocs: double.tryParse(json['totalDocs'].toString())?.toInt(),
  limit: double.tryParse(json['limit'].toString())?.toInt(),
  totalPages: double.tryParse(json['totalPages'].toString())?.toInt(),
  page: double.tryParse(json['page'].toString())?.toInt(),
  pagingCounter: double.tryParse(json['pagingCounter'].toString())?.toInt(),
  hasPrevPage: bool.tryParse(json['hasPrevPage'].toString()),
  hasNextPage: bool.tryParse(json['hasNextPage'].toString()),
  prevPage: double.tryParse(json['prevPage'].toString())?.toInt(),
  nextPage: double.tryParse(json['nextPage'].toString())?.toInt(),
);

Map<String, dynamic> _$CreateChatFolderConversationLoadConversationsModelToJson(
  CreateChatFolderConversationLoadConversationsModel instance,
) => <String, dynamic>{
  'docs': instance.docs,
  'totalDocs': instance.totalDocs,
  'limit': instance.limit,
  'totalPages': instance.totalPages,
  'page': instance.page,
  'pagingCounter': instance.pagingCounter,
  'hasPrevPage': instance.hasPrevPage,
  'hasNextPage': instance.hasNextPage,
  'prevPage': instance.prevPage,
  'nextPage': instance.nextPage,
};
