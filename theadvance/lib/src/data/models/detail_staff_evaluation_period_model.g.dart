// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'detail_staff_evaluation_period_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DetailStaffEvaluationPeriodModel _$DetailStaffEvaluationPeriodModelFromJson(
  Map<String, dynamic> json,
) => DetailStaffEvaluationPeriodModel(
  totalPoint: double.tryParse(json['TotalPoint'].toString()),
  notes: json['Notes']?.toString(),
  generalDetails: (json['GeneralDetails'] is List)
      ? (json['GeneralDetails'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : DetailStaffEvaluationPeriodGeneralDetailsModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
  charts: (json['Charts'] is List)
      ? (json['Charts'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : DetailStaffEvaluationPeriodChartsModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
  details: (json['Details'] is List)
      ? (json['Details'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : DetailStaffEvaluationPeriodDetailsModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
);

Map<String, dynamic> _$DetailStaffEvaluationPeriodModelToJson(
  DetailStaffEvaluationPeriodModel instance,
) => <String, dynamic>{
  'TotalPoint': instance.totalPoint,
  'Notes': instance.notes,
  'GeneralDetails': instance.generalDetails,
  'Charts': instance.charts,
  'Details': instance.details,
};

DetailStaffEvaluationPeriodGeneralDetailsModel
_$DetailStaffEvaluationPeriodGeneralDetailsModelFromJson(
  Map<String, dynamic> json,
) => DetailStaffEvaluationPeriodGeneralDetailsModel(
  groupSeq: double.tryParse(json['GroupSeq'].toString())?.toInt(),
  groupCriteriaName: json['GroupCriteriaName']?.toString(),
  items: (json['Items'] is List)
      ? (json['Items'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : StaffEvaluationGeneralDetailModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
);

Map<String, dynamic> _$DetailStaffEvaluationPeriodGeneralDetailsModelToJson(
  DetailStaffEvaluationPeriodGeneralDetailsModel instance,
) => <String, dynamic>{
  'GroupSeq': instance.groupSeq,
  'GroupCriteriaName': instance.groupCriteriaName,
  'Items': instance.items,
};

DetailStaffEvaluationPeriodChartsModel
_$DetailStaffEvaluationPeriodChartsModelFromJson(Map<String, dynamic> json) =>
    DetailStaffEvaluationPeriodChartsModel(
      objectTypeName: json['ObjectTypeName']?.toString(),
      items: (json['Items'] is List)
          ? (json['Items'] as List<dynamic>)
                .map(
                  (e) => e == null || e is! Map
                      ? null
                      : DetailStaffEvaluationPeriodChartsItemsModel.fromJson(
                          e as Map<String, dynamic>,
                        ),
                )
                .toList()
          : [],
    );

Map<String, dynamic> _$DetailStaffEvaluationPeriodChartsModelToJson(
  DetailStaffEvaluationPeriodChartsModel instance,
) => <String, dynamic>{
  'ObjectTypeName': instance.objectTypeName,
  'Items': instance.items,
};

DetailStaffEvaluationPeriodDetailsModel
_$DetailStaffEvaluationPeriodDetailsModelFromJson(Map<String, dynamic> json) =>
    DetailStaffEvaluationPeriodDetailsModel(
      groupSeq: double.tryParse(json['GroupSeq'].toString())?.toInt(),
      groupCriteriaName: json['GroupCriteriaName']?.toString(),
      avgGroupPoint: double.tryParse(json['AvgGroupPoint'].toString()),
      items: (json['Items'] is List)
          ? (json['Items'] as List<dynamic>)
                .map(
                  (e) => e == null || e is! Map
                      ? null
                      : StaffEvaluationGeneralDetailItemModel.fromJson(
                          e as Map<String, dynamic>,
                        ),
                )
                .toList()
          : [],
    );

Map<String, dynamic> _$DetailStaffEvaluationPeriodDetailsModelToJson(
  DetailStaffEvaluationPeriodDetailsModel instance,
) => <String, dynamic>{
  'GroupSeq': instance.groupSeq,
  'GroupCriteriaName': instance.groupCriteriaName,
  'AvgGroupPoint': instance.avgGroupPoint,
  'Items': instance.items,
};

StaffEvaluationGeneralDetailModel _$StaffEvaluationGeneralDetailModelFromJson(
  Map<String, dynamic> json,
) => StaffEvaluationGeneralDetailModel(
  criteriaSeq: double.tryParse(json['CriteriaSeq'].toString())?.toInt(),
  criteriaName: json['CriteriaName']?.toString(),
  notes: json['Notes']?.toString(),
);

Map<String, dynamic> _$StaffEvaluationGeneralDetailModelToJson(
  StaffEvaluationGeneralDetailModel instance,
) => <String, dynamic>{
  'CriteriaSeq': instance.criteriaSeq,
  'CriteriaName': instance.criteriaName,
  'Notes': instance.notes,
};

DetailStaffEvaluationPeriodChartsItemsModel
_$DetailStaffEvaluationPeriodChartsItemsModelFromJson(
  Map<String, dynamic> json,
) => DetailStaffEvaluationPeriodChartsItemsModel(
  groupCriteriaName: json['GroupCriteriaName']?.toString(),
  avgGroupPoint: double.tryParse(json['AvgGroupPoint'].toString()),
);

Map<String, dynamic> _$DetailStaffEvaluationPeriodChartsItemsModelToJson(
  DetailStaffEvaluationPeriodChartsItemsModel instance,
) => <String, dynamic>{
  'GroupCriteriaName': instance.groupCriteriaName,
  'AvgGroupPoint': instance.avgGroupPoint,
};

StaffEvaluationGeneralDetailItemModel
_$StaffEvaluationGeneralDetailItemModelFromJson(Map<String, dynamic> json) =>
    StaffEvaluationGeneralDetailItemModel(
      criteriaSeq: double.tryParse(json['CriteriaSeq'].toString())?.toInt(),
      criteriaName: json['CriteriaName']?.toString(),
      avgPoint: double.tryParse(json['AvgPoint'].toString()),
    );

Map<String, dynamic> _$StaffEvaluationGeneralDetailItemModelToJson(
  StaffEvaluationGeneralDetailItemModel instance,
) => <String, dynamic>{
  'CriteriaSeq': instance.criteriaSeq,
  'CriteriaName': instance.criteriaName,
  'AvgPoint': instance.avgPoint,
};
