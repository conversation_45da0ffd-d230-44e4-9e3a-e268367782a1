// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'medical_log_detail_get_post_sai_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MedicalLogDetailGetPostSaiModel _$MedicalLogDetailGetPostSaiModelFromJson(
  Map<String, dynamic> json,
) => MedicalLogDetailGetPostSaiModel()
  ..listPostSai = (json['items'] is List)
      ? (json['items'] as List<dynamic>?)
            ?.map(
              (e) => e == null || e is! Map
                  ? null
                  : CatalogModel.fromJson(e as Map<String, dynamic>),
            )
            .toList()
      : [];

Map<String, dynamic> _$MedicalLogDetailGetPostSaiModelToJson(
  MedicalLogDetailGetPostSaiModel instance,
) => <String, dynamic>{'items': instance.listPostSai};

PostSai _$PostSaiFromJson(Map<String, dynamic> json) => PostSai(
  id: json['id']?.toString(),
  name: json['name']?.toString(),
  types: json['types']?.toString(),
  departmentCode: json['department_code']?.toString(),
);

Map<String, dynamic> _$PostSaiToJson(PostSai instance) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'types': instance.types,
  'department_code': instance.departmentCode,
};
