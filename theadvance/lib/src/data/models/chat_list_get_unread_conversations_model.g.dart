// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'chat_list_get_unread_conversations_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ChatListGetUnreadConversationsModel
_$ChatListGetUnreadConversationsModelFromJson(Map<String, dynamic> json) =>
    ChatListGetUnreadConversationsModel(
      total: double.tryParse(json['total'].toString())?.toInt(),
      limit: double.tryParse(json['limit'].toString())?.toInt(),
      conversations: (json['conversations'] is List)
          ? (json['conversations'] as List<dynamic>)
                .map(
                  (e) => e == null || e is! Map
                      ? null
                      : ChatListItemsModel.fromJson(e as Map<String, dynamic>),
                )
                .toList()
          : [],
    );

Map<String, dynamic> _$ChatListGetUnreadConversationsModelToJson(
  ChatListGetUnreadConversationsModel instance,
) => <String, dynamic>{
  'total': instance.total,
  'limit': instance.limit,
  'conversations': instance.conversations,
};
