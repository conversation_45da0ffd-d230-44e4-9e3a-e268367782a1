// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'medical_service_create_methods_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class MedicalServiceCreationMethodsModelAdapter
    extends TypeAdapter<MedicalServiceCreationMethodsModel> {
  @override
  final int typeId = 145;

  @override
  MedicalServiceCreationMethodsModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return MedicalServiceCreationMethodsModel()
      ..items = (fields[0] as List?)?.cast<MedicalServiceMethodModel?>();
  }

  @override
  void write(BinaryWriter writer, MedicalServiceCreationMethodsModel obj) {
    writer
      ..writeByte(1)
      ..writeByte(0)
      ..write(obj.items);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MedicalServiceCreationMethodsModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MedicalServiceCreationMethodsModel _$MedicalServiceCreationMethodsModelFromJson(
  Map<String, dynamic> json,
) => MedicalServiceCreationMethodsModel()
  ..items = (json['items'] is List)
      ? (json['items'] as List<dynamic>?)
            ?.map(
              (e) => e == null || e is! Map
                  ? null
                  : MedicalServiceMethodModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [];

Map<String, dynamic> _$MedicalServiceCreationMethodsModelToJson(
  MedicalServiceCreationMethodsModel instance,
) => <String, dynamic>{'items': instance.items};

MedicalServiceMethodModel _$MedicalServiceMethodModelFromJson(
  Map<String, dynamic> json,
) => MedicalServiceMethodModel(
  id: json['id']?.toString(),
  text: json['text']?.toString(),
);

Map<String, dynamic> _$MedicalServiceMethodModelToJson(
  MedicalServiceMethodModel instance,
) => <String, dynamic>{'id': instance.id, 'text': instance.text};
