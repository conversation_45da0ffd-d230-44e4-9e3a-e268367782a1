// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'chat_get_user_sticker_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ChatGetUserStickerModel _$ChatGetUserStickerModelFromJson(
  Map<String, dynamic> json,
) => ChatGetUserStickerModel(
  items: (json['items'] is List)
      ? (json['items'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : ChatGetUserStickerItemsModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
);

Map<String, dynamic> _$ChatGetUserStickerModelToJson(
  ChatGetUserStickerModel instance,
) => <String, dynamic>{'items': instance.items};

ChatGetUserStickerItemsModel _$ChatGetUserStickerItemsModelFromJson(
  Map<String, dynamic> json,
) => ChatGetUserStickerItemsModel(
  name: json['name']?.toString(),
  category: json['category']?.toString(),
  reviewThumbnail: json['reviewThumbnail']?.toString(),
  createdBy: json['createdBy']?.toString(),
  id: json['id']?.toString(),
  stickers: (json['stickers'] is List)
      ? (json['stickers'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : ChatGetUserStickerItemsStickersModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
);

Map<String, dynamic> _$ChatGetUserStickerItemsModelToJson(
  ChatGetUserStickerItemsModel instance,
) => <String, dynamic>{
  'name': instance.name,
  'category': instance.category,
  'reviewThumbnail': instance.reviewThumbnail,
  'createdBy': instance.createdBy,
  'id': instance.id,
  'stickers': instance.stickers,
};

ChatGetUserStickerItemsStickersModel
_$ChatGetUserStickerItemsStickersModelFromJson(Map<String, dynamic> json) =>
    ChatGetUserStickerItemsStickersModel(
      id: json['id']?.toString(),
      stickerSetId: json['stickerSetId']?.toString(),
      link: json['link']?.toString(),
      size: double.tryParse(json['size'].toString())?.toInt(),
      mimetype: json['mimetype']?.toString(),
      height: double.tryParse(json['height'].toString())?.toInt(),
      width: double.tryParse(json['width'].toString())?.toInt(),
      icon: json['icon']?.toString(),
      filePath: json['filePath']?.toString(),
      tags: json['tags'] as List<dynamic>,
    );

Map<String, dynamic> _$ChatGetUserStickerItemsStickersModelToJson(
  ChatGetUserStickerItemsStickersModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'stickerSetId': instance.stickerSetId,
  'link': instance.link,
  'size': instance.size,
  'mimetype': instance.mimetype,
  'height': instance.height,
  'width': instance.width,
  'icon': instance.icon,
  'filePath': instance.filePath,
  'tags': instance.tags,
};
