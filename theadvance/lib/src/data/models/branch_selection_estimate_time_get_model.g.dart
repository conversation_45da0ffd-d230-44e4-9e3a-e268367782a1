// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'branch_selection_estimate_time_get_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class BranchSelectionEstimateTimeGetModelAdapter
    extends TypeAdapter<BranchSelectionEstimateTimeGetModel> {
  @override
  final int typeId = 136;

  @override
  BranchSelectionEstimateTimeGetModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return BranchSelectionEstimateTimeGetModel()
      ..items = (fields[0] as List?)?.cast<EstimateTimeModel?>();
  }

  @override
  void write(BinaryWriter writer, BranchSelectionEstimateTimeGetModel obj) {
    writer
      ..writeByte(1)
      ..writeByte(0)
      ..write(obj.items);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BranchSelectionEstimateTimeGetModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BranchSelectionEstimateTimeGetModel
_$BranchSelectionEstimateTimeGetModelFromJson(Map<String, dynamic> json) =>
    BranchSelectionEstimateTimeGetModel()
      ..items = (json['items'] is List)
          ? (json['items'] as List<dynamic>?)
                ?.map(
                  (e) => e == null || e is! Map
                      ? null
                      : EstimateTimeModel.fromJson(e as Map<String, dynamic>),
                )
                .toList()
          : [];

Map<String, dynamic> _$BranchSelectionEstimateTimeGetModelToJson(
  BranchSelectionEstimateTimeGetModel instance,
) => <String, dynamic>{'items': instance.items};

EstimateTimeModel _$EstimateTimeModelFromJson(Map<String, dynamic> json) =>
    EstimateTimeModel(
      id: json['id']?.toString(),
      name: json['name']?.toString(),
    );

Map<String, dynamic> _$EstimateTimeModelToJson(EstimateTimeModel instance) =>
    <String, dynamic>{'id': instance.id, 'name': instance.name};
