// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'like_comment_success_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LikeCommentSuccessModel _$LikeCommentSuccessModelFromJson(
  Map<String, dynamic> json,
) => LikeCommentSuccessModel(
  commentId: json['commentId']?.toString(),
  reactionType: json['reactionType']?.toString(),
  isReaction: bool.tryParse(json['isReaction'].toString()),
  reactCount: double.tryParse(json['reactCount'].toString())?.toInt(),
  reactionBy: json['reactionBy']?.toString(),
);

Map<String, dynamic> _$LikeCommentSuccessModelToJson(
  LikeCommentSuccessModel instance,
) => <String, dynamic>{
  'commentId': instance.commentId,
  'reactionType': instance.reactionType,
  'isReaction': instance.isReaction,
  'reactCount': instance.reactCount,
  'reactionBy': instance.reactionBy,
};
