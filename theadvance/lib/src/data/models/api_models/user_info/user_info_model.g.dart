// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_info_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class UserInfoResponseModelAdapter extends TypeAdapter<UserInfoResponseModel> {
  @override
  final int typeId = 3;

  @override
  UserInfoResponseModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return UserInfoResponseModel(
      status: fields[0] as int?,
      message: fields[1] as String?,
      userInfoModel: fields[2] as UserInfoModel?,
    );
  }

  @override
  void write(BinaryWriter writer, UserInfoResponseModel obj) {
    writer
      ..writeByte(3)
      ..writeByte(0)
      ..write(obj.status)
      ..writeByte(1)
      ..write(obj.message)
      ..writeByte(2)
      ..write(obj.userInfoModel);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserInfoResponseModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class UserInfoModelAdapter extends TypeAdapter<UserInfoModel> {
  @override
  final int typeId = 4;

  @override
  UserInfoModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return UserInfoModel(
      idSource: fields[0] as String?,
      source: fields[1] as String?,
      status: fields[2] as String?,
      createdDate: fields[3] as String?,
      ranking: fields[5] as RankingModel?,
      name: fields[6] as String?,
      phone: fields[7] as String?,
      email: fields[8] as String?,
      point: fields[9] as int?,
      note: fields[4] as String?,
      cycleRanking: fields[10] as CycleRankingModel?,
    );
  }

  @override
  void write(BinaryWriter writer, UserInfoModel obj) {
    writer
      ..writeByte(11)
      ..writeByte(0)
      ..write(obj.idSource)
      ..writeByte(1)
      ..write(obj.source)
      ..writeByte(2)
      ..write(obj.status)
      ..writeByte(3)
      ..write(obj.createdDate)
      ..writeByte(4)
      ..write(obj.note)
      ..writeByte(5)
      ..write(obj.ranking)
      ..writeByte(6)
      ..write(obj.name)
      ..writeByte(7)
      ..write(obj.phone)
      ..writeByte(8)
      ..write(obj.email)
      ..writeByte(9)
      ..write(obj.point)
      ..writeByte(10)
      ..write(obj.cycleRanking);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserInfoModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class CycleRankingModelAdapter extends TypeAdapter<CycleRankingModel> {
  @override
  final int typeId = 12;

  @override
  CycleRankingModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return CycleRankingModel(
      point: fields[0] as int?,
      cycleMessage: fields[1] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, CycleRankingModel obj) {
    writer
      ..writeByte(2)
      ..writeByte(0)
      ..write(obj.point)
      ..writeByte(1)
      ..write(obj.cycleMessage);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CycleRankingModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class RankingModelAdapter extends TypeAdapter<RankingModel> {
  @override
  final int typeId = 5;

  @override
  RankingModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return RankingModel(
      rankMessage: fields[0] as String?,
      level: fields[1] as String?,
      name: fields[2] as String?,
      nextRankingPoint: fields[3] as int?,
      nextRankingName: fields[4] as String?,
      nextRankingLevel: fields[5] as String?,
      stt: fields[6] as int?,
    );
  }

  @override
  void write(BinaryWriter writer, RankingModel obj) {
    writer
      ..writeByte(7)
      ..writeByte(0)
      ..write(obj.rankMessage)
      ..writeByte(1)
      ..write(obj.level)
      ..writeByte(2)
      ..write(obj.name)
      ..writeByte(3)
      ..write(obj.nextRankingPoint)
      ..writeByte(4)
      ..write(obj.nextRankingName)
      ..writeByte(5)
      ..write(obj.nextRankingLevel)
      ..writeByte(6)
      ..write(obj.stt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RankingModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserInfoResponseModel _$UserInfoResponseModelFromJson(
  Map<String, dynamic> json,
) => UserInfoResponseModel(
  status: double.tryParse(json['status'].toString())?.toInt(),
  message: json['message']?.toString(),
  userInfoModel: json['data'] == null || json['data'] is! Map
      ? null
      : UserInfoModel.fromJson(json['data'] as Map<String, dynamic>),
);

Map<String, dynamic> _$UserInfoResponseModelToJson(
  UserInfoResponseModel instance,
) => <String, dynamic>{
  'status': instance.status,
  'message': instance.message,
  'data': toJsonObject(instance.userInfoModel),
};

UserInfoModel _$UserInfoModelFromJson(Map<String, dynamic> json) =>
    UserInfoModel(
      idSource: json['id_source']?.toString(),
      source: json['source']?.toString(),
      status: json['status']?.toString(),
      createdDate: json['created_date']?.toString(),
      ranking: json['ranking'] == null || json['ranking'] is! Map
          ? null
          : RankingModel.fromJson(json['ranking'] as Map<String, dynamic>),
      name: json['name']?.toString(),
      phone: json['phone']?.toString(),
      email: json['email']?.toString(),
      point: double.tryParse(json['point'].toString())?.toInt(),
      note: json['note']?.toString(),
      cycleRanking:
          json['cycle_ranking'] == null || json['cycle_ranking'] is! Map
          ? null
          : CycleRankingModel.fromJson(
              json['cycle_ranking'] as Map<String, dynamic>,
            ),
    );

Map<String, dynamic> _$UserInfoModelToJson(UserInfoModel instance) =>
    <String, dynamic>{
      'id_source': instance.idSource,
      'source': instance.source,
      'status': instance.status,
      'created_date': instance.createdDate,
      'note': instance.note,
      'ranking': toJsonObject(instance.ranking),
      'name': instance.name,
      'phone': instance.phone,
      'email': instance.email,
      'point': instance.point,
      'cycle_ranking': toJsonObject(instance.cycleRanking),
    };

CycleRankingModel _$CycleRankingModelFromJson(Map<String, dynamic> json) =>
    CycleRankingModel(
      point: double.tryParse(json['point'].toString())?.toInt(),
      cycleMessage: json['cycle_message']?.toString(),
    );

Map<String, dynamic> _$CycleRankingModelToJson(CycleRankingModel instance) =>
    <String, dynamic>{
      'point': instance.point,
      'cycle_message': instance.cycleMessage,
    };

RankingModel _$RankingModelFromJson(Map<String, dynamic> json) => RankingModel(
  rankMessage: json['rank_message']?.toString(),
  level: json['level']?.toString(),
  name: json['name']?.toString(),
  nextRankingPoint: double.tryParse(
    json['next_ranking_point'].toString(),
  )?.toInt(),
  nextRankingName: json['next_ranking_name']?.toString(),
  nextRankingLevel: json['next_ranking_level']?.toString(),
  stt: double.tryParse(json['stt'].toString())?.toInt() ?? 0,
);

Map<String, dynamic> _$RankingModelToJson(RankingModel instance) =>
    <String, dynamic>{
      'rank_message': instance.rankMessage,
      'level': instance.level,
      'name': instance.name,
      'next_ranking_point': instance.nextRankingPoint,
      'next_ranking_name': instance.nextRankingName,
      'next_ranking_level': instance.nextRankingLevel,
      'stt': instance.stt,
    };
