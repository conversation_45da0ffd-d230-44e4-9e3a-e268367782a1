// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'reply_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ReplyByIdResponseModel _$ReplyByIdResponseModelFromJson(
  Map<String, dynamic> json,
) => ReplyByIdResponseModel(
  status: double.tryParse(json['status'].toString())?.toInt(),
  errorCode: double.tryParse(json['error_code'].toString())?.toInt(),
  errorMessage: json['error_message']?.toString(),
  replyByIdModel: json['data'] == null || json['data'] is! Map
      ? null
      : ReplyByIdModel.fromJson(json['data'] as Map<String, dynamic>),
);

Map<String, dynamic> _$ReplyByIdResponseModelToJson(
  ReplyByIdResponseModel instance,
) => <String, dynamic>{
  'status': instance.status,
  'error_code': instance.errorCode,
  'error_message': instance.errorMessage,
  'data': instance.replyByIdModel,
};

ReplyByIdModel _$ReplyByIdModelFromJson(Map<String, dynamic> json) =>
    ReplyByIdModel(
      total: double.tryParse(json['total_count'].toString())?.toInt(),
      loadMore: bool.tryParse(json['load_more'].toString()),
      nextId: json['next_id']?.toString(),
      replies: (json['replies'] is List)
          ? (json['replies'] as List<dynamic>?)
                ?.map((e) => ReplyModel.fromJson(e as Map<String, dynamic>))
                .toList()
          : [],
    );

Map<String, dynamic> _$ReplyByIdModelToJson(ReplyByIdModel instance) =>
    <String, dynamic>{
      'total_count': instance.total,
      'load_more': instance.loadMore,
      'next_id': instance.nextId,
      'replies': instance.replies,
    };

ReplyModel _$ReplyModelFromJson(Map<String, dynamic> json) => ReplyModel(
  id: json['id']?.toString(),
  avatar: json['avatar']?.toString(),
  name: json['name']?.toString(),
  content: json['content']?.toString(),
  time: json['time']?.toString(),
);

Map<String, dynamic> _$ReplyModelToJson(ReplyModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'avatar': instance.avatar,
      'name': instance.name,
      'content': instance.content,
      'time': instance.time,
    };

SubmitReplyResponseModel _$SubmitReplyResponseModelFromJson(
  Map<String, dynamic> json,
) => SubmitReplyResponseModel(
  status: double.tryParse(json['status'].toString())?.toInt(),
  errorCode: double.tryParse(json['error_code'].toString())?.toInt(),
  errorMessage: json['error_message']?.toString(),
  replyModel: json['data'] == null || json['data'] is! Map
      ? null
      : ReplyModel.fromJson(json['data'] as Map<String, dynamic>),
);

Map<String, dynamic> _$SubmitReplyResponseModelToJson(
  SubmitReplyResponseModel instance,
) => <String, dynamic>{
  'status': instance.status,
  'error_code': instance.errorCode,
  'error_message': instance.errorMessage,
  'data': instance.replyModel,
};
