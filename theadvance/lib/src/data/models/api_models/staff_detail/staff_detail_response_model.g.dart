// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'staff_detail_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

StaffDetailResponseModel _$StaffDetailResponseModelFromJson(
  Map<String, dynamic> json,
) => StaffDetailResponseModel(
  status: double.tryParse(json['status'].toString())?.toInt(),
  errorCode: double.tryParse(json['error_code'].toString())?.toInt(),
  errorMessage: json['error_message']?.toString(),
  data: json['data'] == null || json['data'] is! Map
      ? null
      : StaffDetailModel.fromJson(json['data'] as Map<String, dynamic>),
);

Map<String, dynamic> _$StaffDetailResponseModelToJson(
  StaffDetailResponseModel instance,
) => <String, dynamic>{
  'status': instance.status,
  'error_code': instance.errorCode,
  'error_message': instance.errorMessage,
  'data': instance.data,
};

StaffDetailModel _$StaffDetailModelFromJson(Map<String, dynamic> json) =>
    StaffDetailModel(
      profile: json['profile'] == null || json['profile'] is! Map
          ? null
          : StaffProfileModel.fromJson(json['profile'] as Map<String, dynamic>),
      group: json['group'] == null || json['group'] is! Map
          ? null
          : GroupModel.fromJson(json['group'] as Map<String, dynamic>),
      job: (json['job'] is List)
          ? (json['job'] as List<dynamic>?)
                ?.map((e) => JobModel.fromJson(e as Map<String, dynamic>))
                .toList()
          : [],
      contract: (json['contract'] is List)
          ? (json['contract'] as List<dynamic>?)
                ?.map((e) => ContractModel.fromJson(e as Map<String, dynamic>))
                .toList()
          : [],
    );

Map<String, dynamic> _$StaffDetailModelToJson(StaffDetailModel instance) =>
    <String, dynamic>{
      'profile': instance.profile,
      'group': instance.group,
      'job': instance.job,
      'contract': instance.contract,
    };

ContractModel _$ContractModelFromJson(Map<String, dynamic> json) =>
    ContractModel(
      from: double.tryParse(json['from'].toString())?.toInt(),
      to: double.tryParse(json['to'].toString())?.toInt(),
      description: json['description']?.toString(),
      section: json['section']?.toString(),
    );

Map<String, dynamic> _$ContractModelToJson(ContractModel instance) =>
    <String, dynamic>{
      'from': instance.from,
      'to': instance.to,
      'description': instance.description,
      'section': instance.section,
    };

GroupModel _$GroupModelFromJson(Map<String, dynamic> json) => GroupModel(
  member: (json['member'] is List)
      ? (json['member'] as List<dynamic>?)
            ?.map((e) => StaffItemModel.fromJson(e as Map<String, dynamic>))
            .toList()
      : [],
  lead: (json['lead'] is List)
      ? (json['lead'] as List<dynamic>?)
            ?.map((e) => StaffItemModel.fromJson(e as Map<String, dynamic>))
            .toList()
      : [],
);

Map<String, dynamic> _$GroupModelToJson(GroupModel instance) =>
    <String, dynamic>{'member': instance.member, 'lead': instance.lead};

LeadModel _$LeadModelFromJson(Map<String, dynamic> json) => LeadModel(
  avatar: json['avatar']?.toString(),
  userName: json['user_name']?.toString(),
  id: json['id']?.toString(),
  position: json['position']?.toString(),
);

Map<String, dynamic> _$LeadModelToJson(LeadModel instance) => <String, dynamic>{
  'avatar': instance.avatar,
  'user_name': instance.userName,
  'id': instance.id,
  'position': instance.position,
};

StaffProfileModel _$StaffProfileModelFromJson(Map<String, dynamic> json) =>
    StaffProfileModel(
      id: json['id']?.toString(),
      gender: json['gender']?.toString(),
      dob: double.tryParse(json['dob'].toString())?.toInt(),
      type: json['type']?.toString(),
      position: json['position']?.toString(),
    );

Map<String, dynamic> _$StaffProfileModelToJson(StaffProfileModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'gender': instance.gender,
      'dob': instance.dob,
      'type': instance.type,
      'position': instance.position,
    };

JobModel _$JobModelFromJson(Map<String, dynamic> json) => JobModel(
  date: double.tryParse(json['date'].toString())?.toInt(),
  position: json['position']?.toString(),
  section: json['section']?.toString(),
  status: json['status']?.toString(),
);

Map<String, dynamic> _$JobModelToJson(JobModel instance) => <String, dynamic>{
  'date': instance.date,
  'position': instance.position,
  'section': instance.section,
  'status': instance.status,
};
