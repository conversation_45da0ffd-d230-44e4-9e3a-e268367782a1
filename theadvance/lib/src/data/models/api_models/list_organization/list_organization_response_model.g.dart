// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'list_organization_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ListOrganizationResponseModel _$ListOrganizationResponseModelFromJson(
  Map<String, dynamic> json,
) => ListOrganizationResponseModel(
  status: double.tryParse(json['status'].toString())?.toInt(),
  message: json['message']?.toString(),
  data: json['data'] == null || json['data'] is! Map
      ? null
      : DataListOrganizationResponseModel.fromJson(
          json['data'] as Map<String, dynamic>,
        ),
)..code = double.tryParse(json['code'].toString())?.toInt();

Map<String, dynamic> _$ListOrganizationResponseModelToJson(
  ListOrganizationResponseModel instance,
) => <String, dynamic>{
  'status': instance.status,
  'code': instance.code,
  'message': instance.message,
  'data': instance.data,
};

DataListOrganizationResponseModel _$DataListOrganizationResponseModelFromJson(
  Map<String, dynamic> json,
) => DataListOrganizationResponseModel(
  success: bool.tryParse(json['Success'].toString()),
  message: json['Message']?.toString(),
  data: json['Data'] == null || json['Data'] is! Map
      ? null
      : ListOrganizationModel.fromJson(json['Data'] as Map<String, dynamic>),
);

Map<String, dynamic> _$DataListOrganizationResponseModelToJson(
  DataListOrganizationResponseModel instance,
) => <String, dynamic>{
  'Success': instance.success,
  'Message': instance.message,
  'Data': instance.data,
};

ListOrganizationModel _$ListOrganizationModelFromJson(
  Map<String, dynamic> json,
) => ListOrganizationModel(
  listOrganization: (json['Items'] is List)
      ? (json['Items'] as List<dynamic>?)
            ?.map((e) => OrganizationModel.fromJson(e as Map<String, dynamic>))
            .toList()
      : [],
  totalRow: double.tryParse(json['TotalRow'].toString())?.toInt(),
);

Map<String, dynamic> _$ListOrganizationModelToJson(
  ListOrganizationModel instance,
) => <String, dynamic>{
  'Items': instance.listOrganization,
  'TotalRow': instance.totalRow,
};

OrganizationModel _$OrganizationModelFromJson(Map<String, dynamic> json) =>
    OrganizationModel(
      id: json['Id']?.toString(),
      name: json['Name']?.toString(),
      nameCompare: json['NameCompare']?.toString(),
      logo: json['OrganizationLogoUrl']?.toString(),
    );

Map<String, dynamic> _$OrganizationModelToJson(OrganizationModel instance) =>
    <String, dynamic>{
      'Id': instance.id,
      'Name': instance.name,
      'NameCompare': instance.nameCompare,
      'OrganizationLogoUrl': instance.logo,
    };
