// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'news_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

NewsResponseModel _$NewsResponseModelFromJson(Map<String, dynamic> json) =>
    NewsResponseModel(
      status: double.tryParse(json['status'].toString())?.toInt(),
      code: double.tryParse(json['code'].toString())?.toInt(),
      message: json['message']?.toString(),
      data: json['data'] == null || json['data'] is! Map
          ? null
          : NewsModel.fromJson(json['data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$NewsResponseModelToJson(NewsResponseModel instance) =>
    <String, dynamic>{
      'status': instance.status,
      'code': instance.code,
      'message': instance.message,
      'data': instance.data,
    };

NewsModel _$NewsModelFromJson(Map<String, dynamic> json) => NewsModel(
  isMore: bool.tryParse(json['isMore'].toString()),
  total: double.tryParse(json['total'].toString())?.toInt(),
  items: (json['items'] is List)
      ? (json['items'] as List<dynamic>?)
            ?.map((e) => NewsItemsModel.fromJson(e as Map<String, dynamic>))
            .toList()
      : [],
);

Map<String, dynamic> _$NewsModelToJson(NewsModel instance) => <String, dynamic>{
  'isMore': instance.isMore,
  'total': instance.total,
  'items': instance.items,
};

NewsItemsModel _$NewsItemsModelFromJson(Map<String, dynamic> json) =>
    NewsItemsModel(
      id: json['id']?.toString(),
      title: json['title']?.toString(),
      image: json['images']?.toString(),
      categoryType: double.tryParse(json['category_type'].toString())?.toInt(),
      isHotNews: bool.tryParse(json['is_hot_news'].toString()),
      href: json['href']?.toString(),
      createdTime: json['created_time']?.toString(),
      createdTimeText: json['created_time_text']?.toString(),
      content: json['content']?.toString(),
      icon: json['icon']?.toString(),
    );

Map<String, dynamic> _$NewsItemsModelToJson(NewsItemsModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'images': instance.image,
      'category_type': instance.categoryType,
      'is_hot_news': instance.isHotNews,
      'href': instance.href,
      'created_time': instance.createdTime,
      'created_time_text': instance.createdTimeText,
      'content': instance.content,
      'icon': instance.icon,
    };

SearchNewsResponseModel _$SearchNewsResponseModelFromJson(
  Map<String, dynamic> json,
) => SearchNewsResponseModel(
  errorCode: double.tryParse(json['code'].toString())?.toInt(),
  errorMessage: json['message']?.toString(),
  data: json['data'] == null || json['data'] is! Map
      ? null
      : SearchNewsModel.fromJson(json['data'] as Map<String, dynamic>),
);

Map<String, dynamic> _$SearchNewsResponseModelToJson(
  SearchNewsResponseModel instance,
) => <String, dynamic>{
  'code': instance.errorCode,
  'message': instance.errorMessage,
  'data': instance.data,
};

SearchNewsModel _$SearchNewsModelFromJson(Map<String, dynamic> json) =>
    SearchNewsModel(
      total: double.tryParse(json['total'].toString())?.toInt(),
      collaboratorNews: (json['items'] is List)
          ? (json['items'] as List<dynamic>?)
                ?.map((e) => NewsItemsModel.fromJson(e as Map<String, dynamic>))
                .toList()
          : [],
    );

Map<String, dynamic> _$SearchNewsModelToJson(SearchNewsModel instance) =>
    <String, dynamic>{
      'total': instance.total,
      'items': instance.collaboratorNews,
    };
