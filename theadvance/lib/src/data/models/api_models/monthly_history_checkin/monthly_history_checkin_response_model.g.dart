// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'monthly_history_checkin_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MonthlyCheckinResponseModel _$MonthlyCheckinResponseModelFromJson(
  Map<String, dynamic> json,
) => MonthlyCheckinResponseModel(
  code: double.tryParse(json['code'].toString())?.toInt(),
  message: json['message']?.toString(),
  data: json['data'] == null || json['data'] is! Map
      ? null
      : MonthlyCheckinModel.fromJson(json['data'] as Map<String, dynamic>),
);

Map<String, dynamic> _$MonthlyCheckinResponseModelToJson(
  MonthlyCheckinResponseModel instance,
) => <String, dynamic>{
  'code': instance.code,
  'message': instance.message,
  'data': instance.data,
};

MonthlyCheckinModel _$MonthlyCheckinModelFromJson(Map<String, dynamic> json) =>
    MonthlyCheckinModel(
      month: json['month']?.toString(),
      items:
          ((json['items'] is List)
              ? (json['items'] as List<dynamic>?)
                    ?.map((e) => Map<String, String>.from(e as Map))
                    .toList()
              : []) ??
          [],
    );

Map<String, dynamic> _$MonthlyCheckinModelToJson(
  MonthlyCheckinModel instance,
) => <String, dynamic>{'month': instance.month, 'items': instance.items};
