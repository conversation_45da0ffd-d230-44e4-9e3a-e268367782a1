// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'staff_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

StaffResponseModel _$StaffResponseModelFromJson(Map<String, dynamic> json) =>
    StaffResponseModel(
      status: double.tryParse(json['status'].toString())?.toInt(),
      code: double.tryParse(json['code'].toString())?.toInt(),
      message: json['message']?.toString(),
      data:
          ((json['data'] is List)
              ? (json['data'] as List<dynamic>?)
                    ?.map(
                      (e) => StaffItemModel.fromJson(e as Map<String, dynamic>),
                    )
                    .toList()
              : []) ??
          [],
    );

Map<String, dynamic> _$StaffResponseModelToJson(StaffResponseModel instance) =>
    <String, dynamic>{
      'status': instance.status,
      'code': instance.code,
      'message': instance.message,
      'data': instance.data,
    };

StaffItemModel _$StaffItemModelFromJson(Map<String, dynamic> json) =>
    StaffItemModel(
      avatar: json['avatar']?.toString(),
      name: json['name']?.toString(),
      phone: json['phone']?.toString(),
      position: json['position']?.toString(),
      employeeId: json['employee_id']?.toString(),
    );

Map<String, dynamic> _$StaffItemModelToJson(StaffItemModel instance) =>
    <String, dynamic>{
      'avatar': instance.avatar,
      'name': instance.name,
      'phone': instance.phone,
      'position': instance.position,
      'employee_id': instance.employeeId,
    };

StaffSearchResponseModel _$StaffSearchResponseModelFromJson(
  Map<String, dynamic> json,
) => StaffSearchResponseModel(
  status: double.tryParse(json['status'].toString())?.toInt(),
  code: double.tryParse(json['code'].toString())?.toInt(),
  message: json['message']?.toString(),
  data: json['data'] == null || json['data'] is! Map
      ? null
      : StaffSearchModel.fromJson(json['data'] as Map<String, dynamic>),
);

Map<String, dynamic> _$StaffSearchResponseModelToJson(
  StaffSearchResponseModel instance,
) => <String, dynamic>{
  'status': instance.status,
  'code': instance.code,
  'message': instance.message,
  'data': instance.data,
};

StaffSearchModel _$StaffSearchModelFromJson(Map<String, dynamic> json) =>
    StaffSearchModel(
      recent: json['recent'] == null || json['recent'] is! Map
          ? null
          : StaffSearchItemModel.fromJson(
              json['recent'] as Map<String, dynamic>,
            ),
      recommendation:
          json['recommendation'] == null || json['recommendation'] is! Map
          ? null
          : StaffSearchItemModel.fromJson(
              json['recommendation'] as Map<String, dynamic>,
            ),
    );

Map<String, dynamic> _$StaffSearchModelToJson(StaffSearchModel instance) =>
    <String, dynamic>{
      'recent': instance.recent,
      'recommendation': instance.recommendation,
    };

StaffSearchItemModel _$StaffSearchItemModelFromJson(
  Map<String, dynamic> json,
) => StaffSearchItemModel(
  title: json['title']?.toString(),
  items:
      ((json['items'] is List)
          ? (json['items'] as List<dynamic>?)
                ?.map((e) => StaffItemModel.fromJson(e as Map<String, dynamic>))
                .toList()
          : []) ??
      [],
);

Map<String, dynamic> _$StaffSearchItemModelToJson(
  StaffSearchItemModel instance,
) => <String, dynamic>{'title': instance.title, 'items': instance.items};
