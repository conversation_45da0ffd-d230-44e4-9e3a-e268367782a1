// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'navigation_info_response_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class NavigationInfoResponseModelAdapter
    extends TypeAdapter<NavigationInfoResponseModel> {
  @override
  final int typeId = 101;

  @override
  NavigationInfoResponseModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return NavigationInfoResponseModel(
      errorCode: fields[0] as int?,
      data: fields[2] as NavigationInfoModel?,
      errorMessage: fields[1] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, NavigationInfoResponseModel obj) {
    writer
      ..writeByte(3)
      ..writeByte(0)
      ..write(obj.errorCode)
      ..writeByte(1)
      ..write(obj.errorMessage)
      ..writeByte(2)
      ..write(obj.data);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is NavigationInfoResponseModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class NavigationInfoModelAdapter extends TypeAdapter<NavigationInfoModel> {
  @override
  final int typeId = 102;

  @override
  NavigationInfoModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return NavigationInfoModel(
      unreadNotify: fields[0] as int?,
      newMessage: fields[1] as int?,
    );
  }

  @override
  void write(BinaryWriter writer, NavigationInfoModel obj) {
    writer
      ..writeByte(2)
      ..writeByte(0)
      ..write(obj.unreadNotify)
      ..writeByte(1)
      ..write(obj.newMessage);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is NavigationInfoModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

NavigationInfoResponseModel _$NavigationInfoResponseModelFromJson(
  Map<String, dynamic> json,
) => NavigationInfoResponseModel(
  errorCode: double.tryParse(json['code'].toString())?.toInt(),
  data: json['data'] == null || json['data'] is! Map
      ? null
      : NavigationInfoModel.fromJson(json['data'] as Map<String, dynamic>),
  errorMessage: json['message']?.toString(),
);

Map<String, dynamic> _$NavigationInfoResponseModelToJson(
  NavigationInfoResponseModel instance,
) => <String, dynamic>{
  'code': instance.errorCode,
  'message': instance.errorMessage,
  'data': instance.data,
};

NavigationInfoModel _$NavigationInfoModelFromJson(Map<String, dynamic> json) =>
    NavigationInfoModel(
      unreadNotify: double.tryParse(json['unread_notify'].toString())?.toInt(),
      newMessage: double.tryParse(json['new_message'].toString())?.toInt(),
    );

Map<String, dynamic> _$NavigationInfoModelToJson(
  NavigationInfoModel instance,
) => <String, dynamic>{
  'unread_notify': instance.unreadNotify,
  'new_message': instance.newMessage,
};
