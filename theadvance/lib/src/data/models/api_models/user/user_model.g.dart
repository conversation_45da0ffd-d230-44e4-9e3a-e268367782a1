// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class UserModelAdapter extends TypeAdapter<UserModel> {
  @override
  final int typeId = 100;

  @override
  UserModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return UserModel(
        employeeId: fields[0] as String?,
        name: fields[1] as String?,
        phone: fields[2] as String?,
        organizationId: fields[3] as String?,
        organizationName: fields[4] as String?,
        avatar: fields[5] as String?,
        gender: fields[6] as String?,
        dob: fields[11] as String?,
        jobTitleId: fields[7] as String?,
        jobTitleName: fields[8] as String?,
        departmentId: fields[9] as String?,
        departmentName: fields[10] as String?,
        address: fields[12] as String?,
        bankAccountId: fields[13] as String?,
        bankAccountName: fields[14] as String?,
        identifyCard: fields[15] as String?,
        searchPathOrgCode: fields[16] as String?,
        pathOrgName: fields[17] as String?,
        product: fields[18] as String?,
        branchName: fields[19] as String?,
        branchId: fields[20] as String?,
        chat: fields[21] as UserChatModel?,
        checkin: fields[22] as UserCheckinModel?,
        bio: fields[24] as String?,
        nickName: fields[25] as String?,
        isKyc: fields[27] as bool?,
        bookingMealAddress: fields[29] as String?,
        reportBug: fields[30] as bool?,
        lsLevel1Id: fields[31] as String?,
      )
      ..loginStringee = fields[23] as LoginStringee?
      ..coverImage = fields[26] as String?;
  }

  @override
  void write(BinaryWriter writer, UserModel obj) {
    writer
      ..writeByte(31)
      ..writeByte(0)
      ..write(obj.employeeId)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.phone)
      ..writeByte(3)
      ..write(obj.organizationId)
      ..writeByte(4)
      ..write(obj.organizationName)
      ..writeByte(5)
      ..write(obj.avatar)
      ..writeByte(6)
      ..write(obj.gender)
      ..writeByte(7)
      ..write(obj.jobTitleId)
      ..writeByte(8)
      ..write(obj.jobTitleName)
      ..writeByte(9)
      ..write(obj.departmentId)
      ..writeByte(10)
      ..write(obj.departmentName)
      ..writeByte(11)
      ..write(obj.dob)
      ..writeByte(12)
      ..write(obj.address)
      ..writeByte(13)
      ..write(obj.bankAccountId)
      ..writeByte(14)
      ..write(obj.bankAccountName)
      ..writeByte(15)
      ..write(obj.identifyCard)
      ..writeByte(16)
      ..write(obj.searchPathOrgCode)
      ..writeByte(17)
      ..write(obj.pathOrgName)
      ..writeByte(18)
      ..write(obj.product)
      ..writeByte(19)
      ..write(obj.branchName)
      ..writeByte(20)
      ..write(obj.branchId)
      ..writeByte(21)
      ..write(obj.chat)
      ..writeByte(22)
      ..write(obj.checkin)
      ..writeByte(23)
      ..write(obj.loginStringee)
      ..writeByte(24)
      ..write(obj.bio)
      ..writeByte(25)
      ..write(obj.nickName)
      ..writeByte(26)
      ..write(obj.coverImage)
      ..writeByte(27)
      ..write(obj.isKyc)
      ..writeByte(29)
      ..write(obj.bookingMealAddress)
      ..writeByte(30)
      ..write(obj.reportBug)
      ..writeByte(31)
      ..write(obj.lsLevel1Id);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class UserChatModelAdapter extends TypeAdapter<UserChatModel> {
  @override
  final int typeId = 148;

  @override
  UserChatModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return UserChatModel(isTakeCareAll: fields[0] as bool?);
  }

  @override
  void write(BinaryWriter writer, UserChatModel obj) {
    writer
      ..writeByte(1)
      ..writeByte(0)
      ..write(obj.isTakeCareAll);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserChatModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class UserCheckinModelAdapter extends TypeAdapter<UserCheckinModel> {
  @override
  final int typeId = 150;

  @override
  UserCheckinModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return UserCheckinModel(isTakePhoto: fields[0] as bool?);
  }

  @override
  void write(BinaryWriter writer, UserCheckinModel obj) {
    writer
      ..writeByte(1)
      ..writeByte(0)
      ..write(obj.isTakePhoto);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserCheckinModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class LoginStringeeAdapter extends TypeAdapter<LoginStringee> {
  @override
  final int typeId = 151;

  @override
  LoginStringee read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return LoginStringee(
      token: fields[0] as String?,
      fromNumber: (fields[1] as List).cast<String>(),
    );
  }

  @override
  void write(BinaryWriter writer, LoginStringee obj) {
    writer
      ..writeByte(2)
      ..writeByte(0)
      ..write(obj.token)
      ..writeByte(1)
      ..write(obj.fromNumber);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LoginStringeeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserModel _$UserModelFromJson(Map<String, dynamic> json) =>
    UserModel(
        employeeId: json['employee_id']?.toString(),
        name: json['name']?.toString(),
        phone: json['phone']?.toString(),
        organizationId: json['organization_id']?.toString(),
        organizationName: json['organization_name']?.toString(),
        avatar: json['avatar']?.toString() ?? '',
        gender: json['gender']?.toString(),
        dob: json['dob']?.toString(),
        jobTitleId: json['job_title_id']?.toString(),
        jobTitleName: json['job_title_name']?.toString(),
        departmentId: json['department_id']?.toString(),
        departmentName: json['department_name']?.toString(),
        address: json['address']?.toString(),
        bankAccountId: json['bank_account_id']?.toString(),
        bankAccountName: json['bank_account_name']?.toString(),
        identifyCard: json['identify_card']?.toString(),
        searchPathOrgCode: json['search_path_org_code']?.toString(),
        pathOrgName: json['path_org_name']?.toString(),
        product: json['product']?.toString(),
        branchName: json['branch_name']?.toString(),
        branchId: json['branch_id']?.toString(),
        chat: json['chat'] == null || json['chat'] is! Map
            ? null
            : UserChatModel.fromJson(json['chat'] as Map<String, dynamic>),
        checkin: json['check_in'] == null || json['check_in'] is! Map
            ? null
            : UserCheckinModel.fromJson(
                json['check_in'] as Map<String, dynamic>,
              ),
        bio: json['bio']?.toString(),
        nickName: json['nickname']?.toString(),
        isKyc: bool.tryParse(json['isKyc'].toString()),
        bookingMealAddress: json['booking_meal_address']?.toString(),
        reportBug: bool.tryParse(json['reportBug'].toString()),
        lsLevel1Id: json['lsLevel1Id']?.toString(),
      )
      ..loginStringee = json['stringee'] == null || json['stringee'] is! Map
          ? null
          : LoginStringee.fromJson(json['stringee'] as Map<String, dynamic>)
      ..coverImage = json['cover_image']?.toString();

Map<String, dynamic> _$UserModelToJson(UserModel instance) => <String, dynamic>{
  'employee_id': instance.employeeId,
  'name': instance.name,
  'phone': instance.phone,
  'organization_id': instance.organizationId,
  'organization_name': instance.organizationName,
  'avatar': instance.avatar,
  'gender': instance.gender,
  'job_title_id': instance.jobTitleId,
  'job_title_name': instance.jobTitleName,
  'department_id': instance.departmentId,
  'department_name': instance.departmentName,
  'dob': instance.dob,
  'address': instance.address,
  'bank_account_id': instance.bankAccountId,
  'bank_account_name': instance.bankAccountName,
  'identify_card': instance.identifyCard,
  'search_path_org_code': instance.searchPathOrgCode,
  'path_org_name': instance.pathOrgName,
  'product': instance.product,
  'branch_name': instance.branchName,
  'branch_id': instance.branchId,
  'chat': instance.chat,
  'check_in': instance.checkin,
  'stringee': instance.loginStringee,
  'bio': instance.bio,
  'nickname': instance.nickName,
  'cover_image': instance.coverImage,
  'isKyc': instance.isKyc,
  'booking_meal_address': instance.bookingMealAddress,
  'reportBug': instance.reportBug,
  'lsLevel1Id': instance.lsLevel1Id,
};

UserChatModel _$UserChatModelFromJson(Map<String, dynamic> json) =>
    UserChatModel(
      isTakeCareAll: bool.tryParse(json['is_take_care_all'].toString()),
    );

Map<String, dynamic> _$UserChatModelToJson(UserChatModel instance) =>
    <String, dynamic>{'is_take_care_all': instance.isTakeCareAll};

UserCheckinModel _$UserCheckinModelFromJson(Map<String, dynamic> json) =>
    UserCheckinModel(
      isTakePhoto: bool.tryParse(json['is_take_photo'].toString()),
    );

Map<String, dynamic> _$UserCheckinModelToJson(UserCheckinModel instance) =>
    <String, dynamic>{'is_take_photo': instance.isTakePhoto};

LoginStringee _$LoginStringeeFromJson(Map<String, dynamic> json) =>
    LoginStringee(
      token: json['token']?.toString(),
      fromNumber: (json['fromNumber'] is List)
          ? (json['fromNumber'] as List<dynamic>)
                .map((e) => e as String)
                .toList()
          : [],
    );

Map<String, dynamic> _$LoginStringeeToJson(LoginStringee instance) =>
    <String, dynamic>{
      'token': instance.token,
      'fromNumber': instance.fromNumber,
    };
