// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'support_request_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SupportRequestResponseModel _$SupportRequestResponseModelFromJson(
  Map<String, dynamic> json,
) => SupportRequestResponseModel(
  errorCode: double.tryParse(json['code'].toString())?.toInt(),
  errorMessage: json['message']?.toString(),
  data: json['data'] == null || json['data'] is! Map
      ? null
      : SupportRequestModel.fromJson(json['data'] as Map<String, dynamic>),
);

Map<String, dynamic> _$SupportRequestResponseModelToJson(
  SupportRequestResponseModel instance,
) => <String, dynamic>{
  'code': instance.errorCode,
  'message': instance.errorMessage,
  'data': instance.data,
};

SupportRequestModel _$SupportRequestModelFromJson(Map<String, dynamic> json) =>
    SupportRequestModel(
      items: (json['items'] is List)
          ? (json['items'] as List<dynamic>?)
                ?.map(
                  (e) => e == null || e is! Map
                      ? null
                      : SupportRequestItemModel.fromJson(
                          e as Map<String, dynamic>,
                        ),
                )
                .toList()
          : [],
      total: double.tryParse(json['total'].toString())?.toInt(),
    );

Map<String, dynamic> _$SupportRequestModelToJson(
  SupportRequestModel instance,
) => <String, dynamic>{'items': instance.items, 'total': instance.total};

SupportRequestItemModel _$SupportRequestItemModelFromJson(
  Map<String, dynamic> json,
) => SupportRequestItemModel(
  type: double.tryParse(json['type'].toString())?.toInt(),
  icon: json['icon']?.toString(),
  typeText: json['type_text']?.toString() ?? '',
  status: json['status']?.toString(),
  notes: json['notes']?.toString() ?? '',
  createdTime: double.tryParse(json['created_time'].toString())?.toInt(),
);

Map<String, dynamic> _$SupportRequestItemModelToJson(
  SupportRequestItemModel instance,
) => <String, dynamic>{
  'type': instance.type,
  'type_text': instance.typeText,
  'icon': instance.icon,
  'status': instance.status,
  'notes': instance.notes,
  'created_time': instance.createdTime,
};
