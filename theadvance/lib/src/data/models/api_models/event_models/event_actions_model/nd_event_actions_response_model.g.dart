// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'nd_event_actions_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

NDEventActionsResponseModel _$NDEventActionsResponseModelFromJson(
  Map<String, dynamic> json,
) => NDEventActionsResponseModel(
  status: double.tryParse(json['status'].toString())?.toInt(),
  code: double.tryParse(json['code'].toString())?.toInt(),
  message: json['message']?.toString(),
  data:
      ((json['data'] is List)
          ? (json['data'] as List<dynamic>?)
                ?.map(
                  (e) => e == null || e is! Map
                      ? null
                      : NDEventActionModel.fromJson(e as Map<String, dynamic>),
                )
                .toList()
          : []) ??
      [],
);

Map<String, dynamic> _$NDEventActionsResponseModelToJson(
  NDEventActionsResponseModel instance,
) => <String, dynamic>{
  'status': instance.status,
  'code': instance.code,
  'message': instance.message,
  'data': instance.data,
};

NDEventActionModel _$NDEventActionModelFromJson(Map<String, dynamic> json) =>
    NDEventActionModel(
      title: json['title']?.toString(),
      type: double.tryParse(json['type'].toString())?.toInt(),
    );

Map<String, dynamic> _$NDEventActionModelToJson(NDEventActionModel instance) =>
    <String, dynamic>{'title': instance.title, 'type': instance.type};
