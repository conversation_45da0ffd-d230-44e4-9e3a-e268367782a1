// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'nd_events_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

NDEventResponseModel _$NDEventResponseModelFromJson(
  Map<String, dynamic> json,
) => NDEventResponseModel(
  status: double.tryParse(json['status'].toString())?.toInt(),
  code: double.tryParse(json['code'].toString())?.toInt(),
  message: json['message']?.toString(),
  data:
      ((json['data'] is List)
          ? (json['data'] as List<dynamic>?)
                ?.map((e) => NDEventModel.fromJson(e as Map<String, dynamic>))
                .toList()
          : []) ??
      [],
);

Map<String, dynamic> _$NDEventResponseModelToJson(
  NDEventResponseModel instance,
) => <String, dynamic>{
  'status': instance.status,
  'code': instance.code,
  'message': instance.message,
  'data': instance.data,
};

NDEventModel _$NDEventModelFromJson(Map<String, dynamic> json) => NDEventModel(
  id: double.tryParse(json['id'].toString())?.toInt(),
  name: json['name']?.toString(),
  code: json['code']?.toString(),
  start: double.tryParse(json['start'].toString())?.toInt(),
  end: double.tryParse(json['end'].toString())?.toInt(),
  status: json['status']?.toString(),
  createdBy: json['created_by']?.toString(),
  ticketPrefix: json['ticket_prefix']?.toString(),
);

Map<String, dynamic> _$NDEventModelToJson(NDEventModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'code': instance.code,
      'start': instance.start,
      'end': instance.end,
      'status': instance.status,
      'created_by': instance.createdBy,
      'ticket_prefix': instance.ticketPrefix,
    };
