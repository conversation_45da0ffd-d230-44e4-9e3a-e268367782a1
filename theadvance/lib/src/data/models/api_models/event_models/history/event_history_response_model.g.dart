// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'event_history_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

EventHistoryResponseModel _$EventHistoryResponseModelFromJson(
  Map<String, dynamic> json,
) => EventHistoryResponseModel(
  status: double.tryParse(json['status'].toString())?.toInt(),
  code: double.tryParse(json['code'].toString())?.toInt(),
  message: json['message']?.toString(),
  data: json['data'] == null || json['data'] is! Map
      ? null
      : EventHistoryModel.fromJson(json['data'] as Map<String, dynamic>),
);

Map<String, dynamic> _$EventHistoryResponseModelToJson(
  EventHistoryResponseModel instance,
) => <String, dynamic>{
  'status': instance.status,
  'code': instance.code,
  'message': instance.message,
  'data': instance.data,
};

EventHistoryModel _$EventHistoryModelFromJson(
  Map<String, dynamic> json,
) => EventHistoryModel(
  logs:
      ((json['logs'] is List)
          ? (json['logs'] as List<dynamic>?)
                ?.map(
                  (e) =>
                      EventHistoryLogsModel.fromJson(e as Map<String, dynamic>),
                )
                .toList()
          : []) ??
      [],
  total: double.tryParse(json['total'].toString())?.toInt(),
  cusTypes: (json['cus_types'] is List)
      ? (json['cus_types'] as List<dynamic>?)?.map((e) => e as String).toList()
      : [],
);

Map<String, dynamic> _$EventHistoryModelToJson(EventHistoryModel instance) =>
    <String, dynamic>{
      'logs': instance.logs,
      'total': instance.total,
      'cus_types': instance.cusTypes,
    };

EventHistoryLogsModel _$EventHistoryLogsModelFromJson(
  Map<String, dynamic> json,
) => EventHistoryLogsModel(
  phone: json['cus_phone']?.toString(),
  rank: json['rank']?.toString(),
  ticketCode: json['ticket_code']?.toString(),
  actionType: double.tryParse(json['action_type'].toString())?.toInt(),
  sId: json['_id']?.toString(),
  eventId: double.tryParse(json['event_id'].toString())?.toInt(),
  createdBy: json['created_by']?.toString(),
  status: json['status']?.toString(),
  createdTime: double.tryParse(json['created_time'].toString())?.toInt(),
  updatedTime: double.tryParse(json['updated_time'].toString())?.toInt(),
  fullname: json['cus_fullname']?.toString(),
  voucherCode: json['voucher_code']?.toString(),
  iV: double.tryParse(json['__v'].toString())?.toInt(),
);

Map<String, dynamic> _$EventHistoryLogsModelToJson(
  EventHistoryLogsModel instance,
) => <String, dynamic>{
  'cus_phone': instance.phone,
  'rank': instance.rank,
  'ticket_code': instance.ticketCode,
  'action_type': instance.actionType,
  '_id': instance.sId,
  'event_id': instance.eventId,
  'created_by': instance.createdBy,
  'status': instance.status,
  'created_time': instance.createdTime,
  'updated_time': instance.updatedTime,
  'cus_fullname': instance.fullname,
  'voucher_code': instance.voucherCode,
  '__v': instance.iV,
};
