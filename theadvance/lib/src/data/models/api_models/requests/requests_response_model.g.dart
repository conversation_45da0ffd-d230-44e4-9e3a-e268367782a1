// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'requests_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RequestsResponseModel _$RequestsResponseModelFromJson(
  Map<String, dynamic> json,
) => RequestsResponseModel(
  status: double.tryParse(json['status'].toString())?.toInt(),
  errorCode: double.tryParse(json['error_code'].toString())?.toInt(),
  errorMessage: json['error_message']?.toString(),
  data:
      ((json['data'] is List)
          ? (json['data'] as List<dynamic>?)
                ?.map((e) => RequestModel.fromJson(e as Map<String, dynamic>))
                .toList()
          : []) ??
      [],
);

Map<String, dynamic> _$RequestsResponseModelToJson(
  RequestsResponseModel instance,
) => <String, dynamic>{
  'status': instance.status,
  'error_code': instance.errorCode,
  'error_message': instance.errorMessage,
  'data': instance.data,
};

RequestModel _$RequestModelFromJson(Map<String, dynamic> json) => RequestModel(
  name: json['name']?.toString(),
  type: json['type']?.toString(),
  icon: json['icon'] == null || json['icon'] is! Map
      ? null
      : RequestIconModel.fromJson(json['icon'] as Map<String, dynamic>),
);

Map<String, dynamic> _$RequestModelToJson(RequestModel instance) =>
    <String, dynamic>{
      'name': instance.name,
      'type': instance.type,
      'icon': instance.icon,
    };

RequestIconModel _$RequestIconModelFromJson(Map<String, dynamic> json) =>
    RequestIconModel(
      url: json['url']?.toString(),
      width: double.tryParse(json['width'].toString()),
      height: double.tryParse(json['height'].toString()),
    );

Map<String, dynamic> _$RequestIconModelToJson(RequestIconModel instance) =>
    <String, dynamic>{
      'url': instance.url,
      'width': instance.width,
      'height': instance.height,
    };

DaysOffResponseModel _$DaysOffResponseModelFromJson(
  Map<String, dynamic> json,
) => DaysOffResponseModel(
  status: double.tryParse(json['status'].toString())?.toInt(),
  errorCode: double.tryParse(json['error_code'].toString())?.toInt(),
  errorMessage: json['error_message']?.toString(),
  data: double.tryParse(json['data'].toString()),
);

Map<String, dynamic> _$DaysOffResponseModelToJson(
  DaysOffResponseModel instance,
) => <String, dynamic>{
  'status': instance.status,
  'error_code': instance.errorCode,
  'error_message': instance.errorMessage,
  'data': instance.data,
};
