// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'result_checkin_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ResultCheckinResponseModel _$ResultCheckinResponseModelFromJson(
  Map<String, dynamic> json,
) => ResultCheckinResponseModel(
  status: double.tryParse(json['status'].toString())?.toInt(),
  errorCode: double.tryParse(json['error_code'].toString())?.toInt(),
  errorMessage: json['error_message']?.toString(),
  resultCheckin: json['data'] == null || json['data'] is! Map
      ? null
      : ResultCheckinModel.fromJson(json['data'] as Map<String, dynamic>),
);

Map<String, dynamic> _$ResultCheckinResponseModelToJson(
  ResultCheckinResponseModel instance,
) => <String, dynamic>{
  'status': instance.status,
  'error_code': instance.errorCode,
  'error_message': instance.errorMessage,
  'data': instance.resultCheckin,
};

ResultCheckinModel _$ResultCheckinModelFromJson(Map<String, dynamic> json) =>
    ResultCheckinModel(
      id: json['id']?.toString(),
      checkinId: json['checkin_id']?.toString(),
      time: json['checkin_time']?.toString(),
      status: json['checkin_status']?.toString(),
      name: json['name']?.toString(),
      customerCode: json['customer_code']?.toString(),
    );

Map<String, dynamic> _$ResultCheckinModelToJson(ResultCheckinModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'checkin_id': instance.checkinId,
      'checkin_time': instance.time,
      'checkin_status': instance.status,
      'name': instance.name,
      'customer_code': instance.customerCode,
    };
