// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'member_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MemberResponseModel _$MemberResponseModelFromJson(Map<String, dynamic> json) =>
    MemberResponseModel(
      errorCode: double.tryParse(json['code'].toString())?.toInt(),
      errorMessage: json['message']?.toString(),
      data: json['data'] == null || json['data'] is! Map
          ? null
          : MemberModel.fromJson(json['data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$MemberResponseModelToJson(
  MemberResponseModel instance,
) => <String, dynamic>{
  'code': instance.errorCode,
  'message': instance.errorMessage,
  'data': instance.data,
};

MemberModel _$MemberModelFromJson(Map<String, dynamic> json) => MemberModel(
  total: double.tryParse(json['total'].toString())?.toInt(),
  items: (json['items'] is List)
      ? (json['items'] as List<dynamic>?)
            ?.map(
              (e) => JobSchedulerStaffModel.fromJson(e as Map<String, dynamic>),
            )
            .toList()
      : [],
);

Map<String, dynamic> _$MemberModelToJson(MemberModel instance) =>
    <String, dynamic>{'total': instance.total, 'items': instance.items};
