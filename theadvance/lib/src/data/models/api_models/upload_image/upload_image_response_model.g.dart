// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'upload_image_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UploadImageResponseModel _$UploadImageResponseModelFromJson(
  Map<String, dynamic> json,
) => UploadImageResponseModel(
  status: double.tryParse(json['status'].toString())?.toInt(),
  message: json['message']?.toString(),
  errorCode: double.tryParse(json['error_code'].toString())?.toInt(),
  listUploadImageModel: (json['data'] is List)
      ? (json['data'] as List<dynamic>?)
            ?.map((e) => UploadImageModel.fromJson(e as Map<String, dynamic>))
            .toList()
      : [],
);

Map<String, dynamic> _$UploadImageResponseModelToJson(
  UploadImageResponseModel instance,
) => <String, dynamic>{
  'error_code': instance.errorCode,
  'status': instance.status,
  'message': instance.message,
  'data': instance.listUploadImageModel,
};

UploadImageModel _$UploadImageModelFromJson(Map<String, dynamic> json) =>
    UploadImageModel(id: json['id']?.toString(), url: json['url']?.toString());

Map<String, dynamic> _$UploadImageModelToJson(UploadImageModel instance) =>
    <String, dynamic>{'id': instance.id, 'url': instance.url};
