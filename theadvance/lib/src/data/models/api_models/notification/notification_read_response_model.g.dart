// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'notification_read_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

NotificationReadResponseModel _$NotificationReadResponseModelFromJson(
  Map<String, dynamic> json,
) => NotificationReadResponseModel(
  errorCode: double.tryParse(json['code'].toString())?.toInt(),
  errorMessage: json['message']?.toString(),
  data: json['data'] == null || json['data'] is! Map
      ? null
      : NotificationReadModel.fromJson(json['data'] as Map<String, dynamic>),
);

Map<String, dynamic> _$NotificationReadResponseModelToJson(
  NotificationReadResponseModel instance,
) => <String, dynamic>{
  'code': instance.errorCode,
  'message': instance.errorMessage,
  'data': instance.data,
};

NotificationReadModel _$NotificationReadModelFromJson(
  Map<String, dynamic> json,
) => NotificationReadModel(
  id: json['id']?.toString(),
  isRead: bool.tryParse(json['is_read'].toString()),
)..urlIcon = json['icon']?.toString();

Map<String, dynamic> _$NotificationReadModelToJson(
  NotificationReadModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'is_read': instance.isRead,
  'icon': instance.urlIcon,
};
