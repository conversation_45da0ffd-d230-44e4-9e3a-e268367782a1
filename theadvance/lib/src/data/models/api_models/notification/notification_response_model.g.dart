// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'notification_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

NotificationResponseModel _$NotificationResponseModelFromJson(
  Map<String, dynamic> json,
) => NotificationResponseModel(
  errorCode: double.tryParse(json['code'].toString())?.toInt(),
  errorMessage: json['message']?.toString(),
  data: json['data'] == null || json['data'] is! Map
      ? null
      : NotificationModel.fromJson(json['data'] as Map<String, dynamic>),
);

Map<String, dynamic> _$NotificationResponseModelToJson(
  NotificationResponseModel instance,
) => <String, dynamic>{
  'code': instance.errorCode,
  'message': instance.errorMessage,
  'data': instance.data,
};

NotificationModel _$NotificationModelFromJson(Map<String, dynamic> json) =>
    NotificationModel(
      pageIndex: double.tryParse(json['total'].toString())?.toInt(),
      loadMore: bool.tryParse(json['is_more'].toString()),
      notifications: (json['items'] is List)
          ? (json['items'] as List<dynamic>?)
                ?.map(
                  (e) => NotificationItemsModel.fromJson(
                    e as Map<String, dynamic>,
                  ),
                )
                .toList()
          : [],
    );

Map<String, dynamic> _$NotificationModelToJson(NotificationModel instance) =>
    <String, dynamic>{
      'total': instance.pageIndex,
      'is_more': instance.loadMore,
      'items': instance.notifications,
    };

NotificationItemsModel _$NotificationItemsModelFromJson(
  Map<String, dynamic> json,
) =>
    NotificationItemsModel(
        id: json['id']?.toString(),
        title: json['title']?.toString(),
        content: json['content']?.toString(),
        imageUrl: json['image_url']?.toString(),
        type: json['type']?.toString(),
        isRead: bool.tryParse(json['is_read'].toString()),
        icon: json['icon']?.toString(),
        createdBy: json['created_by']?.toString(),
        createdTime: json['created_time']?.toString(),
        timeText: json['time_text']?.toString(),
      )
      ..value = json['value']?.toString()
      ..data = json['data']?.toString();

Map<String, dynamic> _$NotificationItemsModelToJson(
  NotificationItemsModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'title': instance.title,
  'content': instance.content,
  'image_url': instance.imageUrl,
  'icon': instance.icon,
  'type': instance.type,
  'value': instance.value,
  'created_by': instance.createdBy,
  'created_time': instance.createdTime,
  'time_text': instance.timeText,
  'is_read': instance.isRead,
  'data': instance.data,
};

SearchNotificationResponseModel _$SearchNotificationResponseModelFromJson(
  Map<String, dynamic> json,
) => SearchNotificationResponseModel(
  errorCode: double.tryParse(json['code'].toString())?.toInt(),
  errorMessage: json['message']?.toString(),
  data: json['data'] == null || json['data'] is! Map
      ? null
      : SearchNotificationModel.fromJson(json['data'] as Map<String, dynamic>),
);

Map<String, dynamic> _$SearchNotificationResponseModelToJson(
  SearchNotificationResponseModel instance,
) => <String, dynamic>{
  'code': instance.errorCode,
  'message': instance.errorMessage,
  'data': instance.data,
};

SearchNotificationModel _$SearchNotificationModelFromJson(
  Map<String, dynamic> json,
) => SearchNotificationModel(
  total: double.tryParse(json['total'].toString())?.toInt(),
  notifications: (json['items'] is List)
      ? (json['items'] as List<dynamic>?)
            ?.map(
              (e) => NotificationItemsModel.fromJson(e as Map<String, dynamic>),
            )
            .toList()
      : [],
);

Map<String, dynamic> _$SearchNotificationModelToJson(
  SearchNotificationModel instance,
) => <String, dynamic>{
  'total': instance.total,
  'items': instance.notifications,
};
