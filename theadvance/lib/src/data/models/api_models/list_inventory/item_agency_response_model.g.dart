// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'item_agency_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AgencyResponeModel _$AgencyResponeModelFromJson(Map<String, dynamic> json) =>
    AgencyResponeModel(
      errorCode: double.tryParse(json['code'].toString())?.toInt(),
      errorMessage: json['message']?.toString(),
      data: (json['data'] is List)
          ? (json['data'] as List<dynamic>?)?.map((e) => e as String).toList()
          : [],
    )..status = double.tryParse(json['status'].toString())?.toInt();

Map<String, dynamic> _$AgencyResponeModelToJson(AgencyResponeModel instance) =>
    <String, dynamic>{
      'status': instance.status,
      'code': instance.errorCode,
      'message': instance.errorMessage,
      'data': instance.data,
    };

ItemAgencyModel _$ItemAgencyModelFromJson(Map<String, dynamic> json) =>
    ItemAgencyModel(title: json['title']?.toString());

Map<String, dynamic> _$ItemAgencyModelToJson(ItemAgencyModel instance) =>
    <String, dynamic>{'title': instance.title};
