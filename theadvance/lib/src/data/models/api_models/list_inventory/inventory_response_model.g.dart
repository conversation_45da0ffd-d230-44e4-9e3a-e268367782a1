// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'inventory_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

InventoryResponseModel _$InventoryResponseModelFromJson(Map json) =>
    InventoryResponseModel(
      message: json['message']?.toString(),
      statusCode: double.tryParse(json['status'].toString())?.toInt(),
      data: json['data'] == null || json['data'] is! Map
          ? null
          : InventoryResponseDataModel.fromJson(
              Map<String, dynamic>.from(json['data'] as Map),
            ),
    )..errorCode = double.tryParse(json['code'].toString())?.toInt() ?? 0;

Map<String, dynamic> _$InventoryResponseModelToJson(
  InventoryResponseModel instance,
) => <String, dynamic>{
  'status': instance.statusCode,
  'code': instance.errorCode,
  'message': instance.message,
  'data': instance.data,
};

InventoryResponseDataModel _$InventoryResponseDataModelFromJson(
  Map<String, dynamic> json,
) => InventoryResponseDataModel(
  success: bool.tryParse(json['success'].toString()),
  message: json['message']?.toString(),
  statusCode: double.tryParse(json['statuscode'].toString())?.toInt(),
  totalRow: double.tryParse(json['TotalRow'].toString())?.toInt(),
  data:
      ((json['Results'] is List)
          ? (json['Results'] as List<dynamic>?)
                ?.map((e) => InventoryModel.fromJson(e as Map<String, dynamic>))
                .toList()
          : []) ??
      [],
)..errorCode = double.tryParse(json['error_code'].toString())?.toInt() ?? 0;

Map<String, dynamic> _$InventoryResponseDataModelToJson(
  InventoryResponseDataModel instance,
) => <String, dynamic>{
  'success': instance.success,
  'statuscode': instance.statusCode,
  'error_code': instance.errorCode,
  'message': instance.message,
  'TotalRow': instance.totalRow,
  'Results': instance.data,
};

InventoryModel _$InventoryModelFromJson(Map json) => InventoryModel(
  transactionNo: json['TransactionNo']?.toString(),
  departmentInventoryID: json['DepartmentID']?.toString(),
  departmentInventoryName: json['DepartmentName']?.toString(),
  employeeID: json['EmployeeID']?.toString(),
  employeeName: json['EmployeeName']?.toString(),
  employeeAccountantID: json['AcountantEmployeeID']?.toString(),
  employeeAccountantName: json['AcountantEmployeeName']?.toString(),
  employeeOtherID: json['OtherEmployeeID']?.toString(),
  inventoryType: json['InventoryType']?.toString(),
  employeeOtherName: json['OtherEmployeeName']?.toString(),
  createdDate: json['CreatedDate']?.toString(),
  inventoryTypeName: json['InventoryTypeName']?.toString(),
  rowKey: double.tryParse(json['RowKey'].toString())?.toInt(),
);

Map<String, dynamic> _$InventoryModelToJson(InventoryModel instance) =>
    <String, dynamic>{
      'TransactionNo': instance.transactionNo,
      'DepartmentID': instance.departmentInventoryID,
      'DepartmentName': instance.departmentInventoryName,
      'EmployeeID': instance.employeeID,
      'EmployeeName': instance.employeeName,
      'AcountantEmployeeID': instance.employeeAccountantID,
      'AcountantEmployeeName': instance.employeeAccountantName,
      'OtherEmployeeID': instance.employeeOtherID,
      'InventoryType': instance.inventoryType,
      'OtherEmployeeName': instance.employeeOtherName,
      'CreatedDate': instance.createdDate,
      'InventoryTypeName': instance.inventoryTypeName,
      'RowKey': instance.rowKey,
    };
