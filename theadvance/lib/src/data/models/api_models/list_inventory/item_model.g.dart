// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'item_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ItemModel _$ItemModelFromJson(Map<String, dynamic> json) => ItemModel(
  id: json['Id']?.toString(),
  name: json['Name']?.toString(),
  isParent: bool.tryParse(json['IsParent'].toString()),
  parentId: json['ParentId']?.toString(),
  iconClass: json['IconClass']?.toString(),
  accountIDAssetDefault: json['AccountIDAssetDefault']?.toString(),
  expenseIDAssetDefault: json['ExpenseIDAssetDefault']?.toString(),
  status: bool.tryParse(json['Status'].toString()),
  levelId: double.tryParse(json['LevelId'].toString())?.toInt(),
  levelName: json['LevelName']?.toString(),
  nameCompare: json['NameCompare']?.toString(),
);

Map<String, dynamic> _$ItemModelToJson(ItemModel instance) => <String, dynamic>{
  'Id': instance.id,
  'Name': instance.name,
  'IsParent': instance.isParent,
  'ParentId': instance.parentId,
  'IconClass': instance.iconClass,
  'AccountIDAssetDefault': instance.accountIDAssetDefault,
  'ExpenseIDAssetDefault': instance.expenseIDAssetDefault,
  'Status': instance.status,
  'LevelId': instance.levelId,
  'LevelName': instance.levelName,
  'NameCompare': instance.nameCompare,
};
