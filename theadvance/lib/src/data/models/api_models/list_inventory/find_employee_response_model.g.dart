// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'find_employee_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FindEmployeeResponseModel _$FindEmployeeResponseModelFromJson(Map json) =>
    FindEmployeeResponseModel(
      data: json['data'] == null || json['data'] is! Map
          ? null
          : FindEmployeeModel.fromJson(
              Map<String, dynamic>.from(json['data'] as Map),
            ),
      statusCode: double.tryParse(json['status'].toString())?.toInt(),
      message: json['message']?.toString(),
    )..errorCode = double.tryParse(json['code'].toString())?.toInt() ?? 0;

Map<String, dynamic> _$FindEmployeeResponseModelToJson(
  FindEmployeeResponseModel instance,
) => <String, dynamic>{
  'status': instance.statusCode,
  'code': instance.errorCode,
  'message': instance.message,
  'data': instance.data,
};

FindEmployeeModel _$FindEmployeeModelFromJson(Map<String, dynamic> json) =>
    FindEmployeeModel(
        totalRowItems: double.tryParse(
          json['TotalRowItems'].toString(),
        )?.toInt(),
        childItems: json['ChildItems'],
        items: (json['Items'] is List)
            ? (json['Items'] as List<dynamic>?)
                  ?.map((e) => ItemModel.fromJson(e as Map<String, dynamic>))
                  .toList()
            : [],
        results: (json['Results'] is List)
            ? (json['Results'] as List<dynamic>?)
                  ?.map(
                    (e) => EmployeeResultsModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
                  )
                  .toList()
            : [],
        totalRow: double.tryParse(json['TotalRow'].toString())?.toInt(),
      )
      ..success = bool.tryParse(json['success'].toString())
      ..statusCode = double.tryParse(json['statuscode'].toString())?.toInt()
      ..errorCode = double.tryParse(json['error_code'].toString())?.toInt() ?? 0
      ..message = json['message']?.toString();

Map<String, dynamic> _$FindEmployeeModelToJson(FindEmployeeModel instance) =>
    <String, dynamic>{
      'Items': instance.items,
      'success': instance.success,
      'statuscode': instance.statusCode,
      'error_code': instance.errorCode,
      'message': instance.message,
      'TotalRow': instance.totalRow,
      'TotalRowItems': instance.totalRowItems,
      'Results': instance.results,
      'ChildItems': instance.childItems,
    };

EmployeeResultsModel _$EmployeeResultsModelFromJson(Map json) =>
    EmployeeResultsModel(
      employeeID: json['EmployeeID']?.toString(),
      employeeName: json['EmployeeName']?.toString(),
    );

Map<String, dynamic> _$EmployeeResultsModelToJson(
  EmployeeResultsModel instance,
) => <String, dynamic>{
  'EmployeeID': instance.employeeID,
  'EmployeeName': instance.employeeName,
};
