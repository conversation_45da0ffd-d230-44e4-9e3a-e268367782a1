// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'data_inventory_ticket_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ListDepartmentResponseModel _$ListDepartmentResponseModelFromJson(
  Map<String, dynamic> json,
) =>
    ListDepartmentResponseModel(
        status: double.tryParse(json['status'].toString())?.toInt(),
        errorCode: double.tryParse(json['code'].toString())?.toInt(),
        message: json['message']?.toString(),
      )
      ..data = json['data'] == null || json['data'] is! Map
          ? null
          : ListDepartmentModel.fromJson(json['data'] as Map<String, dynamic>);

Map<String, dynamic> _$ListDepartmentResponseModelToJson(
  ListDepartmentResponseModel instance,
) => <String, dynamic>{
  'status': instance.status,
  'code': instance.errorCode,
  'message': instance.message,
  'data': instance.data,
};

ListDepartmentModel _$ListDepartmentModelFromJson(Map<String, dynamic> json) =>
    ListDepartmentModel(
        totalRowItems: double.tryParse(
          json['TotalRowItems'].toString(),
        )?.toInt(),
        childItems: (json['ChildItems'] is List)
            ? (json['ChildItems'] as List<dynamic>?)
                  ?.map((e) => ItemModel.fromJson(e as Map<String, dynamic>))
                  .toList()
            : [],
        items: (json['Items'] is List)
            ? (json['Items'] as List<dynamic>?)
                  ?.map((e) => ItemModel.fromJson(e as Map<String, dynamic>))
                  .toList()
            : [],
        masterRowKey: double.tryParse(json['MasterRowKey'].toString())?.toInt(),
        results: (json['Results'] is List)
            ? (json['Results'] as List<dynamic>?)
                  ?.map((e) => ItemModel.fromJson(e as Map<String, dynamic>))
                  .toList()
            : [],
        totalRow: double.tryParse(json['TotalRow'].toString())?.toInt(),
      )
      ..success = bool.tryParse(json['success'].toString())
      ..errorCode = double.tryParse(json['error_code'].toString())?.toInt()
      ..message = json['message']?.toString();

Map<String, dynamic> _$ListDepartmentModelToJson(
  ListDepartmentModel instance,
) => <String, dynamic>{
  'Items': instance.items,
  'success': instance.success,
  'error_code': instance.errorCode,
  'message': instance.message,
  'TotalRow': instance.totalRow,
  'TotalRowItems': instance.totalRowItems,
  'Results': instance.results,
  'ChildItems': instance.childItems,
  'MasterRowKey': instance.masterRowKey,
};
