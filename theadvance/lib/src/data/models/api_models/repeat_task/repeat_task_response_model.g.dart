// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'repeat_task_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RepeatTaskResponseModel _$RepeatTaskResponseModelFromJson(
  Map<String, dynamic> json,
) => RepeatTaskResponseModel(
  status: double.tryParse(json['status'].toString())?.toInt(),
  code: double.tryParse(json['code'].toString())?.toInt(),
  message: json['message']?.toString(),
  data: (json['data'] is List)
      ? (json['data'] as List<dynamic>?)
            ?.map((e) => RepeatTaskModel.fromJson(e as Map<String, dynamic>))
            .toList()
      : [],
);

Map<String, dynamic> _$RepeatTaskResponseModelToJson(
  RepeatTaskResponseModel instance,
) => <String, dynamic>{
  'status': instance.status,
  'code': instance.code,
  'message': instance.message,
  'data': instance.data,
};

RepeatTaskModel _$RepeatTaskModelFromJson(Map<String, dynamic> json) =>
    RepeatTaskModel(
      id: double.tryParse(json['id'].toString())?.toInt(),
      code: json['code']?.toString(),
      name: json['name']?.toString(),
    );

Map<String, dynamic> _$RepeatTaskModelToJson(RepeatTaskModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'code': instance.code,
      'name': instance.name,
    };
