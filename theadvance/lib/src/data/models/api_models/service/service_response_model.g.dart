// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'service_response_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ServiceItemsModelAdapter extends TypeAdapter<ServiceItemsModel> {
  @override
  final int typeId = 118;

  @override
  ServiceItemsModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ServiceItemsModel(
      code: fields[0] as String?,
      name: fields[1] as String?,
      url: fields[2] as String?,
      webAppUrl: fields[3] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, ServiceItemsModel obj) {
    writer
      ..writeByte(4)
      ..writeByte(0)
      ..write(obj.code)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.url)
      ..writeByte(3)
      ..write(obj.webAppUrl);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ServiceItemsModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ServiceResponseModel _$ServiceResponseModelFromJson(
  Map<String, dynamic> json,
) => ServiceResponseModel(
  errorCode: double.tryParse(json['code'].toString())?.toInt(),
  data: json['data'] == null || json['data'] is! Map
      ? null
      : ServiceTabBarModel.fromJson(json['data'] as Map<String, dynamic>),
  errorMessage: json['message']?.toString(),
);

Map<String, dynamic> _$ServiceResponseModelToJson(
  ServiceResponseModel instance,
) => <String, dynamic>{
  'code': instance.errorCode,
  'message': instance.errorMessage,
  'data': instance.data,
};

ServiceTabBarModel _$ServiceTabBarModelFromJson(Map<String, dynamic> json) =>
    ServiceTabBarModel(
      tabBarData: json['tab_bar'] == null || json['tab_bar'] is! Map
          ? null
          : TabbarModel.fromJson(json['tab_bar'] as Map<String, dynamic>),
      notificationCount: double.tryParse(
        json['notification_count'].toString(),
      )?.toInt(),
      menuItems:
          ((json['home_menu'] is List)
              ? (json['home_menu'] as List<dynamic>?)
                    ?.map(
                      (e) => e == null || e is! Map
                          ? null
                          : ServiceItemsModel.fromJson(
                              e as Map<String, dynamic>,
                            ),
                    )
                    .toList()
              : []) ??
          [],
      total: double.tryParse(json['total'].toString())?.toInt(),
      popup: json['popup'] == null || json['popup'] is! Map
          ? null
          : HomePopup.fromJson(json['popup'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$ServiceTabBarModelToJson(ServiceTabBarModel instance) =>
    <String, dynamic>{
      'tab_bar': instance.tabBarData,
      'notification_count': instance.notificationCount,
      'home_menu': instance.menuItems,
      'total': instance.total,
      'popup': instance.popup,
    };

HomePopup _$HomePopupFromJson(Map<String, dynamic> json) => HomePopup(
  id: json['id']?.toString(),
  name: json['name']?.toString(),
  code: json['code']?.toString(),
  status: json['status']?.toString(),
  activeDate: json['activeDate'],
  inactiveDate: json['inactiveDate'],
  icon: json['icon']?.toString(),
  smallIcon: json['smallIcon']?.toString(),
  action: json['action'] == null || json['action'] is! Map
      ? null
      : HomePopupAction.fromJson(json['action'] as Map<String, dynamic>),
  createdBy: json['createdBy']?.toString(),
  createdAt: json['createdAt']?.toString(),
  updatedAt: json['updatedAt']?.toString(),
  updatedBy: json['updatedBy']?.toString(),
  activeCount: json['activeCount'],
  rangeActiveDate: (json['rangeActiveDate'] is List)
      ? (json['rangeActiveDate'] as List<dynamic>?)
            ?.map((e) => e as String)
            .toList()
      : [],
);

Map<String, dynamic> _$HomePopupToJson(HomePopup instance) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'code': instance.code,
  'status': instance.status,
  'activeDate': instance.activeDate,
  'inactiveDate': instance.inactiveDate,
  'icon': instance.icon,
  'smallIcon': instance.smallIcon,
  'action': instance.action,
  'createdBy': instance.createdBy,
  'createdAt': instance.createdAt,
  'updatedAt': instance.updatedAt,
  'updatedBy': instance.updatedBy,
  'activeCount': instance.activeCount,
  'rangeActiveDate': instance.rangeActiveDate,
};

HomePopupAction _$HomePopupActionFromJson(Map<String, dynamic> json) =>
    HomePopupAction(
      type: json['type']?.toString(),
      value: json['value']?.toString(),
    );

Map<String, dynamic> _$HomePopupActionToJson(HomePopupAction instance) =>
    <String, dynamic>{'type': instance.type, 'value': instance.value};

ServiceItemsModel _$ServiceItemsModelFromJson(Map<String, dynamic> json) =>
    ServiceItemsModel(
      code: json['code']?.toString(),
      name: json['name']?.toString(),
      url: json['icon_url']?.toString(),
      webAppUrl: json['webAppUrl']?.toString(),
    );

Map<String, dynamic> _$ServiceItemsModelToJson(ServiceItemsModel instance) =>
    <String, dynamic>{
      'code': instance.code,
      'name': instance.name,
      'icon_url': instance.url,
      'webAppUrl': instance.webAppUrl,
    };

TabbarModel _$TabbarModelFromJson(Map<String, dynamic> json) => TabbarModel(
  home: json['home']?.toString(),
  work: json['task']?.toString(),
  checkin: json['check_in']?.toString(),
  more: json['more']?.toString(),
);

Map<String, dynamic> _$TabbarModelToJson(TabbarModel instance) =>
    <String, dynamic>{
      'home': instance.home,
      'task': instance.work,
      'check_in': instance.checkin,
      'more': instance.more,
    };
