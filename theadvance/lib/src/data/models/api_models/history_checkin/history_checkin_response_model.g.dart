// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'history_checkin_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

HistoryCheckinResponseModel _$HistoryCheckinResponseModelFromJson(
  Map<String, dynamic> json,
) => HistoryCheckinResponseModel(
  code: double.tryParse(json['code'].toString())?.toInt(),
  message: json['message']?.toString(),
  data: json['data'] == null || json['data'] is! Map
      ? null
      : HistoryCheckinModel.fromJson(json['data'] as Map<String, dynamic>),
);

Map<String, dynamic> _$HistoryCheckinResponseModelToJson(
  HistoryCheckinResponseModel instance,
) => <String, dynamic>{
  'code': instance.code,
  'message': instance.message,
  'data': instance.data,
};

HistoryCheckinModel _$HistoryCheckinModelFromJson(Map<String, dynamic> json) =>
    HistoryCheckinModel(
      leaveDays: json['leave_days']?.toString(),
      items:
          ((json['items'] is List)
              ? (json['items'] as List<dynamic>?)
                    ?.map(
                      (e) => e == null || e is! Map
                          ? null
                          : CheckinMonthItemsModel.fromJson(
                              e as Map<String, dynamic>,
                            ),
                    )
                    .toList()
              : []) ??
          [],
    );

Map<String, dynamic> _$HistoryCheckinModelToJson(
  HistoryCheckinModel instance,
) => <String, dynamic>{
  'leave_days': instance.leaveDays,
  'items': instance.items,
};

CheckinMonthItemsModel _$CheckinMonthItemsModelFromJson(
  Map<String, dynamic> json,
) => CheckinMonthItemsModel(
  total: json['total']?.toString(),
  month: double.tryParse(json['month'].toString())?.toInt(),
  details:
      ((json['detail'] is List)
          ? (json['detail'] as List<dynamic>?)
                ?.map(
                  (e) => e == null || e is! Map
                      ? null
                      : CheckinDetailModel.fromJson(e as Map<String, dynamic>),
                )
                .toList()
          : []) ??
      [],
  year: double.tryParse(json['year'].toString())?.toInt(),
);

Map<String, dynamic> _$CheckinMonthItemsModelToJson(
  CheckinMonthItemsModel instance,
) => <String, dynamic>{
  'total': instance.total,
  'month': instance.month,
  'year': instance.year,
  'detail': instance.details,
};

CheckinDetailModel _$CheckinDetailModelFromJson(Map<String, dynamic> json) =>
    CheckinDetailModel(
      date: json['date']?.toString(),
      total: double.tryParse(json['total'].toString()),
      isFull: bool.tryParse(json['is_full'].toString()),
      status: json['status']?.toString(),
      records:
          ((json['records'] is List)
              ? (json['records'] as List<dynamic>?)
                    ?.map(
                      (e) => CheckinRecordModel.fromJson(
                        e as Map<String, dynamic>,
                      ),
                    )
                    .toList()
              : []) ??
          [],
      workingTime: json['working_time']?.toString(),
    );

Map<String, dynamic> _$CheckinDetailModelToJson(CheckinDetailModel instance) =>
    <String, dynamic>{
      'date': instance.date,
      'total': instance.total,
      'is_full': instance.isFull,
      'working_time': instance.workingTime,
      'status': instance.status,
      'records': instance.records,
    };

CheckinRecordModel _$CheckinRecordModelFromJson(Map<String, dynamic> json) =>
    CheckinRecordModel(
      time: json['time']?.toString(),
      type: json['type']?.toString(),
      appType: json['app_type']?.toString(),
      title: json['title']?.toString(),
    );

Map<String, dynamic> _$CheckinRecordModelToJson(CheckinRecordModel instance) =>
    <String, dynamic>{
      'time': instance.time,
      'type': instance.type,
      'app_type': instance.appType,
      'title': instance.title,
    };

CheckinUpdateInfoModel _$CheckinUpdateInfoModelFromJson(
  Map<String, dynamic> json,
) => CheckinUpdateInfoModel(
  type: json['type']?.toString(),
  groupType: json['group_type']?.toString(),
  requestDate: double.tryParse(json['request_date'].toString())?.toInt(),
  fromDate: double.tryParse(json['from_date'].toString())?.toInt(),
  toDate: double.tryParse(json['to_date'].toString())?.toInt(),
  notes: json['notes']?.toString(),
  branchId: json['branch_id']?.toString(),
);

Map<String, dynamic> _$CheckinUpdateInfoModelToJson(
  CheckinUpdateInfoModel instance,
) => <String, dynamic>{
  'type': instance.type,
  'group_type': instance.groupType,
  'request_date': instance.requestDate,
  'from_date': instance.fromDate,
  'to_date': instance.toDate,
  'notes': instance.notes,
  'branch_id': instance.branchId,
};
