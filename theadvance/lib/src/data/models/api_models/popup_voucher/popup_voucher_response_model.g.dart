// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'popup_voucher_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PopupVoucherResponseModel _$PopupVoucherResponseModelFromJson(
  Map<String, dynamic> json,
) => PopupVoucherResponseModel(
  status: double.tryParse(json['status'].toString())?.toInt(),
  errorCode: double.tryParse(json['error_code'].toString())?.toInt(),
  errorMessage: json['error_message']?.toString(),
  listPopup: (json['data'] is List)
      ? (json['data'] as List<dynamic>?)
            ?.map((e) => PopupModel.fromJson(e as Map<String, dynamic>))
            .toList()
      : [],
);

Map<String, dynamic> _$PopupVoucherResponseModelToJson(
  PopupVoucherResponseModel instance,
) => <String, dynamic>{
  'status': instance.status,
  'error_code': instance.errorCode,
  'error_message': instance.errorMessage,
  'data': instance.listPopup,
};

PopupModel _$PopupModelFromJson(Map<String, dynamic> json) => PopupModel(
  updatedDate: json['updated_date']?.toString(),
  deleted: bool.tryParse(json['deleted'].toString()),
  img: json['img']?.toString(),
  sliderImg: json['slider_img']?.toString(),
  title: json['title']?.toString(),
  href: json['href']?.toString(),
  publishDate: json['publish_date']?.toString(),
  active: bool.tryParse(json['active'].toString()),
  slide: bool.tryParse(json['slide'].toString()),
  content: json['content']?.toString(),
  sort: double.tryParse(json['sort'].toString())?.toInt(),
  id: json['id']?.toString(),
);

Map<String, dynamic> _$PopupModelToJson(PopupModel instance) =>
    <String, dynamic>{
      'updated_date': instance.updatedDate,
      'deleted': instance.deleted,
      'img': instance.img,
      'slider_img': instance.sliderImg,
      'title': instance.title,
      'href': instance.href,
      'publish_date': instance.publishDate,
      'active': instance.active,
      'slide': instance.slide,
      'content': instance.content,
      'sort': instance.sort,
      'id': instance.id,
    };
