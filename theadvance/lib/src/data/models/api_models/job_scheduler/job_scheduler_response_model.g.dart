// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'job_scheduler_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

JobSchedulerResponseModel _$JobSchedulerResponseModelFromJson(
  Map<String, dynamic> json,
) => JobSchedulerResponseModel(
  status: double.tryParse(json['status'].toString())?.toInt(),
  errorCode: double.tryParse(json['code'].toString())?.toInt(),
  errorMessage: json['message']?.toString(),
  data: json['data'] == null || json['data'] is! Map
      ? null
      : JobSchedulerModel.fromJson(json['data'] as Map<String, dynamic>),
);

Map<String, dynamic> _$JobSchedulerResponseModelToJson(
  JobSchedulerResponseModel instance,
) => <String, dynamic>{
  'status': instance.status,
  'code': instance.errorCode,
  'message': instance.errorMessage,
  'data': instance.data,
};

JobSchedulerModel _$JobSchedulerModelFromJson(Map<String, dynamic> json) =>
    JobSchedulerModel(
      items: (json['items'] is List)
          ? (json['items'] as List<dynamic>?)
                ?.map(
                  (e) =>
                      JobSchedulerItemModel.fromJson(e as Map<String, dynamic>),
                )
                .toList()
          : [],
      total: double.tryParse(json['total'].toString())?.toInt(),
      isMore: bool.tryParse(json['is_more'].toString()),
    );

Map<String, dynamic> _$JobSchedulerModelToJson(JobSchedulerModel instance) =>
    <String, dynamic>{
      'items': instance.items,
      'total': instance.total,
      'is_more': instance.isMore,
    };

JobSchedulerItemModel _$JobSchedulerItemModelFromJson(
  Map<String, dynamic> json,
) => JobSchedulerItemModel(
  product: json['product']?.toString(),
  media: (json['media'] is List)
      ? (json['media'] as List<dynamic>?)?.map((e) => e as String).toList()
      : [],
  status: double.tryParse(json['status'].toString())?.toInt(),
  name: json['name']?.toString(),
  detail: json['detail']?.toString(),
  period: json['period']?.toString(),
  dueDate: double.tryParse(json['due_date'].toString())?.toInt(),
  dueDateText: json['due_date_text']?.toString(),
  priorityUrgency: bool.tryParse(json['priority_urgency'].toString()),
  priorityImportance: bool.tryParse(json['priority_importance'].toString()),
  assignees: (json['assignees'] is List)
      ? (json['assignees'] as List<dynamic>?)
            ?.map(
              (e) => JobSchedulerStaffModel.fromJson(e as Map<String, dynamic>),
            )
            .toList()
      : [],
  id: json['id']?.toString(),
  updatedTime: json['updated_time_text']?.toString(),
  createdTime: json['created_time_text']?.toString(),
  createby: json['created_by_info'] == null || json['created_by_info'] is! Map
      ? null
      : JobSchedulerStaffModel.fromJson(
          json['created_by_info'] as Map<String, dynamic>,
        ),
  updateby: json['updated_by_info'] == null || json['updated_by_info'] is! Map
      ? null
      : JobSchedulerStaffModel.fromJson(
          json['updated_by_info'] as Map<String, dynamic>,
        ),
  level: double.tryParse(json['level'].toString())?.toInt(),
  conservations: (json['comment'] is List)
      ? (json['comment'] as List<dynamic>?)
            ?.map((e) => ConservationModel.fromJson(e as Map<String, dynamic>))
            .toList()
      : [],
  type: double.tryParse(json['type'].toString())?.toInt(),
);

Map<String, dynamic> _$JobSchedulerItemModelToJson(
  JobSchedulerItemModel instance,
) => <String, dynamic>{
  'product': instance.product,
  'media': instance.media,
  'status': instance.status,
  'name': instance.name,
  'detail': instance.detail,
  'period': instance.period,
  'due_date': instance.dueDate,
  'due_date_text': instance.dueDateText,
  'priority_urgency': instance.priorityUrgency,
  'priority_importance': instance.priorityImportance,
  'assignees': instance.assignees,
  'id': instance.id,
  'updated_time_text': instance.updatedTime,
  'created_time_text': instance.createdTime,
  'created_by_info': instance.createby,
  'updated_by_info': instance.updateby,
  'level': instance.level,
  'comment': instance.conservations,
  'type': instance.type,
};

JobSchedulerStaffModel _$JobSchedulerStaffModelFromJson(
  Map<String, dynamic> json,
) => JobSchedulerStaffModel(
  id: json['id']?.toString(),
  name: json['name']?.toString(),
  avatar: json['avatar']?.toString(),
  status: double.tryParse(json['status'].toString())?.toInt(),
);

Map<String, dynamic> _$JobSchedulerStaffModelToJson(
  JobSchedulerStaffModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'avatar': instance.avatar,
  'status': instance.status,
};

ConservationModel _$ConservationModelFromJson(Map<String, dynamic> json) =>
    ConservationModel(
      files: (json['media'] is List)
          ? (json['media'] as List<dynamic>?)?.map((e) => e as String).toList()
          : [],
      id: json['id']?.toString(),
      name: json['name']?.toString(),
      avatar: json['avatar']?.toString(),
      text: json['text']?.toString(),
      createdTimeText: json['created_time_text']?.toString(),
      updatedTimeText: json['updated_time_text']?.toString(),
      createdTime: double.tryParse(json['created_time'].toString())?.toInt(),
      updatedTime: double.tryParse(json['updated_time'].toString())?.toInt(),
    );

Map<String, dynamic> _$ConservationModelToJson(ConservationModel instance) =>
    <String, dynamic>{
      'media': instance.files,
      'id': instance.id,
      'name': instance.name,
      'avatar': instance.avatar,
      'text': instance.text,
      'created_time_text': instance.createdTimeText,
      'updated_time_text': instance.updatedTimeText,
      'created_time': instance.createdTime,
      'updated_time': instance.updatedTime,
    };
