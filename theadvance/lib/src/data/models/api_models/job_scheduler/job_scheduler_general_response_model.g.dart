// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'job_scheduler_general_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

JobSchedulerGeneralResponseModel _$JobSchedulerGeneralResponseModelFromJson(
  Map<String, dynamic> json,
) => JobSchedulerGeneralResponseModel(
  status: double.tryParse(json['status'].toString())?.toInt(),
  errorCode: double.tryParse(json['code'].toString())?.toInt(),
  errorMessage: json['message']?.toString(),
  data: json['data'] == null || json['data'] is! Map
      ? null
      : JobSchedulerGeneralModel.fromJson(json['data'] as Map<String, dynamic>),
);

Map<String, dynamic> _$JobSchedulerGeneralResponseModelToJson(
  JobSchedulerGeneralResponseModel instance,
) => <String, dynamic>{
  'status': instance.status,
  'code': instance.errorCode,
  'message': instance.errorMessage,
  'data': instance.data,
};

JobSchedulerGeneralModel _$JobSchedulerGeneralModelFromJson(
  Map<String, dynamic> json,
) => JobSchedulerGeneralModel(
  statusValue: (json['status_value'] is List)
      ? (json['status_value'] as List<dynamic>?)
            ?.map(
              (e) => e == null || e is! Map
                  ? null
                  : StatusTaskModel.fromJson(e as Map<String, dynamic>),
            )
            .toList()
      : [],
  typeValue: (json['type_value'] is List)
      ? (json['type_value'] as List<dynamic>?)
            ?.map(
              (e) => e == null || e is! Map
                  ? null
                  : TypeTaskModel.fromJson(e as Map<String, dynamic>),
            )
            .toList()
      : [],
);

Map<String, dynamic> _$JobSchedulerGeneralModelToJson(
  JobSchedulerGeneralModel instance,
) => <String, dynamic>{
  'status_value': instance.statusValue,
  'type_value': instance.typeValue,
};

StatusTaskModel _$StatusTaskModelFromJson(Map<String, dynamic> json) =>
    StatusTaskModel(
      id: double.tryParse(json['id'].toString())?.toInt(),
      name: json['name']?.toString(),
      count: double.tryParse(json['count'].toString())?.toInt(),
    );

Map<String, dynamic> _$StatusTaskModelToJson(StatusTaskModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'count': instance.count,
    };

TypeTaskModel _$TypeTaskModelFromJson(Map<String, dynamic> json) =>
    TypeTaskModel(
      id: double.tryParse(json['id'].toString())?.toInt(),
      name: json['name']?.toString(),
    );

Map<String, dynamic> _$TypeTaskModelToJson(TypeTaskModel instance) =>
    <String, dynamic>{'id': instance.id, 'name': instance.name};
