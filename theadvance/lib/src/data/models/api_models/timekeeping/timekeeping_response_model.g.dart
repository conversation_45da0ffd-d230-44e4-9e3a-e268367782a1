// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'timekeeping_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TimekeepingResponseModel _$TimekeepingResponseModelFromJson(
  Map<String, dynamic> json,
) => TimekeepingResponseModel(
  status: double.tryParse(json['status'].toString())?.toInt(),
  errorCode: double.tryParse(json['error_code'].toString())?.toInt(),
  errorMessage: json['error_message']?.toString(),
  data: json['data'] == null || json['data'] is! Map
      ? null
      : TimekeepingModel.fromJson(json['data'] as Map<String, dynamic>),
);

Map<String, dynamic> _$TimekeepingResponseModelToJson(
  TimekeepingResponseModel instance,
) => <String, dynamic>{
  'status': instance.status,
  'error_code': instance.errorCode,
  'error_message': instance.errorMessage,
  'data': instance.data,
};

TimekeepingModel _$TimekeepingModelFromJson(Map<String, dynamic> json) =>
    TimekeepingModel(
      section: json['section']?.toString(),
      position: json['position']?.toString(),
      reality: double.tryParse(json['reality'].toString())?.toInt(),
      timekeeper: double.tryParse(json['timekeeper'].toString())?.toInt(),
      workedDays: double.tryParse(json['worked_days'].toString())?.toInt(),
      rate: json['rate']?.toString(),
      annualLeave: double.tryParse(json['annual_leave'].toString())?.toInt(),
    );

Map<String, dynamic> _$TimekeepingModelToJson(TimekeepingModel instance) =>
    <String, dynamic>{
      'section': instance.section,
      'position': instance.position,
      'reality': instance.reality,
      'timekeeper': instance.timekeeper,
      'worked_days': instance.workedDays,
      'rate': instance.rate,
      'annual_leave': instance.annualLeave,
    };
