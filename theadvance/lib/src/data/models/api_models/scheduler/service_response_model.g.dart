// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'service_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ServicesResponseModel _$ServicesResponseModelFromJson(
  Map<String, dynamic> json,
) => ServicesResponseModel(
  status: double.tryParse(json['status'].toString())?.toInt(),
  errorCode: double.tryParse(json['error_code'].toString())?.toInt(),
  errorMessage: json['error_message']?.toString(),
  listService: (json['data'] is List)
      ? (json['data'] as List<dynamic>?)
            ?.map((e) => ServiceModel.fromJson(e as Map<String, dynamic>))
            .toList()
      : [],
);

Map<String, dynamic> _$ServicesResponseModelToJson(
  ServicesResponseModel instance,
) => <String, dynamic>{
  'status': instance.status,
  'error_code': instance.errorCode,
  'error_message': instance.errorMessage,
  'data': instance.listService,
};

ServiceModel _$ServiceModelFromJson(Map<String, dynamic> json) => ServiceModel(
  id: json['id']?.toString(),
  name: json['name']?.toString(),
  image: json['image']?.toString(),
);

Map<String, dynamic> _$ServiceModelToJson(ServiceModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'image': instance.image,
    };
