// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'branches_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BranchesResponseModel _$BranchesResponseModelFromJson(
  Map<String, dynamic> json,
) => BranchesResponseModel(
  status: double.tryParse(json['status'].toString())?.toInt(),
  code: double.tryParse(json['code'].toString())?.toInt(),
  message: json['message']?.toString(),
  data: json['data'] == null || json['data'] is! Map
      ? null
      : BranchModel.fromJson(json['data'] as Map<String, dynamic>),
);

Map<String, dynamic> _$BranchesResponseModelToJson(
  BranchesResponseModel instance,
) => <String, dynamic>{
  'status': instance.status,
  'code': instance.code,
  'message': instance.message,
  'data': instance.data,
};

BranchModel _$BranchModelFromJson(Map<String, dynamic> json) => BranchModel(
  items:
      ((json['items'] is List)
          ? (json['items'] as List<dynamic>?)
                ?.map(
                  (e) => e == null || e is! Map
                      ? null
                      : BranchItemsModel.fromJson(e as Map<String, dynamic>),
                )
                .toList()
          : []) ??
      [],
);

Map<String, dynamic> _$BranchModelToJson(BranchModel instance) =>
    <String, dynamic>{'items': instance.items};

BranchItemsModel _$BranchItemsModelFromJson(Map<String, dynamic> json) =>
    BranchItemsModel(
      name: json['name']?.toString(),
      id: json['id']?.toString(),
      icon: json['icon']?.toString(),
      code: json['code']?.toString(),
      province: json['province']?.toString(),
    );

Map<String, dynamic> _$BranchItemsModelToJson(BranchItemsModel instance) =>
    <String, dynamic>{
      'name': instance.name,
      'id': instance.id,
      'code': instance.code,
      'icon': instance.icon,
      'province': instance.province,
    };
