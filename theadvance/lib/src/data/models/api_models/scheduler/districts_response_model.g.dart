// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'districts_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DistrictsResponseModel _$DistrictsResponseModelFromJson(
  Map<String, dynamic> json,
) => DistrictsResponseModel(
  status: double.tryParse(json['status'].toString())?.toInt(),
  errorCode: double.tryParse(json['error_code'].toString())?.toInt(),
  errorMessage: json['error_message']?.toString(),
  listDistrict: (json['data'] is List)
      ? (json['data'] as List<dynamic>?)
            ?.map((e) => DistrictModel.fromJson(e as Map<String, dynamic>))
            .toList()
      : [],
);

Map<String, dynamic> _$DistrictsResponseModelToJson(
  DistrictsResponseModel instance,
) => <String, dynamic>{
  'status': instance.status,
  'error_code': instance.errorCode,
  'error_message': instance.errorMessage,
  'data': instance.listDistrict,
};

DistrictModel _$DistrictModelFromJson(Map<String, dynamic> json) =>
    DistrictModel(district: json['district']?.toString());

Map<String, dynamic> _$DistrictModelToJson(DistrictModel instance) =>
    <String, dynamic>{'district': instance.district};
