// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'appointment_day_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AppointmentDayResponseModel _$AppointmentDayResponseModelFromJson(
  Map<String, dynamic> json,
) => AppointmentDayResponseModel(
  status: double.tryParse(json['status'].toString())?.toInt(),
  errorCode: double.tryParse(json['error_code'].toString())?.toInt(),
  errorMessage: json['error_message']?.toString(),
  listAppointmentDay: (json['data'] is List)
      ? (json['data'] as List<dynamic>?)
            ?.map(
              (e) => AppointmentDayModel.fromJson(e as Map<String, dynamic>),
            )
            .toList()
      : [],
);

Map<String, dynamic> _$AppointmentDayResponseModelToJson(
  AppointmentDayResponseModel instance,
) => <String, dynamic>{
  'status': instance.status,
  'error_code': instance.errorCode,
  'error_message': instance.errorMessage,
  'data': instance.listAppointmentDay,
};

AppointmentDayModel _$AppointmentDayModelFromJson(Map<String, dynamic> json) =>
    AppointmentDayModel(
      date: double.tryParse(json['date'].toString())?.toInt(),
      day: double.tryParse(json['day'].toString())?.toInt(),
      dayText: json['day_text']?.toString(),
      fullDate: json['full_date']?.toString(),
    );

Map<String, dynamic> _$AppointmentDayModelToJson(
  AppointmentDayModel instance,
) => <String, dynamic>{
  'date': instance.date,
  'day': instance.day,
  'day_text': instance.dayText,
  'full_date': instance.fullDate,
};
