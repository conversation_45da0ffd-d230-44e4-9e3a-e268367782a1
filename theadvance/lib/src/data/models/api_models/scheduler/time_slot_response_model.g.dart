// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'time_slot_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TimeSlotResponseModel _$TimeSlotResponseModelFromJson(
  Map<String, dynamic> json,
) => TimeSlotResponseModel(
  status: double.tryParse(json['status'].toString())?.toInt(),
  errorCode: double.tryParse(json['error_code'].toString())?.toInt(),
  errorMessage: json['error_message']?.toString(),
  listTimeSlot: (json['data'] is List)
      ? (json['data'] as List<dynamic>?)
            ?.map((e) => TimeSlotModel.fromJson(e as Map<String, dynamic>))
            .toList()
      : [],
);

Map<String, dynamic> _$TimeSlotResponseModelToJson(
  TimeSlotResponseModel instance,
) => <String, dynamic>{
  'status': instance.status,
  'error_code': instance.errorCode,
  'error_message': instance.errorMessage,
  'data': instance.listTimeSlot,
};

TimeSlotModel _$TimeSlotModelFromJson(Map<String, dynamic> json) =>
    TimeSlotModel(
      time: json['time']?.toString(),
      status: json['status']?.toString(),
    );

Map<String, dynamic> _$TimeSlotModelToJson(TimeSlotModel instance) =>
    <String, dynamic>{'time': instance.time, 'status': instance.status};
