// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'provinces_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ProvincesResponseModel _$ProvincesResponseModelFromJson(
  Map<String, dynamic> json,
) => ProvincesResponseModel(
  status: double.tryParse(json['status'].toString())?.toInt(),
  errorCode: double.tryParse(json['error_code'].toString())?.toInt(),
  errorMessage: json['error_message']?.toString(),
  listProvince: (json['data'] is List)
      ? (json['data'] as List<dynamic>?)?.map((e) => e as String).toList()
      : [],
);

Map<String, dynamic> _$ProvincesResponseModelToJson(
  ProvincesResponseModel instance,
) => <String, dynamic>{
  'status': instance.status,
  'error_code': instance.errorCode,
  'error_message': instance.errorMessage,
  'data': instance.listProvince,
};

ProvinceV2ResponseModel _$ProvinceV2ResponseModelFromJson(
  Map<String, dynamic> json,
) => ProvinceV2ResponseModel(
  status: double.tryParse(json['status'].toString())?.toInt(),
  errorCode: double.tryParse(json['error_code'].toString())?.toInt(),
  errorMessage: json['error_message']?.toString(),
  listProvinceV2: (json['data'] is List)
      ? (json['data'] as List<dynamic>?)
            ?.map(
              (e) => e == null || e is! Map
                  ? null
                  : ProvinceV2Model.fromJson(e as Map<String, dynamic>),
            )
            .toList()
      : [],
);

Map<String, dynamic> _$ProvinceV2ResponseModelToJson(
  ProvinceV2ResponseModel instance,
) => <String, dynamic>{
  'status': instance.status,
  'error_code': instance.errorCode,
  'error_message': instance.errorMessage,
  'data': instance.listProvinceV2,
};

ProvinceV2Model _$ProvinceV2ModelFromJson(Map<String, dynamic> json) =>
    ProvinceV2Model(
      province: json['province']?.toString(),
      total: double.tryParse(json['total'].toString())?.toInt(),
    );

Map<String, dynamic> _$ProvinceV2ModelToJson(ProvinceV2Model instance) =>
    <String, dynamic>{'province': instance.province, 'total': instance.total};
