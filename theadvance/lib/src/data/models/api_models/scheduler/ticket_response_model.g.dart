// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ticket_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TicketResponseModel _$TicketResponseModelFromJson(Map<String, dynamic> json) =>
    TicketResponseModel(
      status: double.tryParse(json['status'].toString())?.toInt(),
      errorCode: double.tryParse(json['error_code'].toString())?.toInt(),
      errorMessage: json['error_message']?.toString(),
      ticket: json['data'] == null || json['data'] is! Map
          ? null
          : TicketModel.fromJson(json['data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$TicketResponseModelToJson(
  TicketResponseModel instance,
) => <String, dynamic>{
  'status': instance.status,
  'error_code': instance.errorCode,
  'error_message': instance.errorMessage,
  'data': instance.ticket,
};

TicketModel _$TicketModelFromJson(Map<String, dynamic> json) => TicketModel();

Map<String, dynamic> _$TicketModelToJson(TicketModel instance) =>
    <String, dynamic>{};
