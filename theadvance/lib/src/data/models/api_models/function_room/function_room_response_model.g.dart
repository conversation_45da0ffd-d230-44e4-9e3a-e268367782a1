// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'function_room_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FunctionRoomResponseModel _$FunctionRoomResponseModelFromJson(
  Map<String, dynamic> json,
) => FunctionRoomResponseModel(
  code: double.tryParse(json['code'].toString())?.toInt(),
  message: json['message']?.toString(),
  data: (json['data'] is List)
      ? (json['data'] as List<dynamic>?)
            ?.map(
              (e) => e == null || e is! Map
                  ? null
                  : FunctionRoomItemModel.fromJson(e as Map<String, dynamic>),
            )
            .toList()
      : [],
);

Map<String, dynamic> _$FunctionRoomResponseModelToJson(
  FunctionRoomResponseModel instance,
) => <String, dynamic>{
  'code': instance.code,
  'message': instance.message,
  'data': instance.data,
};

FunctionRoomItemModel _$FunctionRoomItemModelFromJson(
  Map<String, dynamic> json,
) => FunctionRoomItemModel(
  json['id']?.toString(),
  json['date']?.toString(),
  json['content']?.toString(),
  (json['images'] is List)
      ? (json['images'] as List<dynamic>?)?.map((e) => e?.toString()).toList()
      : [],
);

Map<String, dynamic> _$FunctionRoomItemModelToJson(
  FunctionRoomItemModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'date': instance.date,
  'content': instance.content,
  'images': instance.images,
};
