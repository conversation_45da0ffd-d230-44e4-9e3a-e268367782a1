// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'home_info_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

HomeInfoResponseModel _$HomeInfoResponseModelFromJson(
  Map<String, dynamic> json,
) => HomeInfoResponseModel(
  errorCode: double.tryParse(json['error_code'].toString())?.toInt(),
  errorMessage: json['error_message']?.toString(),
  homeInfo: json['data'] == null || json['data'] is! Map
      ? null
      : HomeInfoModel.fromJson(json['data'] as Map<String, dynamic>),
);

Map<String, dynamic> _$HomeInfoResponseModelToJson(
  HomeInfoResponseModel instance,
) => <String, dynamic>{
  'error_code': instance.errorCode,
  'error_message': instance.errorMessage,
  'data': instance.homeInfo,
};

HomeInfoModel _$HomeInfoModelFromJson(Map<String, dynamic> json) =>
    HomeInfoModel(
      homeAds: json['home_ads'] == null || json['home_ads'] is! Map
          ? null
          : HomeAdsModel.fromJson(json['home_ads'] as Map<String, dynamic>),
      menuItems: json['menu_items'] == null || json['menu_items'] is! Map
          ? null
          : MenuItemsModel.fromJson(json['menu_items'] as Map<String, dynamic>),
      news: json['news'] == null || json['news'] is! Map
          ? null
          : HomeNewsModel.fromJson(json['news'] as Map<String, dynamic>),
      notifications:
          json['notifications'] == null || json['notifications'] is! Map
          ? null
          : HomeNotificationsModel.fromJson(
              json['notifications'] as Map<String, dynamic>,
            ),
      schedule: json['schedule']?.toString(),
      greeting: json['greeting']?.toString(),
      floatButton: json['float_button'] == null || json['float_button'] is! Map
          ? null
          : FloatButton.fromJson(json['float_button'] as Map<String, dynamic>),
      banners: (json['banners'] is List)
          ? (json['banners'] as List<dynamic>?)
                ?.map((e) => HomeAdsModel.fromJson(e as Map<String, dynamic>))
                .toList()
          : [],
      expert: (json['expert'] is List)
          ? (json['expert'] as List<dynamic>?)
                ?.map((e) => ExpertModel.fromJson(e as Map<String, dynamic>))
                .toList()
          : [],
      videos: (json['videos'] is List)
          ? (json['videos'] as List<dynamic>?)
                ?.map((e) => VideoModel.fromJson(e as Map<String, dynamic>))
                .toList()
          : [],
    );

Map<String, dynamic> _$HomeInfoModelToJson(HomeInfoModel instance) =>
    <String, dynamic>{
      'home_ads': instance.homeAds,
      'menu_items': instance.menuItems,
      'news': instance.news,
      'notifications': instance.notifications,
      'schedule': instance.schedule,
      'greeting': instance.greeting,
      'float_button': instance.floatButton,
      'banners': instance.banners,
      'expert': instance.expert,
      'videos': instance.videos,
    };

FloatButton _$FloatButtonFromJson(Map<String, dynamic> json) => FloatButton(
  imageUrl: json['image_url']?.toString(),
  landingPageUrl: json['landing_page_url']?.toString(),
  active: bool.tryParse(json['active'].toString()),
  canClose: bool.tryParse(json['can_close'].toString()),
);

Map<String, dynamic> _$FloatButtonToJson(FloatButton instance) =>
    <String, dynamic>{
      'image_url': instance.imageUrl,
      'landing_page_url': instance.landingPageUrl,
      'can_close': instance.canClose,
      'active': instance.active,
    };

HomeAdsModel _$HomeAdsModelFromJson(Map<String, dynamic> json) => HomeAdsModel(
  type: json['type']?.toString(),
  url: json['image_url']?.toString(),
  id: json['id']?.toString(),
  active: bool.tryParse(json['active'].toString()) ?? false,
  action: json['action'] == null || json['action'] is! Map
      ? null
      : HomePopupActionModel.fromJson(json['action'] as Map<String, dynamic>),
);

Map<String, dynamic> _$HomeAdsModelToJson(HomeAdsModel instance) =>
    <String, dynamic>{
      'action': instance.action,
      'type': instance.type,
      'image_url': instance.url,
      'id': instance.id,
      'active': instance.active,
    };

MenuItemsModel _$MenuItemsModelFromJson(Map<String, dynamic> json) =>
    MenuItemsModel(
      total: double.tryParse(json['total'].toString())?.toInt(),
      items: (json['items'] is List)
          ? (json['items'] as List<dynamic>?)
                ?.map((e) => MenuItemModel.fromJson(e as Map<String, dynamic>))
                .toList()
          : [],
    );

Map<String, dynamic> _$MenuItemsModelToJson(MenuItemsModel instance) =>
    <String, dynamic>{'total': instance.total, 'items': instance.items};

MenuItemModel _$MenuItemModelFromJson(Map<String, dynamic> json) =>
    MenuItemModel(
      id: double.tryParse(json['id'].toString())?.toInt() ?? 0,
      name: json['name']?.toString(),
      typeCode: json['type_code']?.toString(),
      iconUrl: json['icon_url']?.toString(),
    );

Map<String, dynamic> _$MenuItemModelToJson(MenuItemModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'type_code': instance.typeCode,
      'icon_url': instance.iconUrl,
    };

HomeNewsModel _$HomeNewsModelFromJson(Map<String, dynamic> json) =>
    HomeNewsModel(
      total: double.tryParse(json['total'].toString())?.toInt(),
      items: (json['items'] is List)
          ? (json['items'] as List<dynamic>?)
                ?.map((e) => NewsItemModel.fromJson(e as Map<String, dynamic>))
                .toList()
          : [],
    );

Map<String, dynamic> _$HomeNewsModelToJson(HomeNewsModel instance) =>
    <String, dynamic>{'total': instance.total, 'items': instance.items};

RawNewsModel _$RawNewsModelFromJson(Map<String, dynamic> json) => RawNewsModel(
  sliderImg: json['slider_img']?.toString(),
  content: json['content']?.toString(),
  title: json['title']?.toString(),
  isShowButton: bool.tryParse(json['is_show_button'].toString()) ?? false,
  newsAction: json['action'] == null || json['action'] is! Map
      ? null
      : NewsActionModel.fromJson(json['action'] as Map<String, dynamic>),
);

Map<String, dynamic> _$RawNewsModelToJson(RawNewsModel instance) =>
    <String, dynamic>{
      'slider_img': instance.sliderImg,
      'content': instance.content,
      'title': instance.title,
      'is_show_button': instance.isShowButton,
      'action': instance.newsAction,
    };

NewsContentResponseModel _$NewsContentResponseModelFromJson(
  Map<String, dynamic> json,
) => NewsContentResponseModel(
  status: double.tryParse(json['status'].toString())?.toInt(),
  errorCode: double.tryParse(json['error_code'].toString())?.toInt(),
  errorMessage: json['error_message']?.toString(),
  rawNewsModel: json['data'] == null || json['data'] is! Map
      ? null
      : RawNewsModel.fromJson(json['data'] as Map<String, dynamic>),
);

Map<String, dynamic> _$NewsContentResponseModelToJson(
  NewsContentResponseModel instance,
) => <String, dynamic>{
  'status': instance.status,
  'error_code': instance.errorCode,
  'error_message': instance.errorMessage,
  'data': instance.rawNewsModel,
};

NewsItemModel _$NewsItemModelFromJson(Map<String, dynamic> json) =>
    NewsItemModel(
      id: json['id']?.toString(),
      image: json['img']?.toString(),
      title: json['title']?.toString(),
      href: json['href']?.toString(),
      newsAction: json['action'] == null || json['action'] is! Map
          ? null
          : NewsActionModel.fromJson(json['action'] as Map<String, dynamic>),
      isShowButton: bool.tryParse(json['is_show_button'].toString()) ?? false,
    );

Map<String, dynamic> _$NewsItemModelToJson(NewsItemModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'img': instance.image,
      'title': instance.title,
      'href': instance.href,
      'is_show_button': instance.isShowButton,
      'action': instance.newsAction,
    };

HomeNotificationsModel _$HomeNotificationsModelFromJson(
  Map<String, dynamic> json,
) => HomeNotificationsModel(
  newNotify: double.tryParse(json['new_notify'].toString())?.toInt(),
  newMessage: double.tryParse(json['new_message'].toString())?.toInt(),
);

Map<String, dynamic> _$HomeNotificationsModelToJson(
  HomeNotificationsModel instance,
) => <String, dynamic>{
  'new_notify': instance.newNotify,
  'new_message': instance.newMessage,
};

NewsActionModel _$NewsActionModelFromJson(Map<String, dynamic> json) =>
    NewsActionModel(
      route: json['route']?.toString(),
      value: json['value']?.toString(),
      buttonTitle: json['button_title']?.toString() ?? '',
      type: json['type']?.toString() ?? '',
      applyConditions: json['apply_conditions'] as List<dynamic> ?? [],
    );

Map<String, dynamic> _$NewsActionModelToJson(NewsActionModel instance) =>
    <String, dynamic>{
      'route': instance.route,
      'value': instance.value,
      'button_title': instance.buttonTitle,
      'type': instance.type,
      'apply_conditions': instance.applyConditions,
    };

ExpertModel _$ExpertModelFromJson(Map<String, dynamic> json) => ExpertModel(
  id: json['id']?.toString(),
  avatar: json['avatar']?.toString(),
  name: json['name']?.toString(),
);

Map<String, dynamic> _$ExpertModelToJson(ExpertModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'avatar': instance.avatar,
      'name': instance.name,
    };

VideoModel _$VideoModelFromJson(Map<String, dynamic> json) => VideoModel(
  id: json['id']?.toString(),
  thumbnail: json['thumbnail']?.toString(),
);

Map<String, dynamic> _$VideoModelToJson(VideoModel instance) =>
    <String, dynamic>{'id': instance.id, 'thumbnail': instance.thumbnail};
