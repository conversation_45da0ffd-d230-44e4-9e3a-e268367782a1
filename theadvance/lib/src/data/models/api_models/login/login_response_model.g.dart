// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'login_response_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class UserPermissionAdapter extends TypeAdapter<UserPermission> {
  @override
  final int typeId = 164;

  @override
  UserPermission read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return UserPermission(
      scanMealQRCode: fields[0] as bool?,
      screenCapture: fields[1] as bool?,
    );
  }

  @override
  void write(BinaryWriter writer, UserPermission obj) {
    writer
      ..writeByte(2)
      ..writeByte(0)
      ..write(obj.scanMealQRCode)
      ..writeByte(1)
      ..write(obj.screenCapture);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserPermissionAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LoginResponseModel _$LoginResponseModelFromJson(Map<String, dynamic> json) =>
    LoginResponseModel(
      errorCode: double.tryParse(json['code'].toString())?.toInt(),
      data: json['data'] == null || json['data'] is! Map
          ? null
          : LoginModel.fromJson(json['data'] as Map<String, dynamic>),
      errorMessage: json['message']?.toString(),
    );

Map<String, dynamic> _$LoginResponseModelToJson(LoginResponseModel instance) =>
    <String, dynamic>{
      'code': instance.errorCode,
      'message': instance.errorMessage,
      'data': instance.data,
    };

LoginModel _$LoginModelFromJson(Map<String, dynamic> json) => LoginModel(
  accessToken: json['access_token']?.toString(),
  profile: json['profile'] == null || json['profile'] is! Map
      ? null
      : UserModel.fromJson(json['profile'] as Map<String, dynamic>),
  permission: json['permission'] == null || json['permission'] is! Map
      ? null
      : UserPermission.fromJson(json['permission'] as Map<String, dynamic>),
);

Map<String, dynamic> _$LoginModelToJson(LoginModel instance) =>
    <String, dynamic>{
      'access_token': instance.accessToken,
      'profile': instance.profile,
      'permission': instance.permission,
    };

UserPermission _$UserPermissionFromJson(Map<String, dynamic> json) =>
    UserPermission(
      scanMealQRCode: bool.tryParse(json['scanMealQRCode'].toString()),
      screenCapture: bool.tryParse(json['screenCapture'].toString()),
    );

Map<String, dynamic> _$UserPermissionToJson(UserPermission instance) =>
    <String, dynamic>{
      'scanMealQRCode': instance.scanMealQRCode,
      'screenCapture': instance.screenCapture,
    };
