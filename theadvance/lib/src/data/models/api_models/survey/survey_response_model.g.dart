// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'survey_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SurveyResponseModel _$SurveyResponseModelFromJson(Map<String, dynamic> json) =>
    SurveyResponseModel(
      status: double.tryParse(json['status'].toString())?.toInt(),
      errorCode: double.tryParse(json['error_code'].toString())?.toInt(),
      errorMessage: json['error_message']?.toString(),
      listSurvey: (json['data'] is List)
          ? (json['data'] as List<dynamic>?)
                ?.map(
                  (e) => e == null || e is! Map
                      ? null
                      : SurveyModel.fromJson(e as Map<String, dynamic>),
                )
                .toList()
          : [],
    );

Map<String, dynamic> _$SurveyResponseModelToJson(
  SurveyResponseModel instance,
) => <String, dynamic>{
  'status': instance.status,
  'error_code': instance.errorCode,
  'error_message': instance.errorMessage,
  'data': instance.listSurvey,
};

SurveySubmitResponseModel _$SurveySubmitResponseModelFromJson(
  Map<String, dynamic> json,
) => SurveySubmitResponseModel(
  status: double.tryParse(json['status'].toString())?.toInt(),
  errorCode: double.tryParse(json['error_code'].toString())?.toInt(),
  errorMessage: json['error_message']?.toString(),
  surveySubmitModel: json['data'] == null || json['data'] is! Map
      ? null
      : SurveySubmitModel.fromJson(json['data'] as Map<String, dynamic>),
);

Map<String, dynamic> _$SurveySubmitResponseModelToJson(
  SurveySubmitResponseModel instance,
) => <String, dynamic>{
  'status': instance.status,
  'error_code': instance.errorCode,
  'error_message': instance.errorMessage,
  'data': instance.surveySubmitModel,
};

SurveySubmitModel _$SurveySubmitModelFromJson(Map<String, dynamic> json) =>
    SurveySubmitModel(
      status: json['status']?.toString(),
      title: json['title']?.toString(),
      body: json['body']?.toString(),
      action: json['action']?.toString(),
    );

Map<String, dynamic> _$SurveySubmitModelToJson(SurveySubmitModel instance) =>
    <String, dynamic>{
      'status': instance.status,
      'title': instance.title,
      'body': instance.body,
      'action': instance.action,
    };

SurveyModel _$SurveyModelFromJson(Map<String, dynamic> json) =>
    SurveyModel(id: json['id']?.toString(), title: json['title']?.toString());

Map<String, dynamic> _$SurveyModelToJson(SurveyModel instance) =>
    <String, dynamic>{'id': instance.id, 'title': instance.title};
