// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'upload_file_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UploadFileResponseModel _$UploadFileResponseModelFromJson(
  Map<String, dynamic> json,
) => UploadFileResponseModel(
  status: double.tryParse(json['status'].toString())?.toInt(),
  message: json['message']?.toString(),
  errorCode: double.tryParse(json['error_code'].toString())?.toInt(),
  data: (json['data'] is List)
      ? (json['data'] as List<dynamic>?)
            ?.map((e) => UploadFileModel.fromJson(e as Map<String, dynamic>))
            .toList()
      : [],
);

Map<String, dynamic> _$UploadFileResponseModelToJson(
  UploadFileResponseModel instance,
) => <String, dynamic>{
  'error_code': instance.errorCode,
  'status': instance.status,
  'message': instance.message,
  'data': instance.data,
};

UploadFileModel _$UploadFileModelFromJson(Map<String, dynamic> json) =>
    UploadFileModel(id: json['id']?.toString(), url: json['url']?.toString());

Map<String, dynamic> _$UploadFileModelToJson(UploadFileModel instance) =>
    <String, dynamic>{'id': instance.id, 'url': instance.url};
