// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tracking_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TrackingResponseModel _$TrackingResponseModelFromJson(
  Map<String, dynamic> json,
) => TrackingResponseModel(
  status: double.tryParse(json['status'].toString())?.toInt(),
  errorCode: double.tryParse(json['error_code'].toString())?.toInt(),
  errorMessage: json['error_message']?.toString(),
);

Map<String, dynamic> _$TrackingResponseModelToJson(
  TrackingResponseModel instance,
) => <String, dynamic>{
  'status': instance.status,
  'error_code': instance.errorCode,
  'error_message': instance.errorMessage,
};

TrackingModel _$TrackingModelFromJson(Map<String, dynamic> json) =>
    TrackingModel(
      trackingNumber: json['tracking_number']?.toString(),
      trackingTime: (json['tracking_time'] is List)
          ? (json['tracking_time'] as List<dynamic>?)
                ?.map((e) => e as String)
                .toList()
          : [],
      event: json['event']?.toString(),
      phone: json['phone']?.toString(),
      appVersion: json['app_version']?.toString(),
      id: json['id']?.toString(),
      firstTrackingTime: json['first_tracking_time']?.toString(),
      lastestTrackingTime: json['lastest_tracking_time']?.toString(),
    );

Map<String, dynamic> _$TrackingModelToJson(TrackingModel instance) =>
    <String, dynamic>{
      'tracking_number': instance.trackingNumber,
      'tracking_time': instance.trackingTime,
      'event': instance.event,
      'phone': instance.phone,
      'app_version': instance.appVersion,
      'id': instance.id,
      'first_tracking_time': instance.firstTrackingTime,
      'lastest_tracking_time': instance.lastestTrackingTime,
    };
