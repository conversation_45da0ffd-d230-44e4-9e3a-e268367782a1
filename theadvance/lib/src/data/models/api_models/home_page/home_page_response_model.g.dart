// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'home_page_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

HomePageResponseModel _$HomePageResponseModelFromJson(
  Map<String, dynamic> json,
) => HomePageResponseModel(
  errorCode: double.tryParse(json['error_code'].toString())?.toInt(),
  errorMessage: json['error_message']?.toString(),
  data: json['data'] == null || json['data'] is! Map
      ? null
      : HomePageModel.fromJson(json['data'] as Map<String, dynamic>),
);

Map<String, dynamic> _$HomePageResponseModelToJson(
  HomePageResponseModel instance,
) => <String, dynamic>{
  'error_code': instance.errorCode,
  'error_message': instance.errorMessage,
  'data': instance.data,
};

HomePageModel _$HomePageModelFromJson(
  Map<String, dynamic> json,
) => HomePageModel(
  notifications: json['notifications'] == null || json['notifications'] is! Map
      ? null
      : HomeNotificationsModel.fromJson(
          json['notifications'] as Map<String, dynamic>,
        ),
  checkInState: json['checkin_state'] == null || json['checkin_state'] is! Map
      ? null
      : CheckInState.fromJson(json['checkin_state'] as Map<String, dynamic>),
  sections:
      ((json['sections'] is List)
          ? (json['sections'] as List<dynamic>?)
                ?.map((e) => HomeSection.fromJson(e as Map<String, dynamic>))
                .toList()
          : []) ??
      [],
);

Map<String, dynamic> _$HomePageModelToJson(HomePageModel instance) =>
    <String, dynamic>{
      'notifications': instance.notifications,
      'checkin_state': instance.checkInState,
      'sections': instance.sections,
    };

CheckInState _$CheckInStateFromJson(Map<String, dynamic> json) => CheckInState(
  background: json['background']?.toString(),
  status: bool.tryParse(json['status'].toString()) ?? false,
  time: json['time']?.toString(),
);

Map<String, dynamic> _$CheckInStateToJson(CheckInState instance) =>
    <String, dynamic>{
      'status': instance.status,
      'time': instance.time,
      'background': instance.background,
    };

HomeSection _$HomeSectionFromJson(Map<String, dynamic> json) => HomeSection(
  name: json['name']?.toString(),
  data: (json['data'] is List)
      ? (json['data'] as List<dynamic>?)
            ?.map((e) => HomeSectionData.fromJson(e as Map<String, dynamic>))
            .toList()
      : [],
  icon: json['icon']?.toString(),
);

Map<String, dynamic> _$HomeSectionToJson(HomeSection instance) =>
    <String, dynamic>{
      'name': instance.name,
      'icon': instance.icon,
      'data': instance.data,
    };

HomeSectionData _$HomeSectionDataFromJson(Map<String, dynamic> json) =>
    HomeSectionData(
      id: json['id']?.toString(),
      title: json['title']?.toString(),
      subTitle: json['sub_title']?.toString(),
      extraInfo: json['extra_info']?.toString(),
      image: json['image']?.toString(),
      action: json['action'] == null || json['action'] is! Map
          ? null
          : HomePopupActionModel.fromJson(
              json['action'] as Map<String, dynamic>,
            ),
    );

Map<String, dynamic> _$HomeSectionDataToJson(HomeSectionData instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'sub_title': instance.subTitle,
      'extra_info': instance.extraInfo,
      'image': instance.image,
      'action': instance.action,
    };
