// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'home_popup_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

HomePopupResponseModel _$HomePopupResponseModelFromJson(
  Map<String, dynamic> json,
) => HomePopupResponseModel(
  status: double.tryParse(json['status'].toString())?.toInt(),
  errorCode: double.tryParse(json['error_code'].toString())?.toInt(),
  errorMessage: json['error_message']?.toString(),
  listHomePopup: (json['data'] is List)
      ? (json['data'] as List<dynamic>?)
            ?.map((e) => HomePopupModel.fromJson(e as Map<String, dynamic>))
            .toList()
      : [],
);

Map<String, dynamic> _$HomePopupResponseModelToJson(
  HomePopupResponseModel instance,
) => <String, dynamic>{
  'status': instance.status,
  'error_code': instance.errorCode,
  'error_message': instance.errorMessage,
  'data': instance.listHomePopup,
};

HomePopupModel _$HomePopupModelFromJson(Map<String, dynamic> json) =>
    HomePopupModel(
      action: json['action'] == null || json['action'] is! Map
          ? null
          : HomePopupActionModel.fromJson(
              json['action'] as Map<String, dynamic>,
            ),
      active: bool.tryParse(json['active'].toString()),
      id: json['id']?.toString(),
      name: json['name']?.toString(),
      content: json['content']?.toString(),
      imageUrl: json['image_url']?.toString(),
      sort: double.tryParse(json['sort'].toString())?.toInt(),
      homePopupDataId: json['homePopupDataId']?.toString(),
      title: json['title']?.toString(),
    );

Map<String, dynamic> _$HomePopupModelToJson(HomePopupModel instance) =>
    <String, dynamic>{
      'action': instance.action,
      'active': instance.active,
      'id': instance.id,
      'title': instance.title,
      'name': instance.name,
      'content': instance.content,
      'image_url': instance.imageUrl,
      'sort': instance.sort,
      'homePopupDataId': instance.homePopupDataId,
    };

HomePopupActionModel _$HomePopupActionModelFromJson(
  Map<String, dynamic> json,
) => HomePopupActionModel(
  active: bool.tryParse(json['active'].toString()) ?? false,
  type: json['type']?.toString(),
  icon: json['icon']?.toString(),
  title: json['title']?.toString(),
  value: json['value']?.toString(),
  screen: json['screen']?.toString(),
);

Map<String, dynamic> _$HomePopupActionModelToJson(
  HomePopupActionModel instance,
) => <String, dynamic>{
  'active': instance.active,
  'type': instance.type,
  'icon': instance.icon,
  'title': instance.title,
  'value': instance.value,
  'screen': instance.screen,
};
