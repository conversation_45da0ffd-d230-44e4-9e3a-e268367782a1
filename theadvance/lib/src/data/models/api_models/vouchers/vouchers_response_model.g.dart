// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'vouchers_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

VouchersResponseModel _$VouchersResponseModelFromJson(
  Map<String, dynamic> json,
) => VouchersResponseModel(
  errorCode: double.tryParse(json['error_code'].toString())?.toInt(),
  errorMessage: json['error_message']?.toString(),
  listVoucher:
      ((json['data'] is List)
          ? (json['data'] as List<dynamic>?)
                ?.map((e) => VoucherModel.fromJson(e as Map<String, dynamic>))
                .toList()
          : []) ??
      [],
);

Map<String, dynamic> _$VouchersResponseModelToJson(
  VouchersResponseModel instance,
) => <String, dynamic>{
  'error_code': instance.errorCode,
  'error_message': instance.errorMessage,
  'data': instance.listVoucher,
};

VoucherModel _$VoucherModelFromJson(Map<String, dynamic> json) => VoucherModel(
  source: json['source']?.toString(),
  id: json['id']?.toString(),
  code: json['code']?.toString(),
  title: json['title']?.toString(),
  masterCode: json['master_code']?.toString(),
  descriptionHtml: json['description_html']?.toString(),
  eventId: double.tryParse(json['event_id'].toString())?.toInt(),
  imageSource: json['image_source']?.toString(),
  image: json['image']?.toString(),
  expiredDate: json['expired_date']?.toString(),
  expired: bool.tryParse(json['expired'].toString()),
  used: bool.tryParse(json['used'].toString()),
  amount: double.tryParse(json['amount'].toString())?.toInt(),
  percent: json['percent']?.toString(),
  qrCode: json['qr_code']?.toString(),
  category: json['category']?.toString(),
  type: json['type']?.toString(),
  isShowButton: bool.tryParse(json['is_show_button'].toString()),
  buttonText: json['buton_text']?.toString(),
  buttonType: json['button_type']?.toString() ?? '',
  buttonValue: json['button_value']?.toString() ?? '',
  receivedTime: json['received_time']?.toString(),
  receivedTimeText: json['received_time_text']?.toString(),
);

Map<String, dynamic> _$VoucherModelToJson(VoucherModel instance) =>
    <String, dynamic>{
      'source': instance.source,
      'id': instance.id,
      'code': instance.code,
      'title': instance.title,
      'master_code': instance.masterCode,
      'description_html': instance.descriptionHtml,
      'event_id': instance.eventId,
      'image_source': instance.imageSource,
      'image': instance.image,
      'expired_date': instance.expiredDate,
      'expired': instance.expired,
      'used': instance.used,
      'amount': instance.amount,
      'percent': instance.percent,
      'qr_code': instance.qrCode,
      'category': instance.category,
      'type': instance.type,
      'is_show_button': instance.isShowButton,
      'buton_text': instance.buttonText,
      'button_value': instance.buttonValue,
      'button_type': instance.buttonType,
      'received_time': instance.receivedTime,
      'received_time_text': instance.receivedTimeText,
    };
