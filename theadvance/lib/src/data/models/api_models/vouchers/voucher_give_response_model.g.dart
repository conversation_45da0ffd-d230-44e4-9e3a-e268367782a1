// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'voucher_give_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GiveVoucherResponseModel _$GiveVoucherResponseModelFromJson(
  Map<String, dynamic> json,
) => GiveVoucherResponseModel(
  status: double.tryParse(json['status'].toString())?.toInt(),
  errorCode: double.tryParse(json['error_code'].toString())?.toInt(),
  errorMessage: json['error_message']?.toString(),
  listGiveVoucher: (json['data'] is List)
      ? (json['data'] as List<dynamic>?)
            ?.map((e) => GiveVoucherModel.fromJson(e as Map<String, dynamic>))
            .toList()
      : [],
);

Map<String, dynamic> _$GiveVoucherResponseModelToJson(
  GiveVoucherResponseModel instance,
) => <String, dynamic>{
  'status': instance.status,
  'error_code': instance.errorCode,
  'error_message': instance.errorMessage,
  'data': instance.listGiveVoucher,
};

GiveVoucherModel _$GiveVoucherModelFromJson(Map<String, dynamic> json) =>
    GiveVoucherModel(
      code: json['code']?.toString(),
      masterCode: json['master_code']?.toString(),
      cusPhone: json['cus_phone']?.toString(),
      cusCode: json['cus_code']?.toString(),
      cusPhoneUsing: json['cus_phone_using']?.toString(),
      partner: json['partner']?.toString(),
      actionType: double.tryParse(json['action_type'].toString())?.toInt(),
      id: json['_id']?.toString(),
      note: json['note']?.toString(),
      status: json['status']?.toString(),
      createdBy: json['created_by']?.toString(),
      updatedTime: double.tryParse(json['updated_time'].toString())?.toInt(),
      createdTime: double.tryParse(json['created_time'].toString())?.toInt(),
      cusUsingCurrent: double.tryParse(
        json['cus_using_current'].toString(),
      )?.toInt(),
      cusUsingTotal: double.tryParse(
        json['cus_using_total'].toString(),
      )?.toInt(),
      eventId: double.tryParse(json['event_id'].toString())?.toInt(),
      giveVoucherId: json['give_voucher_id']?.toString(),
      v: double.tryParse(json['__v'].toString())?.toInt(),
      cusFullname: json['cus_fullname']?.toString(),
      receivedTime: double.tryParse(json['received_time'].toString())?.toInt(),
      updatedBy: json['updated_by']?.toString(),
    );

Map<String, dynamic> _$GiveVoucherModelToJson(GiveVoucherModel instance) =>
    <String, dynamic>{
      'code': instance.code,
      'master_code': instance.masterCode,
      'cus_phone': instance.cusPhone,
      'cus_code': instance.cusCode,
      'cus_phone_using': instance.cusPhoneUsing,
      'partner': instance.partner,
      'action_type': instance.actionType,
      '_id': instance.id,
      'note': instance.note,
      'status': instance.status,
      'created_by': instance.createdBy,
      'updated_time': instance.updatedTime,
      'created_time': instance.createdTime,
      'cus_using_current': instance.cusUsingCurrent,
      'cus_using_total': instance.cusUsingTotal,
      'event_id': instance.eventId,
      'give_voucher_id': instance.giveVoucherId,
      '__v': instance.v,
      'cus_fullname': instance.cusFullname,
      'received_time': instance.receivedTime,
      'updated_by': instance.updatedBy,
    };
