// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'voucher_details_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

VoucherDetailResponseModel _$VoucherDetailResponseModelFromJson(
  Map<String, dynamic> json,
) => VoucherDetailResponseModel(
  errorCode: double.tryParse(json['error_code'].toString())?.toInt(),
  errorMessage: json['error_message']?.toString(),
  voucherDetail: json['data'] == null || json['data'] is! Map
      ? null
      : VoucherDetailModel.fromJson(json['data'] as Map<String, dynamic>),
);

Map<String, dynamic> _$VoucherDetailResponseModelToJson(
  VoucherDetailResponseModel instance,
) => <String, dynamic>{
  'error_code': instance.errorCode,
  'error_message': instance.errorMessage,
  'data': instance.voucherDetail,
};

VoucherDetailModel _$VoucherDetailModelFromJson(Map<String, dynamic> json) =>
    VoucherDetailModel(
      id: json['id']?.toString(),
      title: json['title']?.toString(),
      expiredDate: json['expired_date']?.toString(),
      expired: bool.tryParse(json['expired'].toString()) ?? false,
      used: bool.tryParse(json['used'].toString()) ?? false,
      imageSource: json['image_source']?.toString(),
      count: double.tryParse(json['count'].toString())?.toInt(),
      qrCode: json['qr_code']?.toString() ?? '',
      code: json['code']?.toString() ?? '',
      descriptionHtml: json['description_html']?.toString(),
      isShowButton: bool.tryParse(json['is_show_button'].toString()) ?? false,
      buttonText: json['buton_text']?.toString() ?? '',
    );

Map<String, dynamic> _$VoucherDetailModelToJson(VoucherDetailModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'expired_date': instance.expiredDate,
      'expired': instance.expired,
      'used': instance.used,
      'image_source': instance.imageSource,
      'count': instance.count,
      'qr_code': instance.qrCode,
      'code': instance.code,
      'description_html': instance.descriptionHtml,
      'is_show_button': instance.isShowButton,
      'buton_text': instance.buttonText,
    };
