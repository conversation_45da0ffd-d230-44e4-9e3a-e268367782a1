// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'asset_inventory_save_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AssetInventorySaveModel _$AssetInventorySaveModelFromJson(Map json) =>
    AssetInventorySaveModel(
      organizationID: json['OrganizationID']?.toString(),
      accountLogin: json['AccountLogin']?.toString(),
      functionID: json['FunctionID']?.toString(),
      masterRowKey: double.tryParse(json['MasterRowKey'].toString())?.toInt(),
      employeeID: json['EmployeeID']?.toString(),
      employeeName: json['EmployeeName']?.toString(),
      acountantEmployeeID: json['AcountantEmployeeID']?.toString(),
      acountantEmployeeName: json['AcountantEmployeeName']?.toString(),
      otherEmployeeID: json['OtherEmployeeID']?.toString(),
      otherEmployeeName: json['OtherEmployeeName']?.toString(),
      assetInventoryDetails: (json['AssetInventoryDetails'] is List)
          ? (json['AssetInventoryDetails'] as List<dynamic>?)
                ?.map(
                  (e) => AssetInventoryDetailsModel.fromJson(
                    Map<String, dynamic>.from(e as Map),
                  ),
                )
                .toList()
          : [],
    );

Map<String, dynamic> _$AssetInventorySaveModelToJson(
  AssetInventorySaveModel instance,
) => <String, dynamic>{
  'OrganizationID': instance.organizationID,
  'AccountLogin': instance.accountLogin,
  'FunctionID': instance.functionID,
  'MasterRowKey': instance.masterRowKey,
  'EmployeeID': instance.employeeID,
  'EmployeeName': instance.employeeName,
  'AcountantEmployeeID': instance.acountantEmployeeID,
  'AcountantEmployeeName': instance.acountantEmployeeName,
  'OtherEmployeeID': instance.otherEmployeeID,
  'OtherEmployeeName': instance.otherEmployeeName,
  'AssetInventoryDetails': instance.assetInventoryDetails,
};

AssetInventoryDetailsModel _$AssetInventoryDetailsModelFromJson(Map json) =>
    AssetInventoryDetailsModel(
      rowKey: double.tryParse(json['RowKey'].toString())?.toInt(),
      assetID: json['AssetID']?.toString(),
      status: double.tryParse(json['Status'].toString())?.toInt(),
      notes: json['Notes']?.toString(),
    );

Map<String, dynamic> _$AssetInventoryDetailsModelToJson(
  AssetInventoryDetailsModel instance,
) => <String, dynamic>{
  'RowKey': instance.rowKey,
  'AssetID': instance.assetID,
  'Notes': instance.notes,
  'Status': instance.status,
};
