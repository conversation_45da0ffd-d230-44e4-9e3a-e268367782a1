// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'inventory_ticket_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

InventoryTicketResponseModel _$InventoryTicketResponseModelFromJson(Map json) =>
    InventoryTicketResponseModel(
        data: json['data'] == null || json['data'] is! Map
            ? null
            : InventoryTicketModel.fromJson(
                Map<String, dynamic>.from(json['data'] as Map),
              ),
      )
      ..status = double.tryParse(json['status'].toString())?.toInt()
      ..errorCode = double.tryParse(json['code'].toString())?.toInt()
      ..errorMessage = json['message']?.toString();

Map<String, dynamic> _$InventoryTicketResponseModelToJson(
  InventoryTicketResponseModel instance,
) => <String, dynamic>{
  'status': instance.status,
  'code': instance.errorCode,
  'message': instance.errorMessage,
  'data': instance.data,
};

InventoryTicketModel _$InventoryTicketModelFromJson(
  Map<String, dynamic> json,
) =>
    InventoryTicketModel(
        totalRowItems: double.tryParse(
          json['TotalRowItems'].toString(),
        )?.toInt(),
        childItems: json['ChildItems'],
        items:
            ((json['Items'] is List)
                ? (json['Items'] as List<dynamic>?)
                      ?.map(
                        (e) => ResultsModel.fromJson(e as Map<String, dynamic>),
                      )
                      .toList()
                : []) ??
            [],
        totalRow: double.tryParse(json['TotalRow'].toString())?.toInt(),
        results:
            ((json['Results'] is List)
                ? (json['Results'] as List<dynamic>?)
                      ?.map(
                        (e) => ResultsModel.fromJson(e as Map<String, dynamic>),
                      )
                      .toList()
                : []) ??
            [],
      )
      ..errorCode = double.tryParse(json['error_code'].toString())?.toInt()
      ..errorMessage = json['error_message']?.toString()
      ..success = bool.tryParse(json['success'].toString())
      ..message = json['message']?.toString()
      ..data = double.tryParse(json['data'].toString())?.toInt()
      ..masterRowKey = json['MasterRowKey']?.toString();

Map<String, dynamic> _$InventoryTicketModelToJson(
  InventoryTicketModel instance,
) => <String, dynamic>{
  'error_code': instance.errorCode,
  'error_message': instance.errorMessage,
  'Items': instance.items,
  'success': instance.success,
  'message': instance.message,
  'TotalRow': instance.totalRow,
  'TotalRowItems': instance.totalRowItems,
  'Results': instance.results,
  'ChildItems': instance.childItems,
  'data': instance.data,
  'MasterRowKey': instance.masterRowKey,
};

InventoryTicketItemsModel _$InventoryTicketItemsModelFromJson(Map json) =>
    InventoryTicketItemsModel(
      assetID: json['AssetID']?.toString(),
      assetName: json['AssetName']?.toString(),
      departmentID: json['DepartmentID']?.toString(),
      departmentName: json['DepartmentName']?.toString(),
      employeeReceivedAssetID: json['EmployeeReceivedAssetID']?.toString(),
      employeeReceivedAssetName: json['EmployeeReceivedAssetName']?.toString(),
    );

Map<String, dynamic> _$InventoryTicketItemsModelToJson(
  InventoryTicketItemsModel instance,
) => <String, dynamic>{
  'AssetID': instance.assetID,
  'AssetName': instance.assetName,
  'DepartmentID': instance.departmentID,
  'DepartmentName': instance.departmentName,
  'EmployeeReceivedAssetID': instance.employeeReceivedAssetID,
  'EmployeeReceivedAssetName': instance.employeeReceivedAssetName,
};

ResultsModel _$ResultsModelFromJson(Map json) =>
    ResultsModel(
        assetID: json['AssetID']?.toString(),
        assetRowkey: json['AssetRowkey']?.toString(),
        assetName: json['Assetname']?.toString(),
        transactionNo: json['TransactionNo']?.toString(),
        rowKey: double.tryParse(json['RowKey'].toString())?.toInt(),
        employeeID: json['EmployeeID']?.toString(),
        employeeName: json['EmployeeName']?.toString(),
        employeeAccountantID: json['AcountantEmployeeID']?.toString(),
        employeeAccountantName: json['AcountantEmployeeName']?.toString(),
        employeeOrtherID: json['OtherEmployeeID']?.toString(),
        employeeOrtherName: json['OtherEmployeeName']?.toString(),
        departmentID: json['DepartmentID']?.toString(),
        departmentName: json['DepartmentName']?.toString(),
        departmentInventoryID: json['InventoryDepartmentID']?.toString(),
        inventoryAssetEmployeeID: json['InventoryAssetEmployeeID']?.toString(),
        inventoryAssetEmployeeName: json['InventoryAssetEmployeeName']
            ?.toString(),
        notes: json['Notes']?.toString(),
        status: bool.tryParse(json['Status'].toString()),
        inventoryType: json['InventoryType']?.toString(),
        scanType: json['ScanType']?.toString() ?? 'Scan',
      )
      ..departmentInventoryName = json['InventoryDepartmentName']?.toString()
      ..masterRowKey = double.tryParse(
        json['MasterRowKey'].toString(),
      )?.toInt();

Map<String, dynamic> _$ResultsModelToJson(ResultsModel instance) =>
    <String, dynamic>{
      'AssetID': instance.assetID,
      'AssetRowkey': instance.assetRowkey,
      'Assetname': instance.assetName,
      'TransactionNo': instance.transactionNo,
      'RowKey': instance.rowKey,
      'EmployeeID': instance.employeeID,
      'EmployeeName': instance.employeeName,
      'AcountantEmployeeID': instance.employeeAccountantID,
      'AcountantEmployeeName': instance.employeeAccountantName,
      'OtherEmployeeID': instance.employeeOrtherID,
      'OtherEmployeeName': instance.employeeOrtherName,
      'DepartmentID': instance.departmentID,
      'DepartmentName': instance.departmentName,
      'InventoryDepartmentID': instance.departmentInventoryID,
      'InventoryDepartmentName': instance.departmentInventoryName,
      'InventoryAssetEmployeeID': instance.inventoryAssetEmployeeID,
      'InventoryAssetEmployeeName': instance.inventoryAssetEmployeeName,
      'Notes': instance.notes,
      'Status': instance.status,
      'InventoryType': instance.inventoryType,
      'ScanType': instance.scanType,
      'MasterRowKey': instance.masterRowKey,
    };
