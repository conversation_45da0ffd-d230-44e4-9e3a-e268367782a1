// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'salary_detail_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SalaryDetailResponseModel _$SalaryDetailResponseModelFromJson(
  Map<String, dynamic> json,
) => SalaryDetailResponseModel(
  status: double.tryParse(json['status'].toString())?.toInt(),
  errorCode: double.tryParse(json['error_code'].toString())?.toInt(),
  errorMessage: json['error_message']?.toString(),
  data:
      ((json['data'] is List)
          ? (json['data'] as List<dynamic>?)
                ?.map(
                  (e) => SalaryMonthDetailModel.fromJson(
                    e as Map<String, dynamic>,
                  ),
                )
                .toList()
          : []) ??
      [],
);

Map<String, dynamic> _$SalaryDetailResponseModelToJson(
  SalaryDetailResponseModel instance,
) => <String, dynamic>{
  'status': instance.status,
  'error_code': instance.errorCode,
  'error_message': instance.errorMessage,
  'data': instance.data,
};

SalaryMonthDetailModel _$SalaryMonthDetailModelFromJson(
  Map<String, dynamic> json,
) => SalaryMonthDetailModel(
  month: double.tryParse(json['month'].toString())?.toInt(),
  data:
      ((json['data'] is List)
          ? (json['data'] as List<dynamic>?)
                ?.map(
                  (e) =>
                      SalaryDayDetailModel.fromJson(e as Map<String, dynamic>),
                )
                .toList()
          : []) ??
      [],
);

Map<String, dynamic> _$SalaryMonthDetailModelToJson(
  SalaryMonthDetailModel instance,
) => <String, dynamic>{'month': instance.month, 'data': instance.data};

SalaryDayDetailModel _$SalaryDayDetailModelFromJson(
  Map<String, dynamic> json,
) => SalaryDayDetailModel(
  date: json['date']?.toString(),
  data: json['data'] == null || json['data'] is! Map
      ? null
      : SalaryDayModel.fromJson(json['data'] as Map<String, dynamic>),
);

Map<String, dynamic> _$SalaryDayDetailModelToJson(
  SalaryDayDetailModel instance,
) => <String, dynamic>{'date': instance.date, 'data': instance.data};

SalaryDayModel _$SalaryDayModelFromJson(Map<String, dynamic> json) =>
    SalaryDayModel(
      totalWork: double.tryParse(json['total_work'].toString()),
      checkIn: json['check_in']?.toString(),
      checkOut: json['check_out']?.toString(),
      updateInfo: json['update_info'] == null || json['update_info'] is! Map
          ? null
          : UpdateInfoModel.fromJson(
              json['update_info'] as Map<String, dynamic>,
            ),
    );

Map<String, dynamic> _$SalaryDayModelToJson(SalaryDayModel instance) =>
    <String, dynamic>{
      'total_work': instance.totalWork,
      'check_in': instance.checkIn,
      'check_out': instance.checkOut,
      'update_info': instance.updateInfo,
    };

UpdateInfoModel _$UpdateInfoModelFromJson(Map<String, dynamic> json) =>
    UpdateInfoModel(
      workType: json['work_type']?.toString(),
      fromDate: json['from_date']?.toString(),
      toDate: json['to_date']?.toString(),
      agency: json['agency']?.toString(),
      totalWork: double.tryParse(json['total_work'].toString()),
      note: json['note']?.toString(),
    );

Map<String, dynamic> _$UpdateInfoModelToJson(UpdateInfoModel instance) =>
    <String, dynamic>{
      'work_type': instance.workType,
      'from_date': instance.fromDate,
      'to_date': instance.toDate,
      'agency': instance.agency,
      'total_work': instance.totalWork,
      'note': instance.note,
    };

ChoicesResponseModel _$ChoicesResponseModelFromJson(
  Map<String, dynamic> json,
) => ChoicesResponseModel(
  status: double.tryParse(json['status'].toString())?.toInt(),
  errorCode: double.tryParse(json['error_code'].toString())?.toInt(),
  errorMessage: json['error_message']?.toString(),
  data: json['data'] == null || json['data'] is! Map
      ? null
      : ChoicesModel.fromJson(json['data'] as Map<String, dynamic>),
);

Map<String, dynamic> _$ChoicesResponseModelToJson(
  ChoicesResponseModel instance,
) => <String, dynamic>{
  'status': instance.status,
  'error_code': instance.errorCode,
  'error_message': instance.errorMessage,
  'data': instance.data,
};

ChoicesModel _$ChoicesModelFromJson(Map<String, dynamic> json) => ChoicesModel(
  workTypes:
      ((json['work_types'] is List)
          ? (json['work_types'] as List<dynamic>?)
                ?.map((e) => e as String)
                .toList()
          : []) ??
      [],
  agencies:
      ((json['agencies'] is List)
          ? (json['agencies'] as List<dynamic>?)
                ?.map((e) => e as String)
                .toList()
          : []) ??
      [],
);

Map<String, dynamic> _$ChoicesModelToJson(ChoicesModel instance) =>
    <String, dynamic>{
      'work_types': instance.workTypes,
      'agencies': instance.agencies,
    };
