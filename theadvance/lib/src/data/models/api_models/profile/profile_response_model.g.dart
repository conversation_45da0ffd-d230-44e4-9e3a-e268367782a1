// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'profile_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UpdateProfileResponseModel _$UpdateProfileResponseModelFromJson(
  Map<String, dynamic> json,
) => UpdateProfileResponseModel(
  errorCode: double.tryParse(json['code'].toString())?.toInt(),
  data: json['data'] == null || json['data'] is! Map
      ? null
      : UserModel.fromJson(json['data'] as Map<String, dynamic>),
  errorMessage: json['message']?.toString(),
)..status = double.tryParse(json['status'].toString())?.toInt();

Map<String, dynamic> _$UpdateProfileResponseModelToJson(
  UpdateProfileResponseModel instance,
) => <String, dynamic>{
  'status': instance.status,
  'code': instance.errorCode,
  'message': instance.errorMessage,
  'data': instance.data,
};

ProfileResponseModel _$ProfileResponseModelFromJson(
  Map<String, dynamic> json,
) => ProfileResponseModel(
  status: double.tryParse(json['status'].toString())?.toInt(),
  code: double.tryParse(json['code'].toString())?.toInt(),
  message: json['message']?.toString(),
  data: json['data'] == null || json['data'] is! Map
      ? null
      : ProfileModel.fromJson(json['data'] as Map<String, dynamic>),
);

Map<String, dynamic> _$ProfileResponseModelToJson(
  ProfileResponseModel instance,
) => <String, dynamic>{
  'status': instance.status,
  'code': instance.code,
  'message': instance.message,
  'data': instance.data,
};

ProfileModel _$ProfileModelFromJson(Map<String, dynamic> json) => ProfileModel(
  items: json['items'] == null || json['items'] is! Map
      ? null
      : ProfileItemsModel.fromJson(json['items'] as Map<String, dynamic>),
);

Map<String, dynamic> _$ProfileModelToJson(ProfileModel instance) =>
    <String, dynamic>{'items': instance.items};

ProfileItemsModel _$ProfileItemsModelFromJson(Map<String, dynamic> json) =>
    ProfileItemsModel(
      userBundle: json['info'] == null || json['info'] is! Map
          ? null
          : UserBundleModel.fromJson(json['info'] as Map<String, dynamic>),
      asset: json['asset'] == null || json['asset'] is! Map
          ? null
          : ProfileAssetModel.fromJson(json['asset'] as Map<String, dynamic>),
      socialInsurance:
          json['social_insurance'] == null || json['social_insurance'] is! Map
          ? null
          : ProfileBundleModel.fromJson(
              json['social_insurance'] as Map<String, dynamic>,
            ),
      workingContract:
          json['working_contract'] == null || json['working_contract'] is! Map
          ? null
          : ProfileBundleModel.fromJson(
              json['working_contract'] as Map<String, dynamic>,
            ),
    );

Map<String, dynamic> _$ProfileItemsModelToJson(ProfileItemsModel instance) =>
    <String, dynamic>{
      'info': instance.userBundle,
      'asset': instance.asset,
      'social_insurance': instance.socialInsurance,
      'working_contract': instance.workingContract,
    };

UserBundleModel _$UserBundleModelFromJson(Map<String, dynamic> json) =>
    UserBundleModel(
      title: json['title']?.toString(),
      user: UserModel.fromJson(json['data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$UserBundleModelToJson(UserBundleModel instance) =>
    <String, dynamic>{'title': instance.title, 'data': instance.user};

ProfileBundleModel _$ProfileBundleModelFromJson(Map<String, dynamic> json) =>
    ProfileBundleModel(
      title: json['title']?.toString(),
      data: (json['data'] is List)
          ? (json['data'] as List<dynamic>?)
                ?.map(
                  (e) => ProfileInfoModel.fromJson(e as Map<String, dynamic>),
                )
                .toList()
          : [],
    );

Map<String, dynamic> _$ProfileBundleModelToJson(ProfileBundleModel instance) =>
    <String, dynamic>{'title': instance.title, 'data': instance.data};

ProfileAssetModel _$ProfileAssetModelFromJson(Map<String, dynamic> json) =>
    ProfileAssetModel(
      title: json['title']?.toString(),
      data: (json['data'] is List)
          ? (json['data'] as List<dynamic>?)
                ?.map((e) => AssetModel.fromJson(e as Map<String, dynamic>))
                .toList()
          : [],
    );

Map<String, dynamic> _$ProfileAssetModelToJson(ProfileAssetModel instance) =>
    <String, dynamic>{'title': instance.title, 'data': instance.data};

ProfileInfoModel _$ProfileInfoModelFromJson(Map<String, dynamic> json) =>
    ProfileInfoModel(
      title: json['title']?.toString(),
      content: json['content']?.toString(),
    );

Map<String, dynamic> _$ProfileInfoModelToJson(ProfileInfoModel instance) =>
    <String, dynamic>{'title': instance.title, 'content': instance.content};

AssetModel _$AssetModelFromJson(Map<String, dynamic> json) => AssetModel(
  title: json['title']?.toString(),
  content: json['content']?.toString(),
  assetId: json['asset_id']?.toString(),
);

Map<String, dynamic> _$AssetModelToJson(AssetModel instance) =>
    <String, dynamic>{
      'title': instance.title,
      'content': instance.content,
      'asset_id': instance.assetId,
    };
