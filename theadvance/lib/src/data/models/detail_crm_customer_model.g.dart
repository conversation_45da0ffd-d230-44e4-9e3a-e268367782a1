// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'detail_crm_customer_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DetailCrmCustomerModel _$DetailCrmCustomerModelFromJson(
  Map<String, dynamic> json,
) => DetailCrmCustomerModel(
  customerCrmId: json['customer_crm_id']?.toString(),
  customerErpId: json['customer_erp_id']?.toString(),
  customerCode: json['customer_code']?.toString(),
  customerName: json['customer_name']?.toString(),
  customerGroup: json['customer_group']?.toString(),
  firstVisit: json['first_visit'] == null
      ? null
      : DateTime.tryParse(json['first_visit'].toString()),
  lastVisit: json['last_visit'] == null
      ? null
      : DateTime.tryParse(json['last_visit'].toString()),
  branchName: json['branch_name']?.toString(),
  phone: json['phone']?.toString() ?? '',
  phone1: json['phone1']?.toString() ?? '',
  avatarUrl: json['avatar_url']?.toString(),
);

Map<String, dynamic> _$DetailCrmCustomerModelToJson(
  DetailCrmCustomerModel instance,
) => <String, dynamic>{
  'customer_crm_id': instance.customerCrmId,
  'customer_erp_id': instance.customerErpId,
  'customer_code': instance.customerCode,
  'customer_name': instance.customerName,
  'customer_group': instance.customerGroup,
  'first_visit': instance.firstVisit?.toIso8601String(),
  'last_visit': instance.lastVisit?.toIso8601String(),
  'branch_name': instance.branchName,
  'phone': instance.phone,
  'phone1': instance.phone1,
  'avatar_url': instance.avatarUrl,
};
