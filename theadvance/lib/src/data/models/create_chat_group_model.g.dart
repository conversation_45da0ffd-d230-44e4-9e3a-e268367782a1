// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'create_chat_group_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CreateChatGroupModel _$CreateChatGroupModelFromJson(
  Map<String, dynamic> json,
) => CreateChatGroupModel(
  conversation: json['conversation'] == null || json['conversation'] is! Map
      ? null
      : ChatListItemsModel.fromJson(
          json['conversation'] as Map<String, dynamic>,
        ),
);

Map<String, dynamic> _$CreateChatGroupModelToJson(
  CreateChatGroupModel instance,
) => <String, dynamic>{'conversation': instance.conversation};

CreateChatGroupItemsModel _$CreateChatGroupItemsModelFromJson(
  Map<String, dynamic> json,
) => CreateChatGroupItemsModel(
  id: json['id']?.toString(),
  name: json['name']?.toString(),
  avatar: json['avatar']?.toString(),
  phone: json['phone']?.toString(),
  userCode: json['user_code']?.toString(),
  jobTitle: json['job_title']?.toString(),
  department: json['department']?.toString(),
);

Map<String, dynamic> _$CreateChatGroupItemsModelToJson(
  CreateChatGroupItemsModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'avatar': instance.avatar,
  'phone': instance.phone,
  'user_code': instance.userCode,
  'job_title': instance.jobTitle,
  'department': instance.department,
};
