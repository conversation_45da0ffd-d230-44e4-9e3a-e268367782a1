// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'customer_profile_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class CustomerProfileModelAdapter extends TypeAdapter<CustomerProfileModel> {
  @override
  final int typeId = 122;

  @override
  CustomerProfileModel read(BinaryReader reader) {
    return CustomerProfileModel();
  }

  @override
  void write(BinaryWriter writer, CustomerProfileModel obj) {
    writer.writeByte(0);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CustomerProfileModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CustomerProfileModel _$CustomerProfileModelFromJson(
  Map<String, dynamic> json,
) => CustomerProfileModel()
  ..customerCode = json['customer_code']?.toString()
  ..customerId = json['customer_id']?.toString()
  ..group = json['group']?.toString()
  ..customerName = json['customer_name']?.toString()
  ..avatar = json['avatar']?.toString()
  ..dob = json['dob']?.toString()
  ..work = json['work']?.toString()
  ..address = json['address']?.toString()
  ..visitCount = double.tryParse(json['visit_count'].toString())?.toInt()
  ..averageRevenue = json['average_revenue']?.toString()
  ..totalRevenue = json['total_revenue']?.toString()
  ..accountBalance = json['account_balance']?.toString()
  ..paid = json['paid']?.toString()
  ..refund = json['refund']?.toString()
  ..totalDebt = json['total_debt']?.toString()
  ..totalPayment = json['total_payment']?.toString()
  ..payByCard = json['pay_by_card']?.toString()
  ..nation = json['nation']?.toString()
  ..gender = json['gender']?.toString()
  ..visitReason = json['visit_reason']?.toString()
  ..introducer = json['introducer']?.toString()
  ..firstVisit = json['first_visit']?.toString()
  ..lastVisit = json['last_visit']?.toString()
  ..specialNote = json['special_note']?.toString()
  ..interest = json['interest']?.toString()
  ..habit = json['habit']?.toString();

Map<String, dynamic> _$CustomerProfileModelToJson(
  CustomerProfileModel instance,
) => <String, dynamic>{
  'customer_code': instance.customerCode,
  'customer_id': instance.customerId,
  'group': instance.group,
  'customer_name': instance.customerName,
  'avatar': instance.avatar,
  'dob': instance.dob,
  'work': instance.work,
  'address': instance.address,
  'visit_count': instance.visitCount,
  'average_revenue': instance.averageRevenue,
  'total_revenue': instance.totalRevenue,
  'account_balance': instance.accountBalance,
  'paid': instance.paid,
  'refund': instance.refund,
  'total_debt': instance.totalDebt,
  'total_payment': instance.totalPayment,
  'pay_by_card': instance.payByCard,
  'nation': instance.nation,
  'gender': instance.gender,
  'visit_reason': instance.visitReason,
  'introducer': instance.introducer,
  'first_visit': instance.firstVisit,
  'last_visit': instance.lastVisit,
  'special_note': instance.specialNote,
  'interest': instance.interest,
  'habit': instance.habit,
};
