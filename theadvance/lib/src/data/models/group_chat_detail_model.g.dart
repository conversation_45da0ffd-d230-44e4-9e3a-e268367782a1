// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'group_chat_detail_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GroupChatDetailModel _$GroupChatDetailModelFromJson(
  Map<String, dynamic> json,
) => GroupChatDetailModel(
  conversationId: json['conversationId']?.toString(),
  username: json['username']?.toString(),
  lastSeenAt: json['lastSeenAt']?.toString(),
  lastSeenMessageId: json['lastSeenMessageId']?.toString(),
  isMute: bool.tryParse(json['isMute'].toString()),
  isPin: bool.tryParse(json['isPin'].toString()),
);

Map<String, dynamic> _$GroupChatDetailModelToJson(
  GroupChatDetailModel instance,
) => <String, dynamic>{
  'conversationId': instance.conversationId,
  'username': instance.username,
  'lastSeenAt': instance.lastSeenAt,
  'lastSeenMessageId': instance.lastSeenMessageId,
  'isMute': instance.isMute,
  'isPin': instance.isPin,
};
