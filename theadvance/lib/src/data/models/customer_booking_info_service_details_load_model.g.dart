// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'customer_booking_info_service_details_load_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CustomerBookingInfoServiceDetailsLoadModel
_$CustomerBookingInfoServiceDetailsLoadModelFromJson(
  Map<String, dynamic> json,
) => CustomerBookingInfoServiceDetailsLoadModel(
  items: (json['items'] is List)
      ? (json['items'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : CustomerBookingInfoServiceDetailsLoadItemsModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
);

Map<String, dynamic> _$CustomerBookingInfoServiceDetailsLoadModelToJson(
  CustomerBookingInfoServiceDetailsLoadModel instance,
) => <String, dynamic>{'items': instance.items};

CustomerBookingInfoServiceDetailsLoadItemsModel
_$CustomerBookingInfoServiceDetailsLoadItemsModelFromJson(
  Map<String, dynamic> json,
) => CustomerBookingInfoServiceDetailsLoadItemsModel(
  customerID: double.tryParse(json['CustomerID'].toString())?.toInt(),
  lSServiceID: double.tryParse(json['LSServiceID'].toString())?.toInt(),
  treatDate: json['TreatDate']?.toString(),
  treatmentRecordDetailID: double.tryParse(
    json['TreatmentRecordDetailID'].toString(),
  )?.toInt(),
  treatmentRecordID: double.tryParse(
    json['TreatmentRecordID'].toString(),
  )?.toInt(),
  treatNo: double.tryParse(json['TreatNo'].toString())?.toInt(),
  reExamDate: json['ReExamDate']?.toString(),
  quantity: double.tryParse(json['Quantity'].toString())?.toInt(),
  mayNames: (json['MayNames'] is List)
      ? (json['MayNames'] as List<dynamic>).map((e) => e?.toString()).toList()
      : [],
  tTBDNames: (json['TTBDNames'] is List)
      ? (json['TTBDNames'] as List<dynamic>).map((e) => e?.toString()).toList()
      : [],
  maumucNames: (json['MaumucNames'] is List)
      ? (json['MaumucNames'] as List<dynamic>)
            .map((e) => e?.toString())
            .toList()
      : [],
  thoigianID: json['ThoigianID']?.toString(),
  zoneNames: json['ZoneNames']?.toString(),
  balanceQuantity: json['BalanceQuantity']?.toString(),
  postSaiID: json['PostSaiID']?.toString(),
  treatDetailHtml: json['TreatDetailHtml']?.toString(),
  tanSo: json['TanSo']?.toString(),
  shot: json['Shot']?.toString(),
  buocSong: json['BuocSong']?.toString(),
  dauMay: json['DauMay']?.toString(),
  thongso: json['Thongso']?.toString(),
  cNCMay: json['CNCMay']?.toString(),
  thuchien: json['Thuchien']?.toString(),
  takeEmp: (json['TakeEmp'] is List)
      ? (json['TakeEmp'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : CustomerBookingInfoServiceDetailsLoadItemsTakeEmpModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
  advEmp: json['AdvEmp']?.toString(),
  images: (json['images'] is List)
      ? (json['images'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : CustomerBookingInfoServiceDetailsLoadItemsImagesModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
  doctorCode: json['DoctorCode']?.toString(),
  doctor: json['Doctor']?.toString(),
  skillEmployeeCode: json['SkillEmployeeCode']?.toString(),
  skillEmployee: json['SkillEmployee']?.toString(),
  checkEmployeeCode: json['CheckEmployeeCode']?.toString(),
  checkEmployee: json['CheckEmployee']?.toString(),
  result: double.tryParse(json['Result'].toString())?.toInt(),
);

Map<String, dynamic> _$CustomerBookingInfoServiceDetailsLoadItemsModelToJson(
  CustomerBookingInfoServiceDetailsLoadItemsModel instance,
) => <String, dynamic>{
  'CustomerID': instance.customerID,
  'LSServiceID': instance.lSServiceID,
  'TreatDate': instance.treatDate,
  'TreatmentRecordDetailID': instance.treatmentRecordDetailID,
  'TreatmentRecordID': instance.treatmentRecordID,
  'TreatNo': instance.treatNo,
  'ReExamDate': instance.reExamDate,
  'Quantity': instance.quantity,
  'MayNames': instance.mayNames,
  'TTBDNames': instance.tTBDNames,
  'MaumucNames': instance.maumucNames,
  'ThoigianID': instance.thoigianID,
  'ZoneNames': instance.zoneNames,
  'BalanceQuantity': instance.balanceQuantity,
  'PostSaiID': instance.postSaiID,
  'TreatDetailHtml': instance.treatDetailHtml,
  'TanSo': instance.tanSo,
  'Shot': instance.shot,
  'BuocSong': instance.buocSong,
  'DauMay': instance.dauMay,
  'Thongso': instance.thongso,
  'CNCMay': instance.cNCMay,
  'Thuchien': instance.thuchien,
  'TakeEmp': instance.takeEmp,
  'AdvEmp': instance.advEmp,
  'images': instance.images,
  'DoctorCode': instance.doctorCode,
  'Doctor': instance.doctor,
  'SkillEmployeeCode': instance.skillEmployeeCode,
  'SkillEmployee': instance.skillEmployee,
  'CheckEmployeeCode': instance.checkEmployeeCode,
  'CheckEmployee': instance.checkEmployee,
  'Result': instance.result,
};

CustomerBookingInfoServiceDetailsLoadItemsTakeEmpModel
_$CustomerBookingInfoServiceDetailsLoadItemsTakeEmpModelFromJson(
  Map<String, dynamic> json,
) => CustomerBookingInfoServiceDetailsLoadItemsTakeEmpModel(
  empCode: json['EmpCode']?.toString(),
  empName: json['EmpName']?.toString(),
);

Map<String, dynamic>
_$CustomerBookingInfoServiceDetailsLoadItemsTakeEmpModelToJson(
  CustomerBookingInfoServiceDetailsLoadItemsTakeEmpModel instance,
) => <String, dynamic>{
  'EmpCode': instance.empCode,
  'EmpName': instance.empName,
};

CustomerBookingInfoServiceDetailsLoadItemsImagesModel
_$CustomerBookingInfoServiceDetailsLoadItemsImagesModelFromJson(
  Map<String, dynamic> json,
) => CustomerBookingInfoServiceDetailsLoadItemsImagesModel(
  filePhoto: json['FilePhoto']?.toString(),
);

Map<String, dynamic>
_$CustomerBookingInfoServiceDetailsLoadItemsImagesModelToJson(
  CustomerBookingInfoServiceDetailsLoadItemsImagesModel instance,
) => <String, dynamic>{'FilePhoto': instance.filePhoto};
