// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'medical_log_detail_medicine_list_get_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class MedicalLogDetailMedicineListGetModelAdapter
    extends TypeAdapter<MedicalLogDetailMedicineListGetModel> {
  @override
  final int typeId = 135;

  @override
  MedicalLogDetailMedicineListGetModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return MedicalLogDetailMedicineListGetModel()
      ..items = (fields[0] as List?)?.cast<CatalogModel?>();
  }

  @override
  void write(BinaryWriter writer, MedicalLogDetailMedicineListGetModel obj) {
    writer
      ..writeByte(1)
      ..writeByte(0)
      ..write(obj.items);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MedicalLogDetailMedicineListGetModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MedicalLogDetailMedicineListGetModel
_$MedicalLogDetailMedicineListGetModelFromJson(Map<String, dynamic> json) =>
    MedicalLogDetailMedicineListGetModel()
      ..items = (json['items'] is List)
          ? (json['items'] as List<dynamic>?)
                ?.map(
                  (e) => e == null || e is! Map
                      ? null
                      : CatalogModel.fromJson(e as Map<String, dynamic>),
                )
                .toList()
          : [];

Map<String, dynamic> _$MedicalLogDetailMedicineListGetModelToJson(
  MedicalLogDetailMedicineListGetModel instance,
) => <String, dynamic>{'items': instance.items};
