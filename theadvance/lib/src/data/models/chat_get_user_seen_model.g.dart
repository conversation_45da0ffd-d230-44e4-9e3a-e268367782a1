// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'chat_get_user_seen_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ChatGetUserSeenModel _$ChatGetUserSeenModelFromJson(
  Map<String, dynamic> json,
) => ChatGetUserSeenModel(
  total: double.tryParse(json['total'].toString())?.toInt(),
  docs: (json['docs'] is List)
      ? (json['docs'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : ChatGetUserSeenDocsModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
);

Map<String, dynamic> _$ChatGetUserSeenModelToJson(
  ChatGetUserSeenModel instance,
) => <String, dynamic>{'total': instance.total, 'docs': instance.docs};

ChatGetUserSeenDocsModel _$ChatGetUserSeenDocsModelFromJson(
  Map<String, dynamic> json,
) => ChatGetUserSeenDocsModel(
  username: json['username']?.toString(),
  avatar: json['avatar']?.toString(),
  id: json['id']?.toString(),
  name: json['name']?.toString(),
);

Map<String, dynamic> _$ChatGetUserSeenDocsModelToJson(
  ChatGetUserSeenDocsModel instance,
) => <String, dynamic>{
  'username': instance.username,
  'avatar': instance.avatar,
  'id': instance.id,
  'name': instance.name,
};
