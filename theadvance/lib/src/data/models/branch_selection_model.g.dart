// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'branch_selection_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class BranchSelectionModelAdapter extends TypeAdapter<BranchSelectionModel> {
  @override
  final int typeId = 127;

  @override
  BranchSelectionModel read(BinaryReader reader) {
    return BranchSelectionModel();
  }

  @override
  void write(BinaryWriter writer, BranchSelectionModel obj) {
    writer.writeByte(0);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BranchSelectionModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BranchSelectionModel _$BranchSelectionModelFromJson(
  Map<String, dynamic> json,
) => BranchSelectionModel()
  ..items = (json['items'] is List)
      ? (json['items'] as List<dynamic>?)
            ?.map(
              (e) => e == null || e is! Map
                  ? null
                  : BranchItemsModel.fromJson(e as Map<String, dynamic>),
            )
            .toList()
      : [];

Map<String, dynamic> _$BranchSelectionModelToJson(
  BranchSelectionModel instance,
) => <String, dynamic>{'items': instance.items};
