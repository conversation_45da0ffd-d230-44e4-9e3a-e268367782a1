// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'chat_list_get_recent_contacts_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ChatListGetRecentContactsModel _$ChatListGetRecentContactsModelFromJson(
  Map<String, dynamic> json,
) => ChatListGetRecentContactsModel(
  total: double.tryParse(json['total'].toString())?.toInt(),
  limit: double.tryParse(json['limit'].toString())?.toInt(),
  items: (json['items'] is List)
      ? (json['items'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : ChatListGetRecentContactsItemsModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
);

Map<String, dynamic> _$ChatListGetRecentContactsModelToJson(
  ChatListGetRecentContactsModel instance,
) => <String, dynamic>{
  'total': instance.total,
  'limit': instance.limit,
  'items': instance.items,
};

ChatListGetRecentContactsItemsModel
_$ChatListGetRecentContactsItemsModelFromJson(
  Map<String, dynamic> json,
) => ChatListGetRecentContactsItemsModel(
  id: json['id']?.toString(),
  inviteLink: json['inviteLink']?.toString(),
  avatar: json['avatar']?.toString(),
  name: json['name']?.toString(),
  description: json['description']?.toString(),
  unreadCount: double.tryParse(json['unreadCount'].toString())?.toInt(),
  members: (json['members'] is List)
      ? (json['members'] as List<dynamic>).map((e) => e?.toString()).toList()
      : [],
  membersInfo: (json['membersInfo'] is List)
      ? (json['membersInfo'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : ChatListGetRecentContactsItemsMembersInfoModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
  lastMessageInfo:
      json['lastMessageInfo'] == null || json['lastMessageInfo'] is! Map
      ? null
      : ChatListGetRecentContactsItemsLastMessageInfoModel.fromJson(
          json['lastMessageInfo'] as Map<String, dynamic>,
        ),
  updatedAt: json['updatedAt']?.toString(),
  isGroup: bool.tryParse(json['isGroup'].toString()),
  type: json['type']?.toString(),
  isSavedMessage: bool.tryParse(json['isSavedMessage'].toString()),
  conversationDetails:
      json['conversationDetails'] == null || json['conversationDetails'] is! Map
      ? null
      : ChatListGetRecentContactsItemsConversationDetailsModel.fromJson(
          json['conversationDetails'] as Map<String, dynamic>,
        ),
  org: json['org']?.toString(),
);

Map<String, dynamic> _$ChatListGetRecentContactsItemsModelToJson(
  ChatListGetRecentContactsItemsModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'inviteLink': instance.inviteLink,
  'avatar': instance.avatar,
  'name': instance.name,
  'description': instance.description,
  'unreadCount': instance.unreadCount,
  'members': instance.members,
  'membersInfo': instance.membersInfo,
  'lastMessageInfo': instance.lastMessageInfo,
  'updatedAt': instance.updatedAt,
  'isGroup': instance.isGroup,
  'type': instance.type,
  'isSavedMessage': instance.isSavedMessage,
  'conversationDetails': instance.conversationDetails,
  'org': instance.org,
};

ChatListGetRecentContactsItemsMembersInfoModel
_$ChatListGetRecentContactsItemsMembersInfoModelFromJson(
  Map<String, dynamic> json,
) => ChatListGetRecentContactsItemsMembersInfoModel(
  username: json['username']?.toString(),
  avatar: json['avatar']?.toString(),
  id: json['id']?.toString(),
  name: json['name']?.toString(),
  type: json['type']?.toString(),
  departmentName: json['departmentName']?.toString(),
  phone: json['phone']?.toString(),
);

Map<String, dynamic> _$ChatListGetRecentContactsItemsMembersInfoModelToJson(
  ChatListGetRecentContactsItemsMembersInfoModel instance,
) => <String, dynamic>{
  'username': instance.username,
  'avatar': instance.avatar,
  'id': instance.id,
  'name': instance.name,
  'type': instance.type,
  'departmentName': instance.departmentName,
  'phone': instance.phone,
};

ChatListGetRecentContactsItemsLastMessageInfoModel
_$ChatListGetRecentContactsItemsLastMessageInfoModelFromJson(
  Map<String, dynamic> json,
) => ChatListGetRecentContactsItemsLastMessageInfoModel(
  id: json['id']?.toString(),
  content: json['content']?.toString(),
  parseMode: json['parseMode']?.toString(),
  createdBy: json['createdBy']?.toString(),
  createdAt: json['createdAt']?.toString(),
  createdByInfo: json['createdByInfo'] == null || json['createdByInfo'] is! Map
      ? null
      : ChatListGetRecentContactsItemsLastMessageInfoCreatedByInfoModel.fromJson(
          json['createdByInfo'] as Map<String, dynamic>,
        ),
);

Map<String, dynamic> _$ChatListGetRecentContactsItemsLastMessageInfoModelToJson(
  ChatListGetRecentContactsItemsLastMessageInfoModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'content': instance.content,
  'parseMode': instance.parseMode,
  'createdBy': instance.createdBy,
  'createdAt': instance.createdAt,
  'createdByInfo': instance.createdByInfo,
};

ChatListGetRecentContactsItemsConversationDetailsModel
_$ChatListGetRecentContactsItemsConversationDetailsModelFromJson(
  Map<String, dynamic> json,
) => ChatListGetRecentContactsItemsConversationDetailsModel(
  username: json['username']?.toString(),
  lastSeenAt: json['lastSeenAt']?.toString(),
  lastSeenMessageId: json['lastSeenMessageId']?.toString(),
  isMute: bool.tryParse(json['isMute'].toString()),
  isPin: bool.tryParse(json['isPin'].toString()),
  role: json['role']?.toString(),
);

Map<String, dynamic>
_$ChatListGetRecentContactsItemsConversationDetailsModelToJson(
  ChatListGetRecentContactsItemsConversationDetailsModel instance,
) => <String, dynamic>{
  'username': instance.username,
  'lastSeenAt': instance.lastSeenAt,
  'lastSeenMessageId': instance.lastSeenMessageId,
  'isMute': instance.isMute,
  'isPin': instance.isPin,
  'role': instance.role,
};

ChatListGetRecentContactsItemsLastMessageInfoCreatedByInfoModel
_$ChatListGetRecentContactsItemsLastMessageInfoCreatedByInfoModelFromJson(
  Map<String, dynamic> json,
) => ChatListGetRecentContactsItemsLastMessageInfoCreatedByInfoModel(
  username: json['username']?.toString(),
  avatar: json['avatar']?.toString(),
  id: json['id']?.toString(),
  name: json['name']?.toString(),
  type: json['type']?.toString(),
);

Map<String, dynamic>
_$ChatListGetRecentContactsItemsLastMessageInfoCreatedByInfoModelToJson(
  ChatListGetRecentContactsItemsLastMessageInfoCreatedByInfoModel instance,
) => <String, dynamic>{
  'username': instance.username,
  'avatar': instance.avatar,
  'id': instance.id,
  'name': instance.name,
  'type': instance.type,
};
