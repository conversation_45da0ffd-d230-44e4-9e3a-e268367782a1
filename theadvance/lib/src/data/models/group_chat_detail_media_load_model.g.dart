// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'group_chat_detail_media_load_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GroupChatDetailMediaLoadModel _$GroupChatDetailMediaLoadModelFromJson(
  Map<String, dynamic> json,
) => GroupChatDetailMediaLoadModel(
  total: double.tryParse(json['total'].toString())?.toInt(),
  limit: double.tryParse(json['limit'].toString())?.toInt(),
  items: (json['items'] is List)
      ? (json['items'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : GroupChatDetailMediaLoadItemsModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
);

Map<String, dynamic> _$GroupChatDetailMediaLoadModelToJson(
  GroupChatDetailMediaLoadModel instance,
) => <String, dynamic>{
  'total': instance.total,
  'limit': instance.limit,
  'items': instance.items,
};

GroupChatDetailMediaLoadItemsModel _$GroupChatDetailMediaLoadItemsModelFromJson(
  Map<String, dynamic> json,
) => GroupChatDetailMediaLoadItemsModel(
  conversationId: json['conversationId']?.toString(),
  messageId: json['messageId']?.toString(),
  link: json['link']?.toString(),
  size: double.tryParse(json['size'].toString())?.toInt(),
  originalname: json['originalname']?.toString(),
  mimetype: json['mimetype']?.toString(),
  createdAt: json['createdAt']?.toString(),
  updatedAt: json['updatedAt']?.toString(),
  id: json['id']?.toString(),
  thumbnail: json['thumbnail']?.toString(),
  messageInfo: json['messageInfo'] == null || json['messageInfo'] is! Map
      ? null
      : MessageInfoModel.fromJson(json['messageInfo'] as Map<String, dynamic>),
  width: double.tryParse(json['width'].toString())?.toInt(),
  height: double.tryParse(json['height'].toString())?.toInt(),
);

Map<String, dynamic> _$GroupChatDetailMediaLoadItemsModelToJson(
  GroupChatDetailMediaLoadItemsModel instance,
) => <String, dynamic>{
  'conversationId': instance.conversationId,
  'messageId': instance.messageId,
  'link': instance.link,
  'size': instance.size,
  'originalname': instance.originalname,
  'mimetype': instance.mimetype,
  'createdAt': instance.createdAt,
  'updatedAt': instance.updatedAt,
  'id': instance.id,
  'thumbnail': instance.thumbnail,
  'messageInfo': instance.messageInfo,
  'width': instance.width,
  'height': instance.height,
};

MessageInfoModel _$MessageInfoModelFromJson(Map<String, dynamic> json) =>
    MessageInfoModel(
      id: json['id']?.toString(),
      createdAt: json['createdAt']?.toString(),
    );

Map<String, dynamic> _$MessageInfoModelToJson(MessageInfoModel instance) =>
    <String, dynamic>{'id': instance.id, 'createdAt': instance.createdAt};
