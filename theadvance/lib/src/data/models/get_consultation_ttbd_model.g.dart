// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'get_consultation_ttbd_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GetConsultationTTBDParentGroupModel
_$GetConsultationTTBDParentGroupModelFromJson(
  Map<String, dynamic> json,
) => GetConsultationTTBDParentGroupModel(
  partnerId: json['PartnerId']?.toString(),
  topicId: json['TopicId']?.toString(),
  categoryGroups: (json['CategoryGroups'] is List)
      ? (json['CategoryGroups'] as List<dynamic>?)
            ?.map(
              (e) => GetConsultationTTBDParentModel.fromJson(
                e as Map<String, dynamic>,
              ),
            )
            .toList()
      : [],
  transactionDate: json['TransactionDate']?.toString(),
  employees: (json['Employees'] is List)
      ? (json['Employees'] as List<dynamic>?)
            ?.map((e) => TopicEmployeeModel.fromJson(e as Map<String, dynamic>))
            .toList()
      : [],
  managers: (json['Managers'] is List)
      ? (json['Managers'] as List<dynamic>?)
            ?.map((e) => TopicEmployeeModel.fromJson(e as Map<String, dynamic>))
            .toList()
      : [],
  incomeAmount: double.tryParse(json['IncomeAmount'].toString()),
  surgeryAmount: double.tryParse(json['SurgeryAmount'].toString()),
  debtAmount: double.tryParse(json['DebtAmount'].toString()),
  doctorAmount: double.tryParse(json['DoctorAmount'].toString()),
  pendingContent: json['PendingContent']?.toString(),
  content: json['Content']?.toString(),
  companions: (json['Companions'] is List)
      ? (json['Companions'] as List<dynamic>?)
            ?.map(
              (e) => CustomerRelationShipListItemModel.fromJson(
                e as Map<String, dynamic>,
              ),
            )
            .toList()
      : [],
  transDates: (json['TransDates'] is List)
      ? (json['TransDates'] as List<dynamic>?)?.map((e) => e as String).toList()
      : [],
);

TopicConsultationTTBDParentModel _$TopicConsultationTTBDParentModelFromJson(
  Map<String, dynamic> json,
) => TopicConsultationTTBDParentModel(
  transactionDate: json['TransactionDate']?.toString(),
  employees: (json['Employees'] is List)
      ? (json['Employees'] as List<dynamic>?)
            ?.map((e) => TopicEmployeeModel.fromJson(e as Map<String, dynamic>))
            .toList()
      : [],
  managers: (json['Managers'] is List)
      ? (json['Managers'] as List<dynamic>?)
            ?.map((e) => TopicEmployeeModel.fromJson(e as Map<String, dynamic>))
            .toList()
      : [],
  incomeAmount: double.tryParse(json['IncomeAmount'].toString()),
  surgeryAmount: double.tryParse(json['SurgeryAmount'].toString()),
  debtAmount: double.tryParse(json['DebtAmount'].toString()),
  doctorAmount: double.tryParse(json['DoctorAmount'].toString()),
  pendingContent: json['PendingContent']?.toString(),
  content: json['Content']?.toString(),
  companions: (json['Companions'] is List)
      ? (json['Companions'] as List<dynamic>?)
            ?.map(
              (e) => CustomerRelationShipListItemModel.fromJson(
                e as Map<String, dynamic>,
              ),
            )
            .toList()
      : [],
);

TopicEmployeeModel _$TopicEmployeeModelFromJson(Map<String, dynamic> json) =>
    TopicEmployeeModel(
      id: json['Id']?.toString(),
      name: json['Name']?.toString(),
    );

GetConsultationTTBDParentModel _$GetConsultationTTBDParentModelFromJson(
  Map<String, dynamic> json,
) => GetConsultationTTBDParentModel(
  categoryGroupId: json['CategoryGroupId']?.toString(),
  categoryGroupName: json['CategoryGroupName']?.toString(),
  categoryGroupOrder: double.tryParse(
    json['CategoryGroupOrder'].toString(),
  )?.toInt(),
  categorys: (json['Categorys'] is List)
      ? (json['Categorys'] as List<dynamic>?)
            ?.map(
              (e) => GetConsultationTTBDItemModel.fromJson(
                e as Map<String, dynamic>,
              ),
            )
            .toList()
      : [],
);

GetConsultationTTBDItemModel _$GetConsultationTTBDItemModelFromJson(
  Map<String, dynamic> json,
) => GetConsultationTTBDItemModel(
  categoryId: double.tryParse(json['CategoryId'].toString())?.toInt(),
  categoryCode: json['CategoryCode']?.toString(),
  categoryName: json['CategoryName']?.toString(),
  displayName: json['DisplayName']?.toString(),
  categoryOrder: double.tryParse(json['CategoryOrder'].toString())?.toInt(),
  selectMultipleDetails: bool.tryParse(
    json['SelectMultipleDetails'].toString(),
  ),
  categoryDetails: (json['CategoryDetails'] is List)
      ? (json['CategoryDetails'] as List<dynamic>?)
            ?.map(
              (e) => ConsultationTTBDOptionModel.fromJson(
                e as Map<String, dynamic>,
              ),
            )
            .toList()
      : [],
);

ConsultationTTBDOptionModel _$ConsultationTTBDOptionModelFromJson(
  Map<String, dynamic> json,
) => ConsultationTTBDOptionModel(
  categoryDetailId: double.tryParse(
    json['CategoryDetailId'].toString(),
  )?.toInt(),
  categoryDetailCode: json['CategoryDetailCode']?.toString(),
  categoryDetailName: json['CategoryDetailName']?.toString(),
  fieldOrder: double.tryParse(json['FieldOrder'].toString())?.toInt(),
  fieldType: json['FieldType']?.toString(),
  valueText: json['ValueText']?.toString(),
  requiresExplanation: bool.tryParse(json['RequiresExplanation'].toString()),
  active: bool.tryParse(json['Active'].toString()),
  children: (json['Children'] is List)
      ? (json['Children'] as List<dynamic>?)
            ?.map(
              (e) => ConsultationTTBDOptionModel.fromJson(
                e as Map<String, dynamic>,
              ),
            )
            .toList()
      : [],
);

ConsultationTTBDOptionEndModel _$ConsultationTTBDOptionEndModelFromJson(
  Map<String, dynamic> json,
) => ConsultationTTBDOptionEndModel(
  title: json['title']?.toString(),
  typeField: json['typeField']?.toString(),
  note: json['note']?.toString(),
);
