// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'order_food_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

OrderFoodModel _$OrderFoodModelFromJson(Map<String, dynamic> json) =>
    OrderFoodModel(
      today: (json['today'] is List)
          ? (json['today'] as List<dynamic>)
                .map(
                  (e) => e == null || e is! Map
                      ? null
                      : OrderFoodItemsModel.fromJson(e as Map<String, dynamic>),
                )
                .toList()
          : [],
      tomorrow: (json['tomorrow'] is List)
          ? (json['tomorrow'] as List<dynamic>)
                .map(
                  (e) => e == null || e is! Map
                      ? null
                      : OrderFoodItemsModel.fromJson(e as Map<String, dynamic>),
                )
                .toList()
          : [],
    );

Map<String, dynamic> _$OrderFoodModelToJson(OrderFoodModel instance) =>
    <String, dynamic>{'today': instance.today, 'tomorrow': instance.tomorrow};
