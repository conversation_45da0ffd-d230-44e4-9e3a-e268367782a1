// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'detail_crm_customer_service_load_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DetailCrmCustomerServiceLoadModel _$DetailCrmCustomerServiceLoadModelFromJson(
  Map<String, dynamic> json,
) => DetailCrmCustomerServiceLoadModel(
  items: (json['items'] is List)
      ? (json['items'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : DetailCrmCustomerServiceLoadItemsModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
);

Map<String, dynamic> _$DetailCrmCustomerServiceLoadModelToJson(
  DetailCrmCustomerServiceLoadModel instance,
) => <String, dynamic>{'items': instance.items};

DetailCrmCustomerServiceLoadItemsModel
_$DetailCrmCustomerServiceLoadItemsModelFromJson(Map<String, dynamic> json) =>
    DetailCrmCustomerServiceLoadItemsModel(
      id: double.tryParse(json['id'].toString())?.toInt(),
      code: json['code']?.toString(),
      name: json['name']?.toString(),
      unit: json['unit']?.toString(),
      departmentCode: json['department_code']?.toString(),
    );

Map<String, dynamic> _$DetailCrmCustomerServiceLoadItemsModelToJson(
  DetailCrmCustomerServiceLoadItemsModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'code': instance.code,
  'name': instance.name,
  'unit': instance.unit,
  'department_code': instance.departmentCode,
};
