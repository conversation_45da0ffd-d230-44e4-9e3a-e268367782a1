// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'medical_log_detail_get_tattoo_color_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MedicalLogDetailGetTattooColorModel
_$MedicalLogDetailGetTattooColorModelFromJson(Map<String, dynamic> json) =>
    MedicalLogDetailGetTattooColorModel()
      ..listTattooColor = (json['items'] is List)
          ? (json['items'] as List<dynamic>?)
                ?.map(
                  (e) => e == null || e is! Map
                      ? null
                      : CatalogModel.fromJson(e as Map<String, dynamic>),
                )
                .toList()
          : [];

Map<String, dynamic> _$MedicalLogDetailGetTattooColorModelToJson(
  MedicalLogDetailGetTattooColorModel instance,
) => <String, dynamic>{'items': instance.listTattooColor};
