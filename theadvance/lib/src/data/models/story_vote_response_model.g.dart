// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'story_vote_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

StoryVoteResponseModel _$StoryVoteResponseModelFromJson(
  Map<String, dynamic> json,
) => StoryVoteResponseModel(
  poll: json['poll'] == null || json['poll'] is! Map
      ? null
      : PollModel.fromJson(json['poll'] as Map<String, dynamic>),
  pollUser: (json['pollUser'] is List)
      ? (json['pollUser'] as List<dynamic>?)
            ?.map((e) => PollInfoModel.fromJson(e as Map<String, dynamic>))
            .toList()
      : [],
);

Map<String, dynamic> _$StoryVoteResponseModelToJson(
  StoryVoteResponseModel instance,
) => <String, dynamic>{'poll': instance.poll, 'pollUser': instance.pollUser};
