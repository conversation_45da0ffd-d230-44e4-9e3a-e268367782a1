// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'skin_customer_info_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SkinCustomerInfoModel _$SkinCustomerInfoModelFromJson(
  Map<String, dynamic> json,
) => SkinCustomerInfoModel(
  items: (json['items'] is List)
      ? (json['items'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : SkinCustomerInfoItemsModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
);

SkinCustomerInfoItemsModel _$SkinCustomerInfoItemsModelFromJson(
  Map<String, dynamic> json,
) => SkinCustomerInfoItemsModel(
  rowID: double.tryParse(json['rowID'].toString())?.toInt(),
  customerID: double.tryParse(json['customerID'].toString())?.toInt(),
  khquantam: json['khquantam']?.toString(),
  datungdtda: json['datungdtda']?.toString(),
  benhlydacbiet: json['benhlydacbiet']?.toString(),
  diung: json['diung']?.toString(),
  spgaybongtroc: json['spgaybongtroc']?.toString(),
  tgdentmv: json['tgdentmv']?.toString(),
  tiepxucnang: json['tiepxucnang']?.toString(),
  spdangsudung: json['spdangsudung']?.toString(),
  dtgiamcan: json['dtgiamcan']?.toString(),
  loaida: json['loaida']?.toString(),
  doday: json['doday']?.toString(),
  mauda: json['mauda']?.toString(),
  klttda: json['klttda']?.toString(),
  kldt: json['kldt']?.toString(),
  klnn: json['klnn']?.toString(),
  chuky: json['chuky']?.toString(),
  nguoitao: json['nguoitao']?.toString(),
);

Map<String, dynamic> _$SkinCustomerInfoItemsModelToJson(
  SkinCustomerInfoItemsModel instance,
) => <String, dynamic>{
  'rowID': instance.rowID,
  'customerID': instance.customerID,
  'khquantam': instance.khquantam,
  'datungdtda': instance.datungdtda,
  'benhlydacbiet': instance.benhlydacbiet,
  'diung': instance.diung,
  'spgaybongtroc': instance.spgaybongtroc,
  'tgdentmv': instance.tgdentmv,
  'tiepxucnang': instance.tiepxucnang,
  'spdangsudung': instance.spdangsudung,
  'dtgiamcan': instance.dtgiamcan,
  'loaida': instance.loaida,
  'doday': instance.doday,
  'mauda': instance.mauda,
  'klttda': instance.klttda,
  'kldt': instance.kldt,
  'klnn': instance.klnn,
  'chuky': instance.chuky,
  'nguoitao': instance.nguoitao,
};
