// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'chat_list_pin_conversation_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ChatListPinConversationModel _$ChatListPinConversationModelFromJson(
  Map<String, dynamic> json,
) => ChatListPinConversationModel(
  total: double.tryParse(json['total'].toString())?.toInt(),
  limit: double.tryParse(json['limit'].toString())?.toInt(),
  items: (json['items'] is List)
      ? (json['items'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : ChatListItemsModel.fromJson(e as Map<String, dynamic>),
            )
            .toList()
      : [],
);

Map<String, dynamic> _$ChatListPinConversationModelToJson(
  ChatListPinConversationModel instance,
) => <String, dynamic>{
  'total': instance.total,
  'limit': instance.limit,
  'items': instance.items,
};
