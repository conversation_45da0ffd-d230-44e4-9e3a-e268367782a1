// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'hr_organization_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

HrOrganizationModel _$HrOrganizationModelFromJson(Map<String, dynamic> json) =>
    HrOrganizationModel(
      items: (json['items'] is List)
          ? (json['items'] as List<dynamic>)
                .map(
                  (e) => e == null || e is! Map
                      ? null
                      : HrOrganizationItemsModel.fromJson(
                          e as Map<String, dynamic>,
                        ),
                )
                .toList()
          : [],
    );

Map<String, dynamic> _$HrOrganizationModelToJson(
  HrOrganizationModel instance,
) => <String, dynamic>{'items': instance.items};

HrOrganizationItemsModel _$HrOrganizationItemsModelFromJson(
  Map<String, dynamic> json,
) => HrOrganizationItemsModel(
  organizationId: json['organizationId']?.toString(),
  organizationName: json['organizationName']?.toString(),
  organizationUrl: json['organizationUrl']?.toString(),
);

Map<String, dynamic> _$HrOrganizationItemsModelToJson(
  HrOrganizationItemsModel instance,
) => <String, dynamic>{
  'organizationId': instance.organizationId,
  'organizationName': instance.organizationName,
  'organizationUrl': instance.organizationUrl,
};
