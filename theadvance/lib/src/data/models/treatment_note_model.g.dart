// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'treatment_note_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TreatmentNoteModel _$TreatmentNoteModelFromJson(Map<String, dynamic> json) =>
    TreatmentNoteModel(
      items: (json['items'] is List)
          ? (json['items'] as List<dynamic>)
                .map(
                  (e) => e == null || e is! Map
                      ? null
                      : TreatmentNoteItemsModel.fromJson(
                          e as Map<String, dynamic>,
                        ),
                )
                .toList()
          : [],
    );

Map<String, dynamic> _$TreatmentNoteModelToJson(TreatmentNoteModel instance) =>
    <String, dynamic>{'items': instance.items};

TreatmentNoteItemsModel _$TreatmentNoteItemsModelFromJson(
  Map<String, dynamic> json,
) => TreatmentNoteItemsModel(
  treatmentRecordID: double.tryParse(
    json['TreatmentRecordID'].toString(),
  )?.toInt(),
  treatType: json['TreatType']?.toString(),
  lSServiceID: double.tryParse(json['LSServiceID'].toString())?.toInt(),
  treatmentNoteContent: json['TreatmentNoteContent']?.toString(),
);

Map<String, dynamic> _$TreatmentNoteItemsModelToJson(
  TreatmentNoteItemsModel instance,
) => <String, dynamic>{
  'TreatmentRecordID': instance.treatmentRecordID,
  'TreatType': instance.treatType,
  'LSServiceID': instance.lSServiceID,
  'TreatmentNoteContent': instance.treatmentNoteContent,
};
