// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'medical_service_list_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class MedicalServiceListModelAdapter
    extends TypeAdapter<MedicalServiceListModel> {
  @override
  final int typeId = 134;

  @override
  MedicalServiceListModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return MedicalServiceListModel()
      ..items = (fields[0] as List?)?.cast<MedicalServiceModel?>();
  }

  @override
  void write(BinaryWriter writer, MedicalServiceListModel obj) {
    writer
      ..writeByte(1)
      ..writeByte(0)
      ..write(obj.items);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MedicalServiceListModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MedicalServiceListModel _$MedicalServiceListModelFromJson(
  Map<String, dynamic> json,
) => MedicalServiceListModel()
  ..items = (json['items'] is List)
      ? (json['items'] as List<dynamic>?)
            ?.map(
              (e) => e == null || e is! Map
                  ? null
                  : MedicalServiceModel.fromJson(e as Map<String, dynamic>),
            )
            .toList()
      : [];

Map<String, dynamic> _$MedicalServiceListModelToJson(
  MedicalServiceListModel instance,
) => <String, dynamic>{'items': instance.items};

MedicalServiceModel _$MedicalServiceModelFromJson(Map<String, dynamic> json) =>
    MedicalServiceModel(
      treatmentId: json['treatment_id']?.toString(),
      customerId: json['customer_id']?.toString(),
      lsServiceId: json['ls_service_id']?.toString(),
      serviceName: json['service_name']?.toString(),
      date: json['date']?.toString(),
      buyCount: json['buy_count']?.toString(),
      existsCount: json['exists_count']?.toString(),
      treatNo: json['treat_no']?.toString(),
    );

Map<String, dynamic> _$MedicalServiceModelToJson(
  MedicalServiceModel instance,
) => <String, dynamic>{
  'treatment_id': instance.treatmentId,
  'customer_id': instance.customerId,
  'ls_service_id': instance.lsServiceId,
  'service_name': instance.serviceName,
  'date': instance.date,
  'buy_count': instance.buyCount,
  'exists_count': instance.existsCount,
  'treat_no': instance.treatNo,
};
