// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tag_list_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TagListModel _$TagListModelFromJson(Map<String, dynamic> json) => TagListModel(
  total: double.tryParse(json['total'].toString())?.toInt(),
  limit: double.tryParse(json['limit'].toString())?.toInt(),
  page: double.tryParse(json['page'].toString())?.toInt(),
  items: (json['items'] is List)
      ? (json['items'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : TagListItemsModel.fromJson(e as Map<String, dynamic>),
            )
            .toList()
      : [],
);

Map<String, dynamic> _$TagListModelToJson(TagListModel instance) =>
    <String, dynamic>{
      'total': instance.total,
      'limit': instance.limit,
      'page': instance.page,
      'items': instance.items,
    };

TagListItemsModel _$TagListItemsModelFromJson(Map<String, dynamic> json) =>
    TagListItemsModel(
      username: json['username']?.toString(),
      avatar: json['avatar']?.toString(),
      bio: json['bio']?.toString(),
      coverImage: json['coverImage']?.toString(),
      createdAt: json['createdAt']?.toString(),
      name: json['name']?.toString(),
      updatedAt: json['updatedAt']?.toString(),
      departmentName: json['departmentName']?.toString(),
    );

Map<String, dynamic> _$TagListItemsModelToJson(TagListItemsModel instance) =>
    <String, dynamic>{
      'username': instance.username,
      'avatar': instance.avatar,
      'bio': instance.bio,
      'coverImage': instance.coverImage,
      'createdAt': instance.createdAt,
      'name': instance.name,
      'updatedAt': instance.updatedAt,
      'departmentName': instance.departmentName,
    };
