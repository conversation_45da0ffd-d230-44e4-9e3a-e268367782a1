// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'history_checkin_fetch_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

HistoryCheckinFetchModel _$HistoryCheckinFetchModelFromJson(
  Map<String, dynamic> json,
) => HistoryCheckinFetchModel(
  monthYear: json['monthYear']?.toString(),
  month: json['month']?.toString(),
  year: json['year']?.toString(),
  leaveDays: json['leaveDays']?.toString(),
  countCheckIn: json['countCheckIn']?.toString(),
  items: (json['items'] is List)
      ? (json['items'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : HistoryCheckinDetailModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
);

Map<String, dynamic> _$HistoryCheckinFetchModelToJson(
  HistoryCheckinFetchModel instance,
) => <String, dynamic>{
  'monthYear': instance.monthYear,
  'month': instance.month,
  'year': instance.year,
  'leaveDays': instance.leaveDays,
  'countCheckIn': instance.countCheckIn,
  'items': instance.items,
};

HistoryCheckinDetailModel _$HistoryCheckinDetailModelFromJson(
  Map<String, dynamic> json,
) => HistoryCheckinDetailModel(
  date: json['date']?.toString(),
  timeIn: json['timeIn']?.toString(),
  timeOut: json['timeOut']?.toString(),
  finalCheck: double.tryParse(json['finalCheck'].toString()),
  status: json['status']?.toString(),
  jsonConvertTimeSheets: (json['jsonConvertTimeSheets'] is List)
      ? (json['jsonConvertTimeSheets'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : HistoryCheckinRecordModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
  workingTime: json['workingTime']?.toString(),
);

Map<String, dynamic> _$HistoryCheckinDetailModelToJson(
  HistoryCheckinDetailModel instance,
) => <String, dynamic>{
  'date': instance.date,
  'timeIn': instance.timeIn,
  'timeOut': instance.timeOut,
  'finalCheck': instance.finalCheck,
  'status': instance.status,
  'jsonConvertTimeSheets': instance.jsonConvertTimeSheets,
  'workingTime': instance.workingTime,
};

HistoryCheckinRecordModel _$HistoryCheckinRecordModelFromJson(
  Map<String, dynamic> json,
) => HistoryCheckinRecordModel(
  statusName: json['StatusName']?.toString(),
  employeeTimesheetTypeDescription: json['EmployeeTimesheetTypeDescription']
      ?.toString(),
);

Map<String, dynamic> _$HistoryCheckinRecordModelToJson(
  HistoryCheckinRecordModel instance,
) => <String, dynamic>{
  'StatusName': instance.statusName,
  'EmployeeTimesheetTypeDescription': instance.employeeTimesheetTypeDescription,
};
