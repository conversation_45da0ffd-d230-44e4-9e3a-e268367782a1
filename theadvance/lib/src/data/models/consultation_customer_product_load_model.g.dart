// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'consultation_customer_product_load_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ConsultationCustomerProductLoadModel
_$ConsultationCustomerProductLoadModelFromJson(Map<String, dynamic> json) =>
    ConsultationCustomerProductLoadModel(
      items: (json['items'] is List)
          ? (json['items'] as List<dynamic>)
                .map(
                  (e) => e == null || e is! Map
                      ? null
                      : ConsultationCustomerProductLoadItemsModel.fromJson(
                          e as Map<String, dynamic>,
                        ),
                )
                .toList()
          : [],
    );

Map<String, dynamic> _$ConsultationCustomerProductLoadModelToJson(
  ConsultationCustomerProductLoadModel instance,
) => <String, dynamic>{'items': instance.items};

ConsultationCustomerProductLoadItemsModel
_$ConsultationCustomerProductLoadItemsModelFromJson(
  Map<String, dynamic> json,
) => ConsultationCustomerProductLoadItemsModel(
  productID: double.tryParse(json['ProductID'].toString())?.toInt(),
  productCode: json['ProductCode']?.toString(),
  productName: json['ProductName']?.toString(),
  price: double.tryParse(json['Price'].toString())?.toInt(),
  originalPrice: double.tryParse(json['OriginalPrice'].toString())?.toInt(),
  checkPrice: bool.tryParse(json['IsNotAllowMinPrice'].toString()),
  unitList: (json['UnitList'] is List)
      ? (json['UnitList'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : ConsultationCustomerProductLoadItemsUnitListModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
);

Map<String, dynamic> _$ConsultationCustomerProductLoadItemsModelToJson(
  ConsultationCustomerProductLoadItemsModel instance,
) => <String, dynamic>{
  'ProductID': instance.productID,
  'ProductCode': instance.productCode,
  'ProductName': instance.productName,
  'Price': instance.price,
  'OriginalPrice': instance.originalPrice,
  'IsNotAllowMinPrice': instance.checkPrice,
  'UnitList': instance.unitList,
};

ConsultationCustomerProductLoadItemsUnitListModel
_$ConsultationCustomerProductLoadItemsUnitListModelFromJson(
  Map<String, dynamic> json,
) => ConsultationCustomerProductLoadItemsUnitListModel(
  unit: json['Unit']?.toString(),
);

Map<String, dynamic> _$ConsultationCustomerProductLoadItemsUnitListModelToJson(
  ConsultationCustomerProductLoadItemsUnitListModel instance,
) => <String, dynamic>{'Unit': instance.unit};
