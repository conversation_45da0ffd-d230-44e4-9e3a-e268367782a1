// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'staff_evaluation_periods_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

StaffEvaluationPeriodsModel _$StaffEvaluationPeriodsModelFromJson(
  Map<String, dynamic> json,
) => StaffEvaluationPeriodsModel(
  items: (json['items'] is List)
      ? (json['items'] as List<dynamic>?)
            ?.map(
              (e) => StaffEvaluationPeriodModel.fromJson(
                e as Map<String, dynamic>,
              ),
            )
            .toList()
      : [],
);

Map<String, dynamic> _$StaffEvaluationPeriodsModelToJson(
  StaffEvaluationPeriodsModel instance,
) => <String, dynamic>{'items': instance.items};

StaffEvaluationPeriodModel _$StaffEvaluationPeriodModelFromJson(
  Map<String, dynamic> json,
) => StaffEvaluationPeriodModel(
  appraisalPeriodId: json['AppraisalPeriodID']?.toString(),
  appraisalPeriodName: json['AppraisalPeriodName']?.toString(),
  appraisalTypeName: json['AppraisalTypeName']?.toString(),
  tranYear: double.tryParse(json['TranYear'].toString())?.toInt(),
  fromDate: json['FromDate']?.toString(),
  toDate: json['ToDate']?.toString(),
  descriptions: json['Descriptions']?.toString(),
  statusId: json['StatusID']?.toString(),
  statusName: json['StatusName']?.toString(),
  employeeApprover: json['EmployeeApprover']?.toString(),
  positionId: json['PositionID']?.toString(),
);

Map<String, dynamic> _$StaffEvaluationPeriodModelToJson(
  StaffEvaluationPeriodModel instance,
) => <String, dynamic>{
  'AppraisalPeriodID': instance.appraisalPeriodId,
  'AppraisalPeriodName': instance.appraisalPeriodName,
  'AppraisalTypeName': instance.appraisalTypeName,
  'TranYear': instance.tranYear,
  'FromDate': instance.fromDate,
  'ToDate': instance.toDate,
  'Descriptions': instance.descriptions,
  'StatusID': instance.statusId,
  'StatusName': instance.statusName,
  'EmployeeApprover': instance.employeeApprover,
  'PositionID': instance.positionId,
};
