// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'group_chat_detail_avatar_upload_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GroupChatDetailAvatarUploadModel _$GroupChatDetailAvatarUploadModelFromJson(
  Map<String, dynamic> json,
) => GroupChatDetailAvatarUploadModel(
  filename: json['filename']?.toString(),
  id: json['id']?.toString(),
  size: double.tryParse(json['size'].toString())?.toInt(),
  mimetype: json['mimetype']?.toString(),
  host: json['host']?.toString(),
  link: json['link']?.toString(),
  path: json['path']?.toString(),
);

Map<String, dynamic> _$GroupChatDetailAvatarUploadModelToJson(
  GroupChatDetailAvatarUploadModel instance,
) => <String, dynamic>{
  'filename': instance.filename,
  'id': instance.id,
  'size': instance.size,
  'mimetype': instance.mimetype,
  'host': instance.host,
  'link': instance.link,
  'path': instance.path,
};
