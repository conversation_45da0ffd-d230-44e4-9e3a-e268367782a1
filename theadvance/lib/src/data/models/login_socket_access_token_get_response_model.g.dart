// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'login_socket_access_token_get_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LoginSocketAccessTokenGetResponseModel
_$LoginSocketAccessTokenGetResponseModelFromJson(Map<String, dynamic> json) =>
    LoginSocketAccessTokenGetResponseModel(
        data: json['data'] == null || json['data'] is! Map
            ? null
            : LoginSocketAccessTokenGetModel.fromJson(
                json['data'] as Map<String, dynamic>,
              ),
      )
      ..errorCode = double.tryParse(json['code'].toString())?.toInt()
      ..errorMessage = json['message']?.toString();
