// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'medical_log_detail_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MedicalLogDetailModel _$MedicalLogDetailModelFromJson(
  Map<String, dynamic> json,
) => MedicalLogDetailModel()
  ..id = json['id']?.toString()
  ..date = json['date']?.toString()
  ..serviceName = json['service_name']?.toString()
  ..buyCount = json['buy_count']?.toString()
  ..existsCount = json['exists_count']?.toString()
  ..detailService = json['detail_service']?.toString()
  ..dateCountReturn = json['date_count_return']?.toString()
  ..dateReturn = json['date_return']?.toString()
  ..adviseEmployee =
      json['advise_employee'] == null || json['advise_employee'] is! Map
      ? null
      : EmployeeModel.fromJson(json['advise_employee'] as Map<String, dynamic>)
  ..takeEmployee =
      json['take_employee'] == null || json['take_employee'] is! Map
      ? null
      : EmployeeModel.fromJson(json['take_employee'] as Map<String, dynamic>)
  ..takeDoctor = json['take_doctor'] == null || json['take_doctor'] is! Map
      ? null
      : EmployeeModel.fromJson(json['take_doctor'] as Map<String, dynamic>)
  ..machine = json['machine'] == null || json['machine'] is! Map
      ? null
      : MedicalLogDetailChildModel.fromJson(
          json['machine'] as Map<String, dynamic>,
        )
  ..postSaiId = json['post_sai_id']?.toString()
  ..haMedicineDescription = json['ha_medicine_description']?.toString()
  ..point = json['point']?.toString()
  ..kn = json['kn']?.toString()
  ..haDeviant = json['ha_deviant']?.toString()
  ..originalStatus =
      json['original_status'] == null || json['original_status'] is! Map
      ? null
      : MedicalLogDetailChildModel.fromJson(
          json['original_status'] as Map<String, dynamic>,
        )
  ..tattooColor = json['tattoo_color'] == null || json['tattoo_color'] is! Map
      ? null
      : MedicalLogDetailChildModel.fromJson(
          json['tattoo_color'] as Map<String, dynamic>,
        )
  ..tattooTime = json['tattoo_time'] == null || json['tattoo_time'] is! Map
      ? null
      : CatalogModel.fromJson(json['tattoo_time'] as Map<String, dynamic>)
  ..images =
      ((json['images'] is List)
          ? (json['images'] as List<dynamic>?)
                ?.map((e) => e?.toString())
                .toList()
          : []) ??
      []
  ..prescription = json['prescription'] == null || json['prescription'] is! Map
      ? null
      : MedicalServiceLogPrescriptionModel.fromJson(
          json['prescription'] as Map<String, dynamic>,
        );

Map<String, dynamic> _$MedicalLogDetailModelToJson(
  MedicalLogDetailModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'date': instance.date,
  'service_name': instance.serviceName,
  'buy_count': instance.buyCount,
  'exists_count': instance.existsCount,
  'detail_service': instance.detailService,
  'date_count_return': instance.dateCountReturn,
  'date_return': instance.dateReturn,
  'advise_employee': instance.adviseEmployee,
  'take_employee': instance.takeEmployee,
  'take_doctor': instance.takeDoctor,
  'machine': instance.machine,
  'post_sai_id': instance.postSaiId,
  'ha_medicine_description': instance.haMedicineDescription,
  'point': instance.point,
  'kn': instance.kn,
  'ha_deviant': instance.haDeviant,
  'original_status': instance.originalStatus,
  'tattoo_color': instance.tattooColor,
  'tattoo_time': instance.tattooTime,
  'images': instance.images,
  'prescription': instance.prescription,
};

MedicalLogDetailChildModel _$MedicalLogDetailChildModelFromJson(
  Map<String, dynamic> json,
) => MedicalLogDetailChildModel(
  names: json['names']?.toString(),
  ids:
      ((json['ids'] is List)
          ? (json['ids'] as List<dynamic>?)
                ?.map(
                  (e) => e == null || e is! Map
                      ? null
                      : CatalogModel.fromJson(e as Map<String, dynamic>),
                )
                .toList()
          : []) ??
      [],
);

Map<String, dynamic> _$MedicalLogDetailChildModelToJson(
  MedicalLogDetailChildModel instance,
) => <String, dynamic>{'ids': instance.ids, 'names': instance.names};

CatalogModel _$CatalogModelFromJson(Map<String, dynamic> json) => CatalogModel(
  id: json['id']?.toString(),
  name: json['name']?.toString(),
  text: json['text']?.toString(),
  types: json['types']?.toString(),
  departmentCode: json['department_code']?.toString(),
);

Map<String, dynamic> _$CatalogModelToJson(CatalogModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'text': instance.text,
      'types': instance.types,
      'department_code': instance.departmentCode,
    };

MedicalServiceLogPrescriptionModel _$MedicalServiceLogPrescriptionModelFromJson(
  Map<String, dynamic> json,
) => MedicalServiceLogPrescriptionModel(
  treatmentId: json['treatment_id']?.toString(),
  id: json['id']?.toString(),
  diagnose: json['diagnose']?.toString(),
  note: json['note']?.toString(),
  doctorTakeName: json['doctor_take_name']?.toString(),
  detail:
      ((json['detail'] is List)
          ? (json['detail'] as List<dynamic>?)
                ?.map(
                  (e) => e == null || e is! Map
                      ? null
                      : MedicineDetailModel.fromJson(e as Map<String, dynamic>),
                )
                .toList()
          : []) ??
      [],
);

Map<String, dynamic> _$MedicalServiceLogPrescriptionModelToJson(
  MedicalServiceLogPrescriptionModel instance,
) => <String, dynamic>{
  'treatment_id': instance.treatmentId,
  'id': instance.id,
  'diagnose': instance.diagnose,
  'note': instance.note,
  'doctor_take_name': instance.doctorTakeName,
  'detail': instance.detail,
};
