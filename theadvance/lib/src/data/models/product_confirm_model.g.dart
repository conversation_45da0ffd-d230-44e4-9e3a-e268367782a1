// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'product_confirm_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ProductConfirmModel _$ProductConfirmModelFromJson(Map<String, dynamic> json) =>
    ProductConfirmModel(
      items: (json['Items'] is List)
          ? (json['Items'] as List<dynamic>?)
                ?.map(
                  (e) => ProductConfirmItemsModel.fromJson(
                    e as Map<String, dynamic>,
                  ),
                )
                .toList()
          : [],
      totalRow: double.tryParse(json['TotalRow'].toString())?.toInt(),
    );

ProductConfirmItemsModel _$ProductConfirmItemsModelFromJson(
  Map<String, dynamic> json,
) => ProductConfirmItemsModel(
  rowKey: double.tryParse(json['RowKey'].toString())?.toInt(),
  organizationID: json['OrganizationID']?.toString(),
  transactionID: json['TransactionID']?.toString(),
  transactionNo: json['TransactionNo']?.toString(),
  transactionDate: json['TransactionDate']?.toString(),
  branchID: json['BranchID']?.toString(),
  branchName: json['BranchName']?.toString(),
  contractTypeDeclareName: json['ContractTypeDeclareName']?.toString(),
  currencyID: json['CurrencyID']?.toString(),
  exchangeRate: double.tryParse(json['ExchangeRate'].toString())?.toInt(),
  amount: double.tryParse(json['Amount'].toString()),
  content: json['Content']?.toString(),
  isUrgent: bool.tryParse(json['IsUrgent'].toString()),
);

ProductDetailConfirmModel _$ProductDetailConfirmModelFromJson(
  Map<String, dynamic> json,
) => ProductDetailConfirmModel(
  paymentDetails: (json['PaymentDetails'] is List)
      ? (json['PaymentDetails'] as List<dynamic>?)
            ?.map(
              (e) => ProductDetailConfirmPaymentDetailsModel.fromJson(
                e as Map<String, dynamic>,
              ),
            )
            .toList()
      : [],
  attachmentFiles: (json['AttachmentFiles'] is List)
      ? (json['AttachmentFiles'] as List<dynamic>?)
            ?.map(
              (e) => ProductDetailConfirmAttachmentFilesModel.fromJson(
                e as Map<String, dynamic>,
              ),
            )
            .toList()
      : [],
  histories: (json['Histories'] is List)
      ? (json['Histories'] as List<dynamic>?)
            ?.map(
              (e) => ProductDetailConfirmHistoriesModel.fromJson(
                e as Map<String, dynamic>,
              ),
            )
            .toList()
      : [],
  rowKey: double.tryParse(json['RowKey'].toString())?.toInt(),
  transactionID: json['TransactionID']?.toString(),
  transactionNo: json['TransactionNo']?.toString(),
  transactionDate: json['TransactionDate']?.toString(),
  branchID: json['BranchID']?.toString(),
  branchName: json['BranchName']?.toString(),
  contractTypeDeclareName: json['ContractTypeDeclareName']?.toString(),
  currencyID: json['CurrencyID']?.toString(),
  exchangeRate: double.tryParse(json['ExchangeRate'].toString())?.toInt(),
  amount: double.tryParse(json['Amount'].toString()),
  content: json['Content']?.toString(),
  isUrgent: bool.tryParse(json['IsUrgent'].toString()),
  organizationID: json['OrganizationID']?.toString(),
  suggests: (json['Suggests'] is List)
      ? (json['Suggests'] as List<dynamic>?)
            ?.map(
              (e) => ProductDetailConfirmSugestsModel.fromJson(
                e as Map<String, dynamic>,
              ),
            )
            .toList()
      : [],
);

ProductDetailConfirmPaymentDetailsModel
_$ProductDetailConfirmPaymentDetailsModelFromJson(Map<String, dynamic> json) =>
    ProductDetailConfirmPaymentDetailsModel(
      paymentDate: json['PaymentDate']?.toString(),
      paymentAmount: double.tryParse(json['PaymentAmount'].toString()),
      paymentReasonID: json['PaymentReasonID']?.toString(),
      description: json['Description']?.toString(),
    );

ProductDetailConfirmAttachmentFilesModel
_$ProductDetailConfirmAttachmentFilesModelFromJson(Map<String, dynamic> json) =>
    ProductDetailConfirmAttachmentFilesModel(
      fileUrl: json['FileUrl']?.toString(),
      fileName: json['FileName']?.toString(),
      fileType: json['FileType']?.toString(),
    );

ProductDetailConfirmSugestsModel _$ProductDetailConfirmSugestsModelFromJson(
  Map<String, dynamic> json,
) => ProductDetailConfirmSugestsModel(
  transactionNo: json['TransactionNo']?.toString(),
  transactionDate: json['TransactionDate']?.toString(),
  suggestDetails: (json['SuggestDetails'] is List)
      ? (json['SuggestDetails'] as List<dynamic>?)
            ?.map(
              (e) => ProductDetailConfirmSugestsItemsModel.fromJson(
                e as Map<String, dynamic>,
              ),
            )
            .toList()
      : [],
  notes: json['Notes']?.toString(),
);

ProductDetailConfirmSugestsItemsModel
_$ProductDetailConfirmSugestsItemsModelFromJson(Map<String, dynamic> json) =>
    ProductDetailConfirmSugestsItemsModel(
      itemId: json['ItemID']?.toString(),
      itemName: json['ItemName']?.toString(),
      quantity: double.tryParse(json['Quantity'].toString())?.toInt(),
      unitName: json['UnitName']?.toString(),
    );

ProductDetailConfirmHistoriesModel _$ProductDetailConfirmHistoriesModelFromJson(
  Map<String, dynamic> json,
) => ProductDetailConfirmHistoriesModel(
  content: json['Content']?.toString(),
  createdBy: json['CreatedBy']?.toString(),
  createdName: json['CreatedName']?.toString(),
  createdDate: json['CreatedDate']?.toString(),
);
