// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'story_person_list_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

StoryPersonListModel _$StoryPersonListModelFromJson(
  Map<String, dynamic> json,
) => StoryPersonListModel(
  items: (json['items'] is List)
      ? (json['items'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : StoryPersonListItemsModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
);

Map<String, dynamic> _$StoryPersonListModelToJson(
  StoryPersonListModel instance,
) => <String, dynamic>{'items': instance.items};

StoryPersonListItemsModel _$StoryPersonListItemsModelFromJson(
  Map<String, dynamic> json,
) => StoryPersonListItemsModel(
  id: double.tryParse(json['id'].toString())?.toInt(),
  content: json['content']?.toString(),
  attachment: (json['attachment'] is List)
      ? (json['attachment'] as List<dynamic>?)
            ?.map((e) => AttachmentModel.fromJson(e as Map<String, dynamic>))
            .toList()
      : [],
  createdByInfo: json['createdByInfo'] == null || json['createdByInfo'] is! Map
      ? null
      : CreatedByInfoModel.fromJson(
          json['createdByInfo'] as Map<String, dynamic>,
        ),
  location: json['location']?.toString(),
  createdAt: json['createdAt']?.toString(),
  tags: (json['tags'] is List)
      ? (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList()
      : [],
  theme: json['theme']?.toString(),
  reactCount: double.tryParse(json['reactCount'].toString())?.toInt(),
  commentCount: double.tryParse(json['commentCount'].toString())?.toInt(),
  reactInfo: json['reactInfo'] == null || json['reactInfo'] is! Map
      ? null
      : ReactInfoModel.fromJson(json['reactInfo'] as Map<String, dynamic>),
);

Map<String, dynamic> _$StoryPersonListItemsModelToJson(
  StoryPersonListItemsModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'content': instance.content,
  'attachment': instance.attachment,
  'createdByInfo': instance.createdByInfo,
  'location': instance.location,
  'createdAt': instance.createdAt,
  'tags': instance.tags,
  'theme': instance.theme,
  'reactCount': instance.reactCount,
  'commentCount': instance.commentCount,
  'reactInfo': instance.reactInfo,
};
