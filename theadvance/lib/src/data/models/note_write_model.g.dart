// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'note_write_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

NoteWriteModel _$NoteWriteModelFromJson(
  Map<String, dynamic> json,
) => NoteWriteModel(
  ticketId: double.tryParse(json['ticket_id'].toString())?.toInt(),
  customerErpId: double.tryParse(json['customer_erp_id'].toString())?.toInt(),
  customerCrmId: double.tryParse(json['customer_crm_id'].toString())?.toInt(),
  customerCode: json['customer_code']?.toString(),
  customerName: json['customer_name']?.toString(),
  branchName: json['branch_name']?.toString(),
  jobName: json['job_name']?.toString(),
  createdDate: json['created_date']?.toString(),
  updatedDate: json['updated_date']?.toString(),
  workStatus: json['work_status']?.toString(),
  ticketType: json['ticket_type']?.toString(),
  typeOfTeam: json['type_of_team']?.toString(),
);
