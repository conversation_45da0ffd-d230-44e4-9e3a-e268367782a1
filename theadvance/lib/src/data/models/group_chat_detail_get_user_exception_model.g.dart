// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'group_chat_detail_get_user_exception_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GroupChatDetailGetUserExceptionModel
_$GroupChatDetailGetUserExceptionModelFromJson(Map<String, dynamic> json) =>
    GroupChatDetailGetUserExceptionModel(
      items: (json['items'] is List)
          ? (json['items'] as List<dynamic>)
                .map(
                  (e) => e == null || e is! Map
                      ? null
                      : GroupChatDetailGetUserExceptionItemsModel.fromJson(
                          e as Map<String, dynamic>,
                        ),
                )
                .toList()
          : [],
    );

Map<String, dynamic> _$GroupChatDetailGetUserExceptionModelToJson(
  GroupChatDetailGetUserExceptionModel instance,
) => <String, dynamic>{'items': instance.items};

GroupChatDetailGetUserExceptionItemsModel
_$GroupChatDetailGetUserExceptionItemsModelFromJson(
  Map<String, dynamic> json,
) => GroupChatDetailGetUserExceptionItemsModel(
  conversationId: json['conversationId']?.toString(),
  username: json['username']?.toString(),
  conversationType: json['conversationType']?.toString(),
  createdAt: json['createdAt']?.toString(),
  isMute: bool.tryParse(json['isMute'].toString()),
  isPin: bool.tryParse(json['isPin'].toString()),
  lastSeenAt: json['lastSeenAt']?.toString(),
  lastSeenMessageId: json['lastSeenMessageId']?.toString(),
  role: json['role']?.toString(),
  roleText: json['roleText']?.toString(),
  unreadMessage: double.tryParse(json['unreadMessage'].toString())?.toInt(),
  updatedAt: json['updatedAt']?.toString(),
  isExceptionRule: bool.tryParse(json['isExceptionRule'].toString()),
  usernameInfo: json['usernameInfo'] == null || json['usernameInfo'] is! Map
      ? null
      : GroupChatDetailGetUserExceptionItemsUsernameInfoModel.fromJson(
          json['usernameInfo'] as Map<String, dynamic>,
        ),
);

Map<String, dynamic> _$GroupChatDetailGetUserExceptionItemsModelToJson(
  GroupChatDetailGetUserExceptionItemsModel instance,
) => <String, dynamic>{
  'conversationId': instance.conversationId,
  'username': instance.username,
  'conversationType': instance.conversationType,
  'createdAt': instance.createdAt,
  'isMute': instance.isMute,
  'isPin': instance.isPin,
  'lastSeenAt': instance.lastSeenAt,
  'lastSeenMessageId': instance.lastSeenMessageId,
  'role': instance.role,
  'roleText': instance.roleText,
  'unreadMessage': instance.unreadMessage,
  'updatedAt': instance.updatedAt,
  'isExceptionRule': instance.isExceptionRule,
  'usernameInfo': instance.usernameInfo,
};

GroupChatDetailGetUserExceptionItemsUsernameInfoModel
_$GroupChatDetailGetUserExceptionItemsUsernameInfoModelFromJson(
  Map<String, dynamic> json,
) => GroupChatDetailGetUserExceptionItemsUsernameInfoModel(
  username: json['username']?.toString(),
  name: json['name']?.toString(),
  avatar: json['avatar']?.toString(),
  id: json['id']?.toString(),
);

Map<String, dynamic>
_$GroupChatDetailGetUserExceptionItemsUsernameInfoModelToJson(
  GroupChatDetailGetUserExceptionItemsUsernameInfoModel instance,
) => <String, dynamic>{
  'username': instance.username,
  'name': instance.name,
  'avatar': instance.avatar,
  'id': instance.id,
};
