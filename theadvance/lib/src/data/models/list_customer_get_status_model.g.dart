// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'list_customer_get_status_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ListCustomerGetStatusModel _$ListCustomerGetStatusModelFromJson(
  Map<String, dynamic> json,
) => ListCustomerGetStatusModel()
  ..listCustomerStatus = (json['items'] is List)
      ? (json['items'] as List<dynamic>?)
            ?.map(
              (e) => e == null || e is! Map
                  ? null
                  : CustomerStatus.fromJson(e as Map<String, dynamic>),
            )
            .toList()
      : [];

Map<String, dynamic> _$ListCustomerGetStatusModelToJson(
  ListCustomerGetStatusModel instance,
) => <String, dynamic>{'items': instance.listCustomerStatus};

CustomerStatus _$CustomerStatusFromJson(Map<String, dynamic> json) =>
    CustomerStatus(
      count: double.tryParse(json['count'].toString())?.toInt(),
      title: json['title']?.toString(),
      status: json['status']?.toString(),
    );

Map<String, dynamic> _$CustomerStatusToJson(CustomerStatus instance) =>
    <String, dynamic>{
      'count': instance.count,
      'title': instance.title,
      'status': instance.status,
    };
