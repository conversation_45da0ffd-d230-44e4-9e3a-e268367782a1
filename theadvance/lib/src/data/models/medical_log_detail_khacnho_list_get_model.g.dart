// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'medical_log_detail_khacnho_list_get_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class MedicalLogDetailKhacnhoListGetModelAdapter
    extends TypeAdapter<MedicalLogDetailKhacnhoListGetModel> {
  @override
  final int typeId = 138;

  @override
  MedicalLogDetailKhacnhoListGetModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return MedicalLogDetailKhacnhoListGetModel()
      ..items = (fields[0] as List?)?.cast<CatalogModel?>();
  }

  @override
  void write(BinaryWriter writer, MedicalLogDetailKhacnhoListGetModel obj) {
    writer
      ..writeByte(1)
      ..writeByte(0)
      ..write(obj.items);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MedicalLogDetailKhacnhoListGetModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MedicalLogDetailKhacnhoListGetModel
_$MedicalLogDetailKhacnhoListGetModelFromJson(Map<String, dynamic> json) =>
    MedicalLogDetailKhacnhoListGetModel()
      ..items = (json['items'] is List)
          ? (json['items'] as List<dynamic>?)
                ?.map(
                  (e) => e == null || e is! Map
                      ? null
                      : CatalogModel.fromJson(e as Map<String, dynamic>),
                )
                .toList()
          : [];

Map<String, dynamic> _$MedicalLogDetailKhacnhoListGetModelToJson(
  MedicalLogDetailKhacnhoListGetModel instance,
) => <String, dynamic>{'items': instance.items};
