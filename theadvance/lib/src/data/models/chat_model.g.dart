// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'chat_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ChatModel _$ChatModelFromJson(Map<String, dynamic> json) => ChatModel(
  total: double.tryParse(json['total'].toString())?.toInt(),
  limit: double.tryParse(json['limit'].toString())?.toInt(),
  newestMessageId: json['newestMessageId']?.toString(),
  items: (json['items'] is List)
      ? (json['items'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : ChatItemsModel.fromJson(e as Map<String, dynamic>),
            )
            .toList()
      : [],
);

Map<String, dynamic> _$ChatModelToJson(ChatModel instance) => <String, dynamic>{
  'total': instance.total,
  'limit': instance.limit,
  'newestMessageId': instance.newestMessageId,
  'items': instance.items,
};

ChatItemsModel _$ChatItemsModelFromJson(
  Map<String, dynamic> json,
) => ChatItemsModel(
  id: json['id']?.toString(),
  conversationId: json['conversationId']?.toString(),
  content: json['content']?.toString(),
  createdAt: json['createdAt']?.toString(),
  isEdited: bool.tryParse(json['isEdited'].toString()),
  alowEdited: bool.tryParse(json['alowEdited'].toString()),
  createdByInfo: json['createdByInfo'] == null || json['createdByInfo'] is! Map
      ? null
      : ChatItemsCreatedByInfoModel.fromJson(
          json['createdByInfo'] as Map<String, dynamic>,
        ),
  messageForward:
      json['messageForward'] == null || json['messageForward'] is! Map
      ? null
      : ChatItemsMessageForwardModel.fromJson(
          json['messageForward'] as Map<String, dynamic>,
        ),
  messageReplyInfo:
      json['messageReplyInfo'] == null || json['messageReplyInfo'] is! Map
      ? null
      : ChatItemsMessageReplyInfoModel.fromJson(
          json['messageReplyInfo'] as Map<String, dynamic>,
        ),
  reactions: (json['reactions'] is List)
      ? (json['reactions'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : ChatItemsReactionsModel.fromJson(e as Map<String, dynamic>),
            )
            .toList()
      : [],
  replyMarkup: json['replyMarkup'] == null || json['replyMarkup'] is! Map
      ? null
      : ChatItemsReplyMarkupModel.fromJson(
          json['replyMarkup'] as Map<String, dynamic>,
        ),
  requestId: json['requestId']?.toString(),
  attachment: (json['attachment'] is List)
      ? (json['attachment'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : ChatItemsAttachmentModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
  forward: json['forward'] == null || json['forward'] is! Map
      ? null
      : ChatItemsForwardFromModel.fromJson(
          json['forward'] as Map<String, dynamic>,
        ),
  replyInfo: json['replyInfo'] == null || json['replyInfo'] is! Map
      ? null
      : ChatItemsReplyFromModel.fromJson(
          json['replyInfo'] as Map<String, dynamic>,
        ),
  mention: (json['mention'] is List)
      ? (json['mention'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : ChatItemsMentionModel.fromJson(e as Map<String, dynamic>),
            )
            .toList()
      : [],
  userSeen: (json['userSeen'] is List)
      ? (json['userSeen'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : ChatItemsUserSeenModel.fromJson(e as Map<String, dynamic>),
            )
            .toList()
      : [],
  parseMode: json['parseMode']?.toString(),
  deepLink: json['deepLink']?.toString(),
  poll: json['poll']?.toString(),
  pollInfo: json['pollInfo'] == null || json['pollInfo'] is! Map
      ? null
      : ChatItemsPollInfoModel.fromJson(
          json['pollInfo'] as Map<String, dynamic>,
        ),
  seen: bool.tryParse(json['seen'].toString()) ?? false,
  sticker: json['sticker'] == null || json['sticker'] is! Map
      ? null
      : ChatStickerModel.fromJson(json['sticker'] as Map<String, dynamic>),
  call: json['call'] == null || json['call'] is! Map
      ? null
      : ChatCallModel.fromJson(json['call'] as Map<String, dynamic>),
);

Map<String, dynamic> _$ChatItemsModelToJson(ChatItemsModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'conversationId': instance.conversationId,
      'content': instance.content,
      'createdAt': instance.createdAt,
      'isEdited': instance.isEdited,
      'alowEdited': instance.alowEdited,
      'createdByInfo': instance.createdByInfo,
      'messageForward': instance.messageForward,
      'messageReplyInfo': instance.messageReplyInfo,
      'reactions': instance.reactions,
      'replyMarkup': instance.replyMarkup,
      'requestId': instance.requestId,
      'attachment': instance.attachment,
      'forward': instance.forward,
      'replyInfo': instance.replyInfo,
      'mention': instance.mention,
      'userSeen': instance.userSeen,
      'parseMode': instance.parseMode,
      'deepLink': instance.deepLink,
      'poll': instance.poll,
      'pollInfo': instance.pollInfo,
      'seen': instance.seen,
      'sticker': instance.sticker,
      'call': instance.call,
    };

ChatCallModel _$ChatCallModelFromJson(Map<String, dynamic> json) =>
    ChatCallModel(
      callId: json['callId']?.toString(),
      from: json['from']?.toString(),
      to: json['to']?.toString(),
      answerDuration: double.tryParse(
        json['answerDuration'].toString(),
      )?.toInt(),
      createdAt: json['createdAt']?.toString(),
    );

Map<String, dynamic> _$ChatCallModelToJson(ChatCallModel instance) =>
    <String, dynamic>{
      'callId': instance.callId,
      'from': instance.from,
      'to': instance.to,
      'answerDuration': instance.answerDuration,
      'createdAt': instance.createdAt,
    };

ChatStickerModel _$ChatStickerModelFromJson(Map<String, dynamic> json) =>
    ChatStickerModel(
      stickerSetId: json['stickerSetId']?.toString(),
      id: json['id']?.toString(),
      link: json['link']?.toString(),
      size: double.tryParse(json['size'].toString())?.toInt(),
      mimetype: json['mimetype']?.toString(),
      height: double.tryParse(json['height'].toString())?.toInt(),
      width: double.tryParse(json['width'].toString())?.toInt(),
    );

Map<String, dynamic> _$ChatStickerModelToJson(ChatStickerModel instance) =>
    <String, dynamic>{
      'stickerSetId': instance.stickerSetId,
      'id': instance.id,
      'link': instance.link,
      'size': instance.size,
      'mimetype': instance.mimetype,
      'height': instance.height,
      'width': instance.width,
    };

ChatItemsCreatedByInfoModel _$ChatItemsCreatedByInfoModelFromJson(
  Map<String, dynamic> json,
) => ChatItemsCreatedByInfoModel(
  username: json['username']?.toString(),
  avatar: json['avatar']?.toString(),
  id: json['id']?.toString(),
  name: json['name']?.toString(),
  type: json['type']?.toString(),
);

Map<String, dynamic> _$ChatItemsCreatedByInfoModelToJson(
  ChatItemsCreatedByInfoModel instance,
) => <String, dynamic>{
  'username': instance.username,
  'avatar': instance.avatar,
  'id': instance.id,
  'name': instance.name,
  'type': instance.type,
};

ChatItemsMessageForwardModel _$ChatItemsMessageForwardModelFromJson(
  Map<String, dynamic> json,
) => ChatItemsMessageForwardModel();

Map<String, dynamic> _$ChatItemsMessageForwardModelToJson(
  ChatItemsMessageForwardModel instance,
) => <String, dynamic>{};

ChatItemsMessageReplyInfoModel _$ChatItemsMessageReplyInfoModelFromJson(
  Map<String, dynamic> json,
) => ChatItemsMessageReplyInfoModel();

Map<String, dynamic> _$ChatItemsMessageReplyInfoModelToJson(
  ChatItemsMessageReplyInfoModel instance,
) => <String, dynamic>{};

ChatItemsReactionsModel _$ChatItemsReactionsModelFromJson(
  Map<String, dynamic> json,
) => ChatItemsReactionsModel(
  icon: json['icon']?.toString(),
  username: json['username']?.toString(),
  reactionAt: json['reactionAt']?.toString(),
  avatar: json['avatar']?.toString(),
  name: json['name']?.toString(),
);

Map<String, dynamic> _$ChatItemsReactionsModelToJson(
  ChatItemsReactionsModel instance,
) => <String, dynamic>{
  'icon': instance.icon,
  'username': instance.username,
  'reactionAt': instance.reactionAt,
  'avatar': instance.avatar,
  'name': instance.name,
};

ChatItemsReplyMarkupModel _$ChatItemsReplyMarkupModelFromJson(
  Map<String, dynamic> json,
) => ChatItemsReplyMarkupModel(
  inlineKeyboard: json['inlineKeyboard'] as List<dynamic>,
);

Map<String, dynamic> _$ChatItemsReplyMarkupModelToJson(
  ChatItemsReplyMarkupModel instance,
) => <String, dynamic>{'inlineKeyboard': instance.inlineKeyboard};

ActionButtonModel _$ActionButtonModelFromJson(Map<String, dynamic> json) =>
    ActionButtonModel(
      text: json['text']?.toString(),
      value: json['value']?.toString(),
      url: json['url']?.toString(),
    );

Map<String, dynamic> _$ActionButtonModelToJson(ActionButtonModel instance) =>
    <String, dynamic>{
      'text': instance.text,
      'value': instance.value,
      'url': instance.url,
    };

ChatItemsAttachmentModel _$ChatItemsAttachmentModelFromJson(
  Map<String, dynamic> json,
) => ChatItemsAttachmentModel(
  link: json['link']?.toString(),
  size: double.tryParse(json['size'].toString())?.toInt(),
  originalname: json['originalname']?.toString(),
  mimetype: json['mimetype']?.toString(),
  thumbnail: json['thumbnail']?.toString(),
  width: double.tryParse(json['width'].toString())?.toInt(),
  height: double.tryParse(json['height'].toString())?.toInt(),
  duration: double.tryParse(json['duration'].toString())?.toInt(),
  waveform: (json['waveform'] is List)
      ? (json['waveform'] as List<dynamic>?)
            ?.map((e) => double.tryParse(e.toString())?.toInt())
            .toList()
      : [],
  speechToText: json['speechToText'] == null || json['speechToText'] is! Map
      ? null
      : SpeechToTextModel.fromJson(
          json['speechToText'] as Map<String, dynamic>,
        ),
  attachmentId: json['attachmentId']?.toString(),
  checksum: json['checksum']?.toString(),
);

Map<String, dynamic> _$ChatItemsAttachmentModelToJson(
  ChatItemsAttachmentModel instance,
) => <String, dynamic>{
  'link': instance.link,
  'size': instance.size,
  'originalname': instance.originalname,
  'mimetype': instance.mimetype,
  'thumbnail': instance.thumbnail,
  'width': instance.width,
  'height': instance.height,
  'duration': instance.duration,
  'waveform': instance.waveform,
  'speechToText': instance.speechToText,
  'attachmentId': instance.attachmentId,
  'checksum': instance.checksum,
};

SpeechToTextModel _$SpeechToTextModelFromJson(Map<String, dynamic> json) =>
    SpeechToTextModel(
      text: json['text']?.toString(),
      canSpeechToText: bool.tryParse(json['canSpeechToText'].toString()),
    );

Map<String, dynamic> _$SpeechToTextModelToJson(SpeechToTextModel instance) =>
    <String, dynamic>{
      'text': instance.text,
      'canSpeechToText': instance.canSpeechToText,
    };

ChatItemsForwardFromModel _$ChatItemsForwardFromModelFromJson(
  Map<String, dynamic> json,
) => ChatItemsForwardFromModel(
  createdBy: json['createdBy']?.toString(),
  content: json['content']?.toString(),
  createdAt: json['createdAt']?.toString(),
  id: json['id']?.toString(),
  createdByInfo: json['createdByInfo'] == null || json['createdByInfo'] is! Map
      ? null
      : ChatItemsForwardFromCreatedByInfoModel.fromJson(
          json['createdByInfo'] as Map<String, dynamic>,
        ),
  attachment: (json['attachment'] is List)
      ? (json['attachment'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : ChatItemsAttachmentModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
  mention: (json['mention'] is List)
      ? (json['mention'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : ChatItemsMentionModel.fromJson(e as Map<String, dynamic>),
            )
            .toList()
      : [],
  parseMode: json['parseMode']?.toString(),
  deepLink: json['deepLink']?.toString(),
  sticker: json['sticker'] == null || json['sticker'] is! Map
      ? null
      : ChatStickerModel.fromJson(json['sticker'] as Map<String, dynamic>),
);

Map<String, dynamic> _$ChatItemsForwardFromModelToJson(
  ChatItemsForwardFromModel instance,
) => <String, dynamic>{
  'createdBy': instance.createdBy,
  'content': instance.content,
  'createdAt': instance.createdAt,
  'id': instance.id,
  'createdByInfo': instance.createdByInfo,
  'attachment': instance.attachment,
  'mention': instance.mention,
  'parseMode': instance.parseMode,
  'deepLink': instance.deepLink,
  'sticker': instance.sticker,
};

ChatItemsReplyFromModel _$ChatItemsReplyFromModelFromJson(
  Map<String, dynamic> json,
) => ChatItemsReplyFromModel(
  id: json['id']?.toString(),
  content: json['content']?.toString(),
  createdBy: json['createdBy']?.toString(),
  createdAt: json['createdAt']?.toString(),
  createdByInfo: json['createdByInfo'] == null || json['createdByInfo'] is! Map
      ? null
      : ChatItemsReplyFromCreatedByInfoModel.fromJson(
          json['createdByInfo'] as Map<String, dynamic>,
        ),
  attachment: (json['attachment'] is List)
      ? (json['attachment'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : ChatItemsAttachmentModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
  sticker: json['sticker'] == null || json['sticker'] is! Map
      ? null
      : ChatStickerModel.fromJson(json['sticker'] as Map<String, dynamic>),
);

Map<String, dynamic> _$ChatItemsReplyFromModelToJson(
  ChatItemsReplyFromModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'content': instance.content,
  'createdBy': instance.createdBy,
  'createdAt': instance.createdAt,
  'createdByInfo': instance.createdByInfo,
  'attachment': instance.attachment,
  'sticker': instance.sticker,
};

ChatItemsMentionModel _$ChatItemsMentionModelFromJson(
  Map<String, dynamic> json,
) => ChatItemsMentionModel(
  username: json['username']?.toString(),
  label: json['label']?.toString(),
  text: json['text']?.toString(),
);

Map<String, dynamic> _$ChatItemsMentionModelToJson(
  ChatItemsMentionModel instance,
) => <String, dynamic>{
  'username': instance.username,
  'label': instance.label,
  'text': instance.text,
};

ChatItemsUserSeenModel _$ChatItemsUserSeenModelFromJson(
  Map<String, dynamic> json,
) => ChatItemsUserSeenModel(
  username: json['username']?.toString(),
  avatar: json['avatar']?.toString(),
  id: json['id']?.toString(),
  name: json['name']?.toString(),
);

Map<String, dynamic> _$ChatItemsUserSeenModelToJson(
  ChatItemsUserSeenModel instance,
) => <String, dynamic>{
  'username': instance.username,
  'avatar': instance.avatar,
  'id': instance.id,
  'name': instance.name,
};

ChatItemsPollInfoModel _$ChatItemsPollInfoModelFromJson(
  Map<String, dynamic> json,
) => ChatItemsPollInfoModel(
  messageId: json['messageId']?.toString(),
  conversationId: json['conversationId']?.toString(),
  title: json['title']?.toString(),
  options: (json['options'] is List)
      ? (json['options'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : ChatItemsPollInfoOptionsModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
  status: json['status']?.toString(),
  anonymous: bool.tryParse(json['anonymous'].toString()),
  multipleResponse: bool.tryParse(json['multipleResponse'].toString()),
  createdBy: json['createdBy']?.toString(),
  id: json['id']?.toString(),
  createdAt: json['createdAt']?.toString(),
  updatedAt: json['updatedAt']?.toString(),
);

Map<String, dynamic> _$ChatItemsPollInfoModelToJson(
  ChatItemsPollInfoModel instance,
) => <String, dynamic>{
  'messageId': instance.messageId,
  'conversationId': instance.conversationId,
  'title': instance.title,
  'options': instance.options,
  'status': instance.status,
  'anonymous': instance.anonymous,
  'multipleResponse': instance.multipleResponse,
  'createdBy': instance.createdBy,
  'id': instance.id,
  'createdAt': instance.createdAt,
  'updatedAt': instance.updatedAt,
};

ChatItemsForwardFromCreatedByInfoModel
_$ChatItemsForwardFromCreatedByInfoModelFromJson(Map<String, dynamic> json) =>
    ChatItemsForwardFromCreatedByInfoModel(
      username: json['username']?.toString(),
      avatar: json['avatar']?.toString(),
      id: json['id']?.toString(),
      name: json['name']?.toString(),
    );

Map<String, dynamic> _$ChatItemsForwardFromCreatedByInfoModelToJson(
  ChatItemsForwardFromCreatedByInfoModel instance,
) => <String, dynamic>{
  'username': instance.username,
  'avatar': instance.avatar,
  'id': instance.id,
  'name': instance.name,
};

ChatItemsReplyFromCreatedByInfoModel
_$ChatItemsReplyFromCreatedByInfoModelFromJson(Map<String, dynamic> json) =>
    ChatItemsReplyFromCreatedByInfoModel(
      username: json['username']?.toString(),
      avatar: json['avatar']?.toString(),
      id: json['id']?.toString(),
      name: json['name']?.toString(),
    );

Map<String, dynamic> _$ChatItemsReplyFromCreatedByInfoModelToJson(
  ChatItemsReplyFromCreatedByInfoModel instance,
) => <String, dynamic>{
  'username': instance.username,
  'avatar': instance.avatar,
  'id': instance.id,
  'name': instance.name,
};

ChatItemsPollInfoOptionsModel _$ChatItemsPollInfoOptionsModelFromJson(
  Map<String, dynamic> json,
) => ChatItemsPollInfoOptionsModel(
  text: json['text']?.toString(),
  id: json['id']?.toString(),
  members: (json['members'] is List)
      ? (json['members'] as List<dynamic>)
            .map(
              (e) => ChatItemsUserSeenModel.fromJson(e as Map<String, dynamic>),
            )
            .toList()
      : [],
);

Map<String, dynamic> _$ChatItemsPollInfoOptionsModelToJson(
  ChatItemsPollInfoOptionsModel instance,
) => <String, dynamic>{
  'text': instance.text,
  'id': instance.id,
  'members': instance.members,
};

MemberVoteListModel _$MemberVoteListModelFromJson(Map<String, dynamic> json) =>
    MemberVoteListModel(
      username: json['username']?.toString(),
      avatar: json['avatar']?.toString(),
      name: json['name']?.toString(),
    );

Map<String, dynamic> _$MemberVoteListModelToJson(
  MemberVoteListModel instance,
) => <String, dynamic>{
  'username': instance.username,
  'avatar': instance.avatar,
  'name': instance.name,
};
