// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'group_chat_detail_file_load_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GroupChatDetailFileLoadModel _$GroupChatDetailFileLoadModelFromJson(
  Map<String, dynamic> json,
) => GroupChatDetailFileLoadModel(
  total: double.tryParse(json['total'].toString())?.toInt(),
  limit: double.tryParse(json['limit'].toString())?.toInt(),
  items: (json['items'] is List)
      ? (json['items'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : GroupChatDetailFileLoadItemsModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
);

Map<String, dynamic> _$GroupChatDetailFileLoadModelToJson(
  GroupChatDetailFileLoadModel instance,
) => <String, dynamic>{
  'total': instance.total,
  'limit': instance.limit,
  'items': instance.items,
};

GroupChatDetailFileLoadItemsModel _$GroupChatDetailFileLoadItemsModelFromJson(
  Map<String, dynamic> json,
) => GroupChatDetailFileLoadItemsModel(
  conversationId: json['conversationId']?.toString(),
  messageId: json['messageId']?.toString(),
  link: json['link']?.toString(),
  size: double.tryParse(json['size'].toString())?.toInt(),
  originalname: json['originalname']?.toString(),
  mimetype: json['mimetype']?.toString(),
  createdAt: json['createdAt']?.toString(),
  updatedAt: json['updatedAt']?.toString(),
  id: json['id']?.toString(),
  thumbnail: json['thumbnail']?.toString(),
  messageInfo: json['messageInfo'] == null || json['messageInfo'] is! Map
      ? null
      : MessageInfoModel.fromJson(json['messageInfo'] as Map<String, dynamic>),
);

Map<String, dynamic> _$GroupChatDetailFileLoadItemsModelToJson(
  GroupChatDetailFileLoadItemsModel instance,
) => <String, dynamic>{
  'conversationId': instance.conversationId,
  'messageId': instance.messageId,
  'link': instance.link,
  'size': instance.size,
  'originalname': instance.originalname,
  'mimetype': instance.mimetype,
  'createdAt': instance.createdAt,
  'updatedAt': instance.updatedAt,
  'id': instance.id,
  'thumbnail': instance.thumbnail,
  'messageInfo': instance.messageInfo,
};
