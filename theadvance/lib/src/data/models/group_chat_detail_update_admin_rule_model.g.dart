// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'group_chat_detail_update_admin_rule_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GroupChatDetailUpdateAdminRuleModel
_$GroupChatDetailUpdateAdminRuleModelFromJson(Map<String, dynamic> json) =>
    GroupChatDetailUpdateAdminRuleModel(
      conversationId: json['conversationId']?.toString(),
      rules: (json['rules'] is List)
          ? (json['rules'] as List<dynamic>)
                .map(
                  (e) => e == null || e is! Map
                      ? null
                      : GroupChatDetailUpdateAdminRuleRulesModel.fromJson(
                          e as Map<String, dynamic>,
                        ),
                )
                .toList()
          : [],
      role: json['role']?.toString(),
      username: json['username']?.toString(),
      roleText: json['roleText']?.toString(),
    );

Map<String, dynamic> _$GroupChatDetailUpdateAdminRuleModelToJson(
  GroupChatDetailUpdateAdminRuleModel instance,
) => <String, dynamic>{
  'conversationId': instance.conversationId,
  'rules': instance.rules,
  'role': instance.role,
  'username': instance.username,
  'roleText': instance.roleText,
};

GroupChatDetailUpdateAdminRuleRulesModel
_$GroupChatDetailUpdateAdminRuleRulesModelFromJson(Map<String, dynamic> json) =>
    GroupChatDetailUpdateAdminRuleRulesModel(
      id: json['id']?.toString(),
      name: json['name']?.toString(),
      actions: json['actions']?.toString(),
      subject: json['subject']?.toString(),
      createdAt: json['createdAt']?.toString(),
      updatedAt: json['updatedAt']?.toString(),
    );

Map<String, dynamic> _$GroupChatDetailUpdateAdminRuleRulesModelToJson(
  GroupChatDetailUpdateAdminRuleRulesModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'actions': instance.actions,
  'subject': instance.subject,
  'createdAt': instance.createdAt,
  'updatedAt': instance.updatedAt,
};
