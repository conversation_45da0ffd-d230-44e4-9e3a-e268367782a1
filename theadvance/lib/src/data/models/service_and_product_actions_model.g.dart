// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'service_and_product_actions_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ServiceAndProductActionsModelAdapter
    extends TypeAdapter<ServiceAndProductActionsModel> {
  @override
  final int typeId = 143;

  @override
  ServiceAndProductActionsModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ServiceAndProductActionsModel()
      ..items = (fields[0] as List?)?.cast<ServiceAndProductActionModel?>();
  }

  @override
  void write(BinaryWriter writer, ServiceAndProductActionsModel obj) {
    writer
      ..writeByte(1)
      ..writeByte(0)
      ..write(obj.items);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ServiceAndProductActionsModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ServiceAndProductActionsModel _$ServiceAndProductActionsModelFromJson(
  Map<String, dynamic> json,
) => ServiceAndProductActionsModel()
  ..items = (json['items'] is List)
      ? (json['items'] as List<dynamic>?)
            ?.map(
              (e) => e == null || e is! Map
                  ? null
                  : ServiceAndProductActionModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [];

Map<String, dynamic> _$ServiceAndProductActionsModelToJson(
  ServiceAndProductActionsModel instance,
) => <String, dynamic>{'items': instance.items};

ServiceAndProductActionModel _$ServiceAndProductActionModelFromJson(
  Map<String, dynamic> json,
) => ServiceAndProductActionModel(
  action: json['action']?.toString(),
  text: json['text']?.toString(),
);

Map<String, dynamic> _$ServiceAndProductActionModelToJson(
  ServiceAndProductActionModel instance,
) => <String, dynamic>{'action': instance.action, 'text': instance.text};
