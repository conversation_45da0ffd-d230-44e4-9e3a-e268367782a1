// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'detail_crm_customer_booking_log_fetch_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DetailCrmCustomerBookingLogFetchModel
_$DetailCrmCustomerBookingLogFetchModelFromJson(Map<String, dynamic> json) =>
    DetailCrmCustomerBookingLogFetchModel(
      items: (json['items'] is List)
          ? (json['items'] as List<dynamic>)
                .map(
                  (e) => e == null || e is! Map
                      ? null
                      : DetailCrmBookingLogItemModel.fromJson(
                          e as Map<String, dynamic>,
                        ),
                )
                .toList()
          : [],
    );

Map<String, dynamic> _$DetailCrmCustomerBookingLogFetchModelToJson(
  DetailCrmCustomerBookingLogFetchModel instance,
) => <String, dynamic>{'items': instance.items};

DetailCrmBookingLogItemModel _$DetailCrmBookingLogItemModelFromJson(
  Map<String, dynamic> json,
) => DetailCrmBookingLogItemModel(
  comeDateString: json['come_date_string']?.toString(),
  comeTimeString: json['come_time_string']?.toString(),
  content: json['content']?.toString(),
  promotionName: json['promotion_name']?.toString(),
  branchName: json['branch_name']?.toString(),
  createrName: json['creater_name']?.toString(),
  bookingType: json['booking_type']?.toString(),
  bookingMethod: json['booking_method']?.toString(),
  callMethod: json['call_method']?.toString(),
  bookingFromResource: json['booking_from_resource']?.toString(),
  fromChannel: json['from_channel']?.toString(),
  createDate: json['create_date']?.toString(),
  serviceArr: (json['service_arr'] is List)
      ? (json['service_arr'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : DetailCrmBookingLogServiceArrModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
  bookingId: double.tryParse(json['booking_id'].toString())?.toInt(),
  bookingStatus: json['booking_status']?.toString(),
  comeStatus: bool.tryParse(json['come_status'].toString()),
);

Map<String, dynamic> _$DetailCrmBookingLogItemModelToJson(
  DetailCrmBookingLogItemModel instance,
) => <String, dynamic>{
  'come_date_string': instance.comeDateString,
  'come_time_string': instance.comeTimeString,
  'content': instance.content,
  'promotion_name': instance.promotionName,
  'branch_name': instance.branchName,
  'creater_name': instance.createrName,
  'booking_type': instance.bookingType,
  'booking_method': instance.bookingMethod,
  'call_method': instance.callMethod,
  'booking_from_resource': instance.bookingFromResource,
  'from_channel': instance.fromChannel,
  'create_date': instance.createDate,
  'service_arr': instance.serviceArr,
  'booking_id': instance.bookingId,
  'booking_status': instance.bookingStatus,
  'come_status': instance.comeStatus,
};

DetailCrmBookingLogServiceArrModel _$DetailCrmBookingLogServiceArrModelFromJson(
  Map<String, dynamic> json,
) => DetailCrmBookingLogServiceArrModel(
  serviceName: json['service_name']?.toString(),
  serviceStatus: json['service_status']?.toString(),
  bookingStatus: json['booking_status']?.toString(),
  status: json['status']?.toString(),
);

Map<String, dynamic> _$DetailCrmBookingLogServiceArrModelToJson(
  DetailCrmBookingLogServiceArrModel instance,
) => <String, dynamic>{
  'service_name': instance.serviceName,
  'service_status': instance.serviceStatus,
  'booking_status': instance.bookingStatus,
  'status': instance.status,
};
