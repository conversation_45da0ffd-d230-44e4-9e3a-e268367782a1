// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'detail_crm_customer_advice_type_fetch_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DetailCrmCustomerAdviceTypeFetchModel
_$DetailCrmCustomerAdviceTypeFetchModelFromJson(Map<String, dynamic> json) =>
    DetailCrmCustomerAdviceTypeFetchModel(
      items:
          ((json['items'] is List)
              ? (json['items'] as List<dynamic>?)
                    ?.map(
                      (e) => e == null || e is! Map
                          ? null
                          : CrmAdviceTypeModel.fromJson(
                              e as Map<String, dynamic>,
                            ),
                    )
                    .toList()
              : []) ??
          const [],
    );

Map<String, dynamic> _$DetailCrmCustomerAdviceTypeFetchModelToJson(
  DetailCrmCustomerAdviceTypeFetchModel instance,
) => <String, dynamic>{'items': instance.items};

CrmAdviceTypeModel _$CrmAdviceTypeModelFromJson(Map<String, dynamic> json) =>
    CrmAdviceTypeModel(
      id: json['id']?.toString(),
      name: json['name']?.toString(),
    );

Map<String, dynamic> _$CrmAdviceTypeModelToJson(CrmAdviceTypeModel instance) =>
    <String, dynamic>{'id': instance.id, 'name': instance.name};
