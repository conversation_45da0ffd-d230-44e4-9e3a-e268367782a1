// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'taking_care_customer_get_treatment_photo_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TakingCareCustomerGetTreatmentPhotoModel
_$TakingCareCustomerGetTreatmentPhotoModelFromJson(Map<String, dynamic> json) =>
    TakingCareCustomerGetTreatmentPhotoModel(
      items: (json['items'] is List)
          ? (json['items'] as List<dynamic>)
                .map(
                  (e) => e == null || e is! Map
                      ? null
                      : TakingCareCustomerGetTreatmentPhotoItemsModel.fromJson(
                          e as Map<String, dynamic>,
                        ),
                )
                .toList()
          : [],
    );

Map<String, dynamic> _$TakingCareCustomerGetTreatmentPhotoModelToJson(
  TakingCareCustomerGetTreatmentPhotoModel instance,
) => <String, dynamic>{'items': instance.items};

TakingCareCustomerGetTreatmentPhotoItemsModel
_$TakingCareCustomerGetTreatmentPhotoItemsModelFromJson(
  Map<String, dynamic> json,
) => TakingCareCustomerGetTreatmentPhotoItemsModel(
  createDate: json['CreateDate']?.toString(),
  detail: (json['Detail'] is List)
      ? (json['Detail'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : TakingCareCustomerGetTreatmentPhotoItemsDetailModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
);

Map<String, dynamic> _$TakingCareCustomerGetTreatmentPhotoItemsModelToJson(
  TakingCareCustomerGetTreatmentPhotoItemsModel instance,
) => <String, dynamic>{
  'CreateDate': instance.createDate,
  'Detail': instance.detail,
};

TakingCareCustomerGetTreatmentPhotoItemsDetailModel
_$TakingCareCustomerGetTreatmentPhotoItemsDetailModelFromJson(
  Map<String, dynamic> json,
) => TakingCareCustomerGetTreatmentPhotoItemsDetailModel(
  id: double.tryParse(json['ID'].toString())?.toInt(),
  treatmentRecordID: double.tryParse(
    json['TreatmentRecordID'].toString(),
  )?.toInt(),
  treatmentRecordDetailID: double.tryParse(
    json['TreatmentRecordDetailID'].toString(),
  )?.toInt(),
  filePhoto: json['FilePhoto']?.toString(),
  createDate: json['CreateDate']?.toString(),
);

Map<String, dynamic>
_$TakingCareCustomerGetTreatmentPhotoItemsDetailModelToJson(
  TakingCareCustomerGetTreatmentPhotoItemsDetailModel instance,
) => <String, dynamic>{
  'ID': instance.id,
  'TreatmentRecordID': instance.treatmentRecordID,
  'TreatmentRecordDetailID': instance.treatmentRecordDetailID,
  'FilePhoto': instance.filePhoto,
  'CreateDate': instance.createDate,
};
