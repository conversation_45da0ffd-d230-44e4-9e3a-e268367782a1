// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'detail_crm_customer_room_load_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DetailCrmCustomerRoomLoadModel _$DetailCrmCustomerRoomLoadModelFromJson(
  Map<String, dynamic> json,
) => DetailCrmCustomerRoomLoadModel(
  items: (json['items'] is List)
      ? (json['items'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : DetailCrmCustomerRoomLoadItemsModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
);

Map<String, dynamic> _$DetailCrmCustomerRoomLoadModelToJson(
  DetailCrmCustomerRoomLoadModel instance,
) => <String, dynamic>{'items': instance.items};

DetailCrmCustomerRoomLoadItemsModel
_$DetailCrmCustomerRoomLoadItemsModelFromJson(Map<String, dynamic> json) =>
    DetailCrmCustomerRoomLoadItemsModel(
      code: json['code']?.toString(),
      name: json['name']?.toString(),
    );

Map<String, dynamic> _$DetailCrmCustomerRoomLoadItemsModelToJson(
  DetailCrmCustomerRoomLoadItemsModel instance,
) => <String, dynamic>{'code': instance.code, 'name': instance.name};
