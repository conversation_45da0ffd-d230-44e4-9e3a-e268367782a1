// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ticket_active_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TicketActiveModel _$TicketActiveModelFromJson(Map<String, dynamic> json) =>
    TicketActiveModel(
      docs: (json['docs'] is List)
          ? (json['docs'] as List<dynamic>)
                .map(
                  (e) => e == null || e is! Map
                      ? null
                      : TicketActiveItemsModel.fromJson(
                          e as Map<String, dynamic>,
                        ),
                )
                .toList()
          : [],
    );

Map<String, dynamic> _$TicketActiveModelToJson(TicketActiveModel instance) =>
    <String, dynamic>{'docs': instance.docs};

TicketActiveItemsModel _$TicketActiveItemsModelFromJson(
  Map<String, dynamic> json,
) => TicketActiveItemsModel(
  id: json['id']?.toString(),
  action: json['action']?.toString(),
  actionDisplay: json['actionDisplay']?.toString(),
  createdAt: json['createdAt']?.toString(),
  createdBy: json['createdBy']?.toString(),
  content: json['content']?.toString(),
  reason: json['reason']?.toString(),
  isFollowProblem: bool.tryParse(json['isFollowProblem'].toString()),
  attachments: (json['attachments'] is List)
      ? (json['attachments'] as List<dynamic>)
            .map((e) => AttachmentModel.fromJson(e as Map<String, dynamic>))
            .toList()
      : [],
  mentions: (json['mentions'] is List)
      ? (json['mentions'] as List<dynamic>?)
            ?.map((e) => MentionModel.fromJson(e as Map<String, dynamic>))
            .toList()
      : [],
  assigneeDisplay: json['assigneeDisplay']?.toString(),
);

Map<String, dynamic> _$TicketActiveItemsModelToJson(
  TicketActiveItemsModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'createdAt': instance.createdAt,
  'action': instance.action,
  'actionDisplay': instance.actionDisplay,
  'createdBy': instance.createdBy,
  'content': instance.content,
  'reason': instance.reason,
  'isFollowProblem': instance.isFollowProblem,
  'attachments': instance.attachments,
  'mentions': instance.mentions,
  'assigneeDisplay': instance.assigneeDisplay,
};
