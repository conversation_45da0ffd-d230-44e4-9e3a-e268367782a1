// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'service_detail_employee_fetch_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ServiceDetailEmployeeFetchModel _$ServiceDetailEmployeeFetchModelFromJson(
  Map<String, dynamic> json,
) => ServiceDetailEmployeeFetchModel(
  total: double.tryParse(json['total'].toString())?.toInt(),
  limit: double.tryParse(json['limit'].toString())?.toInt(),
  page: double.tryParse(json['page'].toString())?.toInt(),
  items: (json['items'] is List)
      ? (json['items'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : ServiceDetailEmployeeFetchItemsModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
);

Map<String, dynamic> _$ServiceDetailEmployeeFetchModelToJson(
  ServiceDetailEmployeeFetchModel instance,
) => <String, dynamic>{
  'total': instance.total,
  'limit': instance.limit,
  'page': instance.page,
  'items': instance.items,
};

ServiceDetailEmployeeFetchItemsModel
_$ServiceDetailEmployeeFetchItemsModelFromJson(Map<String, dynamic> json) =>
    ServiceDetailEmployeeFetchItemsModel(
      name: json['name']?.toString(),
      employeeId: json['employee_id']?.toString(),
    );

Map<String, dynamic> _$ServiceDetailEmployeeFetchItemsModelToJson(
  ServiceDetailEmployeeFetchItemsModel instance,
) => <String, dynamic>{
  'name': instance.name,
  'employee_id': instance.employeeId,
};
