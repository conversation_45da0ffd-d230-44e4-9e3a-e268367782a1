// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'detail_crm_customer_booking_detail_load_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DetailCrmCustomerBookingDetailLoadModel
_$DetailCrmCustomerBookingDetailLoadModelFromJson(Map<String, dynamic> json) =>
    DetailCrmCustomerBookingDetailLoadModel(
      items: (json['items'] is List)
          ? (json['items'] as List<dynamic>)
                .map(
                  (e) => e == null || e is! Map
                      ? null
                      : DetailCrmCustomerBookingDetailLoadItemsModel.fromJson(
                          e as Map<String, dynamic>,
                        ),
                )
                .toList()
          : [],
    );

Map<String, dynamic> _$DetailCrmCustomerBookingDetailLoadModelToJson(
  DetailCrmCustomerBookingDetailLoadModel instance,
) => <String, dynamic>{'items': instance.items};

DetailCrmCustomerBookingDetailLoadItemsModel
_$DetailCrmCustomerBookingDetailLoadItemsModelFromJson(
  Map<String, dynamic> json,
) => DetailCrmCustomerBookingDetailLoadItemsModel(
  detailId: double.tryParse(json['detail_id'].toString())?.toInt(),
  serviceId: double.tryParse(json['service_id'].toString())?.toInt(),
  serviceName: json['service_name']?.toString(),
  bookingId: double.tryParse(json['booking_id'].toString())?.toInt(),
  treatmentId: json['treatment_id'],
  isCancel: bool.tryParse(json['is_cancel'].toString()),
  detailDesc: json['detail_desc']?.toString(),
  isMap: double.tryParse(json['is_map'].toString())?.toInt(),
  isTreatment: bool.tryParse(json['is_treatment'].toString()),
);

Map<String, dynamic> _$DetailCrmCustomerBookingDetailLoadItemsModelToJson(
  DetailCrmCustomerBookingDetailLoadItemsModel instance,
) => <String, dynamic>{
  'detail_id': instance.detailId,
  'service_id': instance.serviceId,
  'service_name': instance.serviceName,
  'booking_id': instance.bookingId,
  'treatment_id': instance.treatmentId,
  'is_cancel': instance.isCancel,
  'detail_desc': instance.detailDesc,
  'is_map': instance.isMap,
  'is_treatment': instance.isTreatment,
};
