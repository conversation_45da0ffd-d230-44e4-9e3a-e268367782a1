// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'detail_crm_customer_promotion_load_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DetailCrmCustomerPromotionLoadModel
_$DetailCrmCustomerPromotionLoadModelFromJson(Map<String, dynamic> json) =>
    DetailCrmCustomerPromotionLoadModel(
      items: (json['items'] is List)
          ? (json['items'] as List<dynamic>)
                .map(
                  (e) => e == null || e is! Map
                      ? null
                      : DetailCrmCustomerPromotionLoadItemsModel.fromJson(
                          e as Map<String, dynamic>,
                        ),
                )
                .toList()
          : [],
    );

Map<String, dynamic> _$DetailCrmCustomerPromotionLoadModelToJson(
  DetailCrmCustomerPromotionLoadModel instance,
) => <String, dynamic>{'items': instance.items};

DetailCrmCustomerPromotionLoadItemsModel
_$DetailCrmCustomerPromotionLoadItemsModelFromJson(Map<String, dynamic> json) =>
    DetailCrmCustomerPromotionLoadItemsModel(
      id: double.tryParse(json['id'].toString())?.toInt(),
      name: json['name']?.toString(),
    );

Map<String, dynamic> _$DetailCrmCustomerPromotionLoadItemsModelToJson(
  DetailCrmCustomerPromotionLoadItemsModel instance,
) => <String, dynamic>{'id': instance.id, 'name': instance.name};
