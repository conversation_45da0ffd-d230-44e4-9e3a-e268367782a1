// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'customer_info_details_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CustomerInfoDetailsModel _$CustomerInfoDetailsModelFromJson(
  Map<String, dynamic> json,
) => CustomerInfoDetailsModel()
  ..customerId = json['customer_id']?.toString()
  ..customerCode = json['customer_code']?.toString()
  ..group = json['group']?.toString()
  ..customerName = json['customer_name']?.toString()
  ..avatar = json['avatar']?.toString()
  ..branchCode = json['branch_code']?.toString()
  ..floor = json['floor']?.toString()
  ..bookingTime = json['booking_time']?.toString()
  ..checkInTime = json['check_in_time']?.toString()
  ..checkInNumber = json['check_in_number']?.toString()
  ..checkInId = double.tryParse(json['check_in_id'].toString())?.toInt()
  ..estimateTime = double.tryParse(json['estimate_time'].toString())?.toInt()
  ..processingTime = double.tryParse(
    json['processing_time'].toString(),
  )?.toInt()
  ..estimateText = json['estimate_text']?.toString()
  ..highlight = bool.tryParse(json['highlight'].toString())
  ..status = json['status']?.toString()
  ..floorCode = json['floor_code']?.toString()
  ..floorId = json['floor_id']?.toString()
  ..roomCode = json['room_code']?.toString()
  ..bedCode = json['bed_code']?.toString()
  ..bedId = json['bed_id']?.toString()
  ..note = json['note']?.toString()
  ..visitCount = double.tryParse(json['visit_count'].toString())?.toInt()
  ..averageRevenue = json['average_revenue']?.toString()
  ..totalRevenue = json['total_revenue']?.toString()
  ..accountBalance = json['account_balance']?.toString()
  ..isCheckout = bool.tryParse(json['is_checkout'].toString())
  ..features = (json['features'] is List)
      ? (json['features'] as List<dynamic>?)
            ?.map(
              (e) => e == null || e is! Map
                  ? null
                  : CustomerInfoDetailsFeature.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [];

Map<String, dynamic> _$CustomerInfoDetailsModelToJson(
  CustomerInfoDetailsModel instance,
) => <String, dynamic>{
  'customer_id': instance.customerId,
  'customer_code': instance.customerCode,
  'group': instance.group,
  'customer_name': instance.customerName,
  'avatar': instance.avatar,
  'branch_code': instance.branchCode,
  'floor': instance.floor,
  'booking_time': instance.bookingTime,
  'check_in_time': instance.checkInTime,
  'check_in_number': instance.checkInNumber,
  'check_in_id': instance.checkInId,
  'estimate_time': instance.estimateTime,
  'processing_time': instance.processingTime,
  'estimate_text': instance.estimateText,
  'highlight': instance.highlight,
  'status': instance.status,
  'floor_code': instance.floorCode,
  'floor_id': instance.floorId,
  'room_code': instance.roomCode,
  'bed_code': instance.bedCode,
  'bed_id': instance.bedId,
  'note': instance.note,
  'visit_count': instance.visitCount,
  'average_revenue': instance.averageRevenue,
  'total_revenue': instance.totalRevenue,
  'account_balance': instance.accountBalance,
  'is_checkout': instance.isCheckout,
  'features': instance.features,
};

CustomerInfoDetailsFeature _$CustomerInfoDetailsFeatureFromJson(
  Map<String, dynamic> json,
) => CustomerInfoDetailsFeature(
  code: json['code']?.toString(),
  title: json['title']?.toString(),
);

Map<String, dynamic> _$CustomerInfoDetailsFeatureToJson(
  CustomerInfoDetailsFeature instance,
) => <String, dynamic>{'code': instance.code, 'title': instance.title};
