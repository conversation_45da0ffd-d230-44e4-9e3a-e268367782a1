// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'get_consultation_ndtv_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GetConsultationNDTVModel _$GetConsultationNDTVModelFromJson(
  Map<String, dynamic> json,
) => GetConsultationNDTVModel(
  ddkh: (json['ddkh'] is List)
      ? (json['ddkh'] as List<dynamic>?)
            ?.map(
              (e) => ConsultationTTBDFieldsModel.fromJson(
                e as Map<String, dynamic>,
              ),
            )
            .toList()
      : [],
  sothich: (json['sothich'] is List)
      ? (json['sothich'] as List<dynamic>?)
            ?.map(
              (e) => ConsultationTTBDFieldsModel.fromJson(
                e as Map<String, dynamic>,
              ),
            )
            .toList()
      : [],
  nguoiDiCung: (json['nguoiDiCung'] is List)
      ? (json['nguoiDiCung'] as List<dynamic>?)
            ?.map(
              (e) => ConsultationTTBDFieldsModel.fromJson(
                e as Map<String, dynamic>,
              ),
            )
            .toList()
      : [],
  benhLy: (json['benhLy'] is List)
      ? (json['benhLy'] as List<dynamic>?)
            ?.map(
              (e) => ConsultationTTBDFieldsModel.fromJson(
                e as Map<String, dynamic>,
              ),
            )
            .toList()
      : [],
);

ConsultationTTBDFieldsModel _$ConsultationTTBDFieldsModelFromJson(
  Map<String, dynamic> json,
) => ConsultationTTBDFieldsModel(
  json['title']?.toString(),
  json['type']?.toString(),
  (json['values'] is List)
      ? (json['values'] as List<dynamic>?)?.map((e) => e as String).toList()
      : [],
);
