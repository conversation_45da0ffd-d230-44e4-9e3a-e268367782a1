// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'emoji_list_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

EmojiListModel _$EmojiListModelFromJson(Map<String, dynamic> json) =>
    EmojiListModel(
      total: double.tryParse(json['total'].toString())?.toInt(),
      limit: double.tryParse(json['limit'].toString())?.toInt(),
      page: double.tryParse(json['page'].toString())?.toInt(),
      items: (json['items'] is List)
          ? (json['items'] as List<dynamic>)
                .map(
                  (e) => e == null || e is! Map
                      ? null
                      : EmojiListItemsModel.fromJson(e as Map<String, dynamic>),
                )
                .toList()
          : [],
    );

Map<String, dynamic> _$EmojiListModelToJson(EmojiListModel instance) =>
    <String, dynamic>{
      'total': instance.total,
      'limit': instance.limit,
      'page': instance.page,
      'items': instance.items,
    };

EmojiListItemsModel _$EmojiListItemsModelFromJson(Map<String, dynamic> json) =>
    EmojiListItemsModel(
      text: json['text']?.toString(),
      icon: json['icon']?.toString(),
      createdAt: json['createdAt']?.toString(),
      updatedAt: json['updatedAt']?.toString(),
    );

Map<String, dynamic> _$EmojiListItemsModelToJson(
  EmojiListItemsModel instance,
) => <String, dynamic>{
  'text': instance.text,
  'icon': instance.icon,
  'createdAt': instance.createdAt,
  'updatedAt': instance.updatedAt,
};
