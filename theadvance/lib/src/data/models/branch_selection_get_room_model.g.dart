// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'branch_selection_get_room_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class BranchSelectionGetRoomModelAdapter
    extends TypeAdapter<BranchSelectionGetRoomModel> {
  @override
  final int typeId = 125;

  @override
  BranchSelectionGetRoomModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return BranchSelectionGetRoomModel()
      ..items = (fields[0] as List?)?.cast<RoomModel?>();
  }

  @override
  void write(BinaryWriter writer, BranchSelectionGetRoomModel obj) {
    writer
      ..writeByte(1)
      ..writeByte(0)
      ..write(obj.items);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BranchSelectionGetRoomModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BranchSelectionGetRoomModel _$BranchSelectionGetRoomModelFromJson(
  Map<String, dynamic> json,
) => BranchSelectionGetRoomModel()
  ..items = (json['items'] is List)
      ? (json['items'] as List<dynamic>?)
            ?.map(
              (e) => e == null || e is! Map
                  ? null
                  : RoomModel.fromJson(e as Map<String, dynamic>),
            )
            .toList()
      : [];

Map<String, dynamic> _$BranchSelectionGetRoomModelToJson(
  BranchSelectionGetRoomModel instance,
) => <String, dynamic>{'items': instance.items};

RoomModel _$RoomModelFromJson(Map<String, dynamic> json) => RoomModel(
  text: json['text']?.toString(),
  id: json['id']?.toString(),
  code: json['code']?.toString(),
  available: double.tryParse(json['available'].toString())?.toInt(),
  availableBed: json['available_bed']?.toString(),
);

Map<String, dynamic> _$RoomModelToJson(RoomModel instance) => <String, dynamic>{
  'text': instance.text,
  'code': instance.code,
  'id': instance.id,
  'available': instance.available,
  'available_bed': instance.availableBed,
};
