// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'service_and_product_get_category_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ServiceAndProductGetCategoryModel _$ServiceAndProductGetCategoryModelFromJson(
  Map<String, dynamic> json,
) =>
    ServiceAndProductGetCategoryModel()
      ..listServiceAndProductCategory = (json['items'] is List)
          ? (json['items'] as List<dynamic>?)
                ?.map(
                  (e) => e == null || e is! Map
                      ? null
                      : ServiceAndProductCategoryModel.fromJson(
                          e as Map<String, dynamic>,
                        ),
                )
                .toList()
          : [];

Map<String, dynamic> _$ServiceAndProductGetCategoryModelToJson(
  ServiceAndProductGetCategoryModel instance,
) => <String, dynamic>{'items': instance.listServiceAndProductCategory};

ServiceAndProductCategoryModel _$ServiceAndProductCategoryModelFromJson(
  Map<String, dynamic> json,
) => ServiceAndProductCategoryModel(
  title: json['title']?.toString(),
  type: json['type']?.toString(),
);

Map<String, dynamic> _$ServiceAndProductCategoryModelToJson(
  ServiceAndProductCategoryModel instance,
) => <String, dynamic>{'title': instance.title, 'type': instance.type};
