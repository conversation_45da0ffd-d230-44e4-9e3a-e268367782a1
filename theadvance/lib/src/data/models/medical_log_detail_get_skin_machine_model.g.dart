// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'medical_log_detail_get_skin_machine_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MedicalLogDetailGetSkinMachineModel
_$MedicalLogDetailGetSkinMachineModelFromJson(Map<String, dynamic> json) =>
    MedicalLogDetailGetSkinMachineModel()
      ..listSkinMachine = (json['items'] is List)
          ? (json['items'] as List<dynamic>?)
                ?.map(
                  (e) => e == null || e is! Map
                      ? null
                      : CatalogModel.fromJson(e as Map<String, dynamic>),
                )
                .toList()
          : [];

Map<String, dynamic> _$MedicalLogDetailGetSkinMachineModelToJson(
  MedicalLogDetailGetSkinMachineModel instance,
) => <String, dynamic>{'items': instance.listSkinMachine};

SkinMachine _$SkinMachineFromJson(Map<String, dynamic> json) => SkinMachine(
  id: json['id']?.toString(),
  name: json['name']?.toString(),
  text: json['text']?.toString(),
  types: json['types']?.toString(),
  departmentCode: json['department_code']?.toString(),
);

Map<String, dynamic> _$SkinMachineToJson(SkinMachine instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'text': instance.text,
      'types': instance.types,
      'department_code': instance.departmentCode,
    };
