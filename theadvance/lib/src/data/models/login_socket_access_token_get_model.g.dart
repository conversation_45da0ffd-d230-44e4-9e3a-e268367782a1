// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'login_socket_access_token_get_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LoginSocketAccessTokenGetModel _$LoginSocketAccessTokenGetModelFromJson(
  Map<String, dynamic> json,
) => LoginSocketAccessTokenGetModel(
  accessToken: json['accessToken']?.toString(),
  user: json['user'] == null || json['user'] is! Map
      ? null
      : LoginSocketAccessTokenGetUserModel.fromJson(
          json['user'] as Map<String, dynamic>,
        ),
);

Map<String, dynamic> _$LoginSocketAccessTokenGetModelToJson(
  LoginSocketAccessTokenGetModel instance,
) => <String, dynamic>{
  'accessToken': instance.accessToken,
  'user': instance.user,
};

LoginSocketAccessTokenGetUserModel _$LoginSocketAccessTokenGetUserModelFromJson(
  Map<String, dynamic> json,
) => LoginSocketAccessTokenGetUserModel(
  username: json['username']?.toString(),
  avatar: json['avatar']?.toString(),
  createdAt: json['createdAt']?.toString(),
  departmentName: json['departmentName']?.toString(),
  id: json['id']?.toString(),
  isOnline: bool.tryParse(json['isOnline'].toString()),
  name: json['name']?.toString(),
  phone: json['phone']?.toString(),
  product: json['product']?.toString(),
  takeCareGroupId: json['takeCareGroupId']?.toString(),
  type: json['type']?.toString(),
  updatedAt: json['updatedAt']?.toString(),
  botMessagePermission:
      json['botMessagePermission'] == null ||
          json['botMessagePermission'] is! Map
      ? null
      : LoginSocketAccessTokenGetUserBotMessagePermissionModel.fromJson(
          json['botMessagePermission'] as Map<String, dynamic>,
        ),
  permission: json['permission'] == null || json['permission'] is! Map
      ? null
      : ChatPermissionModel.fromJson(
          json['permission'] as Map<String, dynamic>,
        ),
);

Map<String, dynamic> _$LoginSocketAccessTokenGetUserModelToJson(
  LoginSocketAccessTokenGetUserModel instance,
) => <String, dynamic>{
  'username': instance.username,
  'avatar': instance.avatar,
  'createdAt': instance.createdAt,
  'departmentName': instance.departmentName,
  'id': instance.id,
  'isOnline': instance.isOnline,
  'name': instance.name,
  'phone': instance.phone,
  'product': instance.product,
  'takeCareGroupId': instance.takeCareGroupId,
  'type': instance.type,
  'updatedAt': instance.updatedAt,
  'botMessagePermission': instance.botMessagePermission,
  'permission': instance.permission,
};

ChatPermissionModel _$ChatPermissionModelFromJson(Map<String, dynamic> json) =>
    ChatPermissionModel(
      record: json['record'] == null || json['record'] is! Map
          ? null
          : RecordPermissionModel.fromJson(
              json['record'] as Map<String, dynamic>,
            ),
      botMessage: json['botMessage'] == null || json['botMessage'] is! Map
          ? null
          : LoginSocketAccessTokenGetUserBotMessagePermissionModel.fromJson(
              json['botMessage'] as Map<String, dynamic>,
            ),
      message: json['message'] == null || json['message'] is! Map
          ? null
          : LoginSocketAccessTokenGetUserMessagePermissionModel.fromJson(
              json['message'] as Map<String, dynamic>,
            ),
    );

Map<String, dynamic> _$ChatPermissionModelToJson(
  ChatPermissionModel instance,
) => <String, dynamic>{
  'record': instance.record,
  'botMessage': instance.botMessage,
  'message': instance.message,
};

RecordPermissionModel _$RecordPermissionModelFromJson(
  Map<String, dynamic> json,
) =>
    RecordPermissionModel(download: bool.tryParse(json['download'].toString()));

Map<String, dynamic> _$RecordPermissionModelToJson(
  RecordPermissionModel instance,
) => <String, dynamic>{'download': instance.download};

LoginSocketAccessTokenGetUserBotMessagePermissionModel
_$LoginSocketAccessTokenGetUserBotMessagePermissionModelFromJson(
  Map<String, dynamic> json,
) => LoginSocketAccessTokenGetUserBotMessagePermissionModel(
  edit: bool.tryParse(json['edit'].toString()),
  copy: bool.tryParse(json['copy'].toString()),
  forward: bool.tryParse(json['forward'].toString()),
  speechToText: bool.tryParse(json['speechToText'].toString()),
);

Map<String, dynamic>
_$LoginSocketAccessTokenGetUserBotMessagePermissionModelToJson(
  LoginSocketAccessTokenGetUserBotMessagePermissionModel instance,
) => <String, dynamic>{
  'edit': instance.edit,
  'copy': instance.copy,
  'forward': instance.forward,
  'speechToText': instance.speechToText,
};

LoginSocketAccessTokenGetUserMessagePermissionModel
_$LoginSocketAccessTokenGetUserMessagePermissionModelFromJson(
  Map<String, dynamic> json,
) => LoginSocketAccessTokenGetUserMessagePermissionModel(
  speechToText: bool.tryParse(json['speechToText'].toString()),
);

Map<String, dynamic>
_$LoginSocketAccessTokenGetUserMessagePermissionModelToJson(
  LoginSocketAccessTokenGetUserMessagePermissionModel instance,
) => <String, dynamic>{'speechToText': instance.speechToText};
