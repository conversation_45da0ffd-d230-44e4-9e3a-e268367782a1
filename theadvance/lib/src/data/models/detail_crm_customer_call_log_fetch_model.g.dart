// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'detail_crm_customer_call_log_fetch_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DetailCrmCustomerCallLogFetchModel _$DetailCrmCustomerCallLogFetchModelFromJson(
  Map<String, dynamic> json,
) => DetailCrmCustomerCallLogFetchModel(
  items: (json['items'] is List)
      ? (json['items'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : DetailCrmCallLogItemModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
);

Map<String, dynamic> _$DetailCrmCustomerCallLogFetchModelToJson(
  DetailCrmCustomerCallLogFetchModel instance,
) => <String, dynamic>{'items': instance.items};

DetailCrmCallLogItemModel _$DetailCrmCallLogItemModelFromJson(
  Map<String, dynamic> json,
) => DetailCrmCallLogItemModel(
  eventId: json['event_id']?.toString(),
  groupName: json['group_name']?.toString(),
  userName: json['user_name']?.toString(),
  status: json['status']?.toString(),
  callDate: json['call_date']?.toString(),
  callTime: json['call_time']?.toString(),
  type: json['type']?.toString(),
);

Map<String, dynamic> _$DetailCrmCallLogItemModelToJson(
  DetailCrmCallLogItemModel instance,
) => <String, dynamic>{
  'event_id': instance.eventId,
  'group_name': instance.groupName,
  'user_name': instance.userName,
  'status': instance.status,
  'call_date': instance.callDate,
  'call_time': instance.callTime,
  'type': instance.type,
};
