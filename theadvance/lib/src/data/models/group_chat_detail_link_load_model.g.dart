// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'group_chat_detail_link_load_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GroupChatDetailLinkLoadModel _$GroupChatDetailLinkLoadModelFromJson(
  Map<String, dynamic> json,
) => GroupChatDetailLinkLoadModel(
  limit: double.tryParse(json['limit'].toString())?.toInt(),
  total: double.tryParse(json['total'].toString())?.toInt(),
  items: (json['items'] is List)
      ? (json['items'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : GroupChatDetailLinkLoadItemsModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
);

Map<String, dynamic> _$GroupChatDetailLinkLoadModelToJson(
  GroupChatDetailLinkLoadModel instance,
) => <String, dynamic>{
  'limit': instance.limit,
  'total': instance.total,
  'items': instance.items,
};

GroupChatDetailLinkLoadItemsModel _$GroupChatDetailLinkLoadItemsModelFromJson(
  Map<String, dynamic> json,
) => GroupChatDetailLinkLoadItemsModel(
  id: json['id']?.toString(),
  createdAt: json['createdAt']?.toString(),
  conversationId: json['conversationId']?.toString(),
  links: (json['links'] is List)
      ? (json['links'] as List<dynamic>).map((e) => e?.toString()).toList()
      : [],
);

Map<String, dynamic> _$GroupChatDetailLinkLoadItemsModelToJson(
  GroupChatDetailLinkLoadItemsModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'createdAt': instance.createdAt,
  'conversationId': instance.conversationId,
  'links': instance.links,
};
