// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'poll_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PollModel _$PollModelFromJson(Map<String, dynamic> json) => PollModel(
  title: json['title']?.toString(),
  options: (json['options'] is List)
      ? (json['options'] as List<dynamic>?)
            ?.map(
              (e) => PollOptionEntiesModel.fromJson(e as Map<String, dynamic>),
            )
            .toList()
      : [],
  voteCount: double.tryParse(json['voteCount'].toString())?.toInt(),
  isAnonymous: bool.tryParse(json['isAnonymous'].toString()),
  isMultiple: bool.tryParse(json['isMultiple'].toString()),
  voteList: (json['voteList'] is List)
      ? (json['voteList'] as List<dynamic>?)
            ?.map((e) => VoteItemModel.fromJson(e as Map<String, dynamic>))
            .toList()
      : [],
);

Map<String, dynamic> _$PollModelToJson(PollModel instance) => <String, dynamic>{
  'title': instance.title,
  'options': instance.options,
  'voteCount': instance.voteCount,
  'isAnonymous': instance.isAnonymous,
  'isMultiple': instance.isMultiple,
  'voteList': instance.voteList,
};

PollOptionEntiesModel _$PollOptionEntiesModelFromJson(
  Map<String, dynamic> json,
) => PollOptionEntiesModel(
  text: json['text']?.toString(),
  count: double.tryParse(json['count'].toString())?.toInt(),
  voteId: json['voteId']?.toString(),
  status: json['status']?.toString(),
);

Map<String, dynamic> _$PollOptionEntiesModelToJson(
  PollOptionEntiesModel instance,
) => <String, dynamic>{
  'text': instance.text,
  'count': instance.count,
  'voteId': instance.voteId,
  'status': instance.status,
};

VoteItemModel _$VoteItemModelFromJson(Map<String, dynamic> json) =>
    VoteItemModel(
      voteId: json['voteId']?.toString(),
      createdBy: json['createdBy']?.toString(),
      voteUserInfo: json['voteUserInfo'] == null || json['voteUserInfo'] is! Map
          ? null
          : CreatedByInfoModel.fromJson(
              json['voteUserInfo'] as Map<String, dynamic>,
            ),
    );

Map<String, dynamic> _$VoteItemModelToJson(VoteItemModel instance) =>
    <String, dynamic>{
      'voteId': instance.voteId,
      'createdBy': instance.createdBy,
      'voteUserInfo': instance.voteUserInfo,
    };
