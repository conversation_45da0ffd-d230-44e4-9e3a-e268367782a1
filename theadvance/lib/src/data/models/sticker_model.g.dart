// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sticker_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

StickerModel _$StickerModelFromJson(Map<String, dynamic> json) => StickerModel(
  items: (json['items'] is List)
      ? (json['items'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : StickerItemsModel.fromJson(e as Map<String, dynamic>),
            )
            .toList()
      : [],
);

Map<String, dynamic> _$StickerModelToJson(StickerModel instance) =>
    <String, dynamic>{'items': instance.items};

StickerItemsModel _$StickerItemsModelFromJson(Map<String, dynamic> json) =>
    StickerItemsModel(
      content: json['content']?.toString(),
      isParticipating: bool.tryParse(json['isParticipating'].toString()),
      groupNum: double.tryParse(json['groupNum'].toString())?.toInt(),
      startPos: double.tryParse(json['startPos'].toString())?.toInt(),
      endPos: double.tryParse(json['endPos'].toString())?.toInt(),
    );

Map<String, dynamic> _$StickerItemsModelToJson(StickerItemsModel instance) =>
    <String, dynamic>{
      'content': instance.content,
      'isParticipating': instance.isParticipating,
      'groupNum': instance.groupNum,
      'startPos': instance.startPos,
      'endPos': instance.endPos,
    };
