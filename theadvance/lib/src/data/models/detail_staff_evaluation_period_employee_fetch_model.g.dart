// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'detail_staff_evaluation_period_employee_fetch_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DetailStaffEvaluationPeriodEmployeeFetchModel
_$DetailStaffEvaluationPeriodEmployeeFetchModelFromJson(
  Map<String, dynamic> json,
) => DetailStaffEvaluationPeriodEmployeeFetchModel(
  items:
      ((json['items'] is List)
          ? (json['items'] as List<dynamic>?)
                ?.map(
                  (e) => e == null || e is! Map
                      ? null
                      : StaffModel.fromJson(e as Map<String, dynamic>),
                )
                .toList()
          : []) ??
      const [],
);

Map<String, dynamic> _$DetailStaffEvaluationPeriodEmployeeFetchModelToJson(
  DetailStaffEvaluationPeriodEmployeeFetchModel instance,
) => <String, dynamic>{'items': instance.items};

StaffModel _$StaffModelFromJson(Map<String, dynamic> json) => StaffModel(
  avatar: json['Avatar']?.toString(),
  appraisalPeriodId: json['AppraisalPeriodID']?.toString(),
  objectAppraisalId: json['ObjectAppraisalID']?.toString(),
  objectAppraisalName: json['ObjectAppraisalName']?.toString(),
  employeeId: json['EmployeeID']?.toString(),
  employeeName: json['EmployeeName']?.toString(),
  employeeAppraisalName: json['EmployeeAppraisalName']?.toString(),
  positionId: json['PositionID']?.toString(),
  positionName: json['PositionName']?.toString(),
  employeeAppraisalId: json['EmployeeAppraisalID']?.toString(),
  statusId: json['StatusID']?.toString(),
  statusName: json['StatusName']?.toString(),
);

Map<String, dynamic> _$StaffModelToJson(StaffModel instance) =>
    <String, dynamic>{
      'Avatar': instance.avatar,
      'AppraisalPeriodID': instance.appraisalPeriodId,
      'ObjectAppraisalID': instance.objectAppraisalId,
      'ObjectAppraisalName': instance.objectAppraisalName,
      'EmployeeID': instance.employeeId,
      'EmployeeName': instance.employeeName,
      'EmployeeAppraisalName': instance.employeeAppraisalName,
      'PositionID': instance.positionId,
      'PositionName': instance.positionName,
      'EmployeeAppraisalID': instance.employeeAppraisalId,
      'StatusID': instance.statusId,
      'StatusName': instance.statusName,
    };
