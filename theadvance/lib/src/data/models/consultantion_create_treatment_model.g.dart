// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'consultantion_create_treatment_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ConsultationTreatmentDetalModel _$ConsultationTreatmentDetalModelFromJson(
  Map<String, dynamic> json,
) => ConsultationTreatmentDetalModel(
  items: (json['items'] is List)
      ? (json['items'] as List<dynamic>?)
            ?.map(
              (e) => ConsultationTreatmentDetalItemModel.fromJson(
                e as Map<String, dynamic>,
              ),
            )
            .toList()
      : [],
);

ConsultationTreatmentDetalItemModel
_$ConsultationTreatmentDetalItemModelFromJson(Map<String, dynamic> json) =>
    ConsultationTreatmentDetalItemModel(
      treatmentRecordId: double.tryParse(
        json['TreatmentRecordID'].toString(),
      )?.toInt(),
      treatNo: double.tryParse(json['TreatNo'].toString())?.toInt(),
      treatDate: json['TreatDate'] == null
          ? null
          : DateTime.tryParse(json['TreatDate'].toString()),
      lsServiceId: double.tryParse(json['LSServiceID'].toString())?.toInt(),
      advEmp: json['AdvEmp'],
      advEmpId: json['AdvEmpID'],
      reExamDate: json['ReExamDate'],
      quantity: double.tryParse(json['Quantity'].toString())?.toInt(),
      mayNames: json['MayNames']?.toString(),
      ttbdNames: json['TTBDNames']?.toString(),
      maumucNames: json['MaumucNames']?.toString(),
      thoigianId: json['ThoigianID']?.toString(),
      zoneNames: json['ZoneNames']?.toString(),
      balanceQuantity: json['BalanceQuantity'],
      postSaiId: json['PostSaiID']?.toString(),
      treatDetailHtml: json['TreatDetailHtml']?.toString(),
      tanSo: json['TanSo']?.toString(),
      shot: json['Shot']?.toString(),
      buocSong: json['BuocSong']?.toString(),
      dauMay: json['DauMay']?.toString(),
    );
