// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'chat_search_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ChatSearchModel _$ChatSearchModelFromJson(Map<String, dynamic> json) =>
    ChatSearchModel(
      total: double.tryParse(json['total'].toString())?.toInt(),
      limit: double.tryParse(json['limit'].toString())?.toInt(),
      items: (json['items'] is List)
          ? (json['items'] as List<dynamic>)
                .map(
                  (e) => e == null || e is! Map
                      ? null
                      : ChatItemsModel.fromJson(e as Map<String, dynamic>),
                )
                .toList()
          : [],
    );

Map<String, dynamic> _$ChatSearchModelToJson(ChatSearchModel instance) =>
    <String, dynamic>{
      'total': instance.total,
      'limit': instance.limit,
      'items': instance.items,
    };
