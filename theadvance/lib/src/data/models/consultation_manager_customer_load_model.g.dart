// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'consultation_manager_customer_load_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ConsultationManagerCustomerLoadModel
_$ConsultationManagerCustomerLoadModelFromJson(Map<String, dynamic> json) =>
    ConsultationManagerCustomerLoadModel(
      items: (json['items'] is List)
          ? (json['items'] as List<dynamic>)
                .map(
                  (e) => e == null || e is! Map
                      ? null
                      : ConsultationManagerCustomerLoadItemsModel.fromJson(
                          e as Map<String, dynamic>,
                        ),
                )
                .toList()
          : [],
    );

Map<String, dynamic> _$ConsultationManagerCustomerLoadModelToJson(
  ConsultationManagerCustomerLoadModel instance,
) => <String, dynamic>{'items': instance.items};

ConsultationManagerCustomerLoadItemsModel
_$ConsultationManagerCustomerLoadItemsModelFromJson(
  Map<String, dynamic> json,
) => ConsultationManagerCustomerLoadItemsModel(
  customerName: json['customerName']?.toString(),
  customerAvatar: json['customerAvatar']?.toString(),
  comeHours: json['comeHours']?.toString(),
  assignStatus: json['assignStatus']?.toString(),
  customerCode: json['customerCode']?.toString(),
  customerId: double.tryParse(json['customerId'].toString())?.toInt(),
  noVisit: json['noVisit']?.toString(),
  groupRevenue: json['groupRevenue']?.toString(),
  isNew: json['isNew']?.toString(),
  assignId: json['assignId']?.toString(),
  serviceName: json['serviceName']?.toString(),
  serviceId: json['serviceId']?.toString(),
  workId: json['workId']?.toString(),
  workName: json['workName']?.toString(),
  totalMinutes: json['totalMinutes']?.toString(),
  startTime: json['startTime']?.toString(),
  dealType: json['dealType']?.toString(),
  currentMinutes: json['currentMinutes']?.toString(),
  bedCode: json['bedCode']?.toString(),
  roomName: json['roomName']?.toString(),
  roomCode: json['roomCode']?.toString(),
  empList: (json['empList'] is List)
      ? (json['empList'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : PxEmployeeModel.fromJson(e as Map<String, dynamic>),
            )
            .toList()
      : [],
  status: json['status']?.toString(),
  statusText: json['statusText']?.toString(),
  fullCustomerCode: json['fullCustomerCode']?.toString(),
  lsLevel1ID: json['lsLevel1ID']?.toString(),
);

Map<String, dynamic> _$ConsultationManagerCustomerLoadItemsModelToJson(
  ConsultationManagerCustomerLoadItemsModel instance,
) => <String, dynamic>{
  'customerName': instance.customerName,
  'customerAvatar': instance.customerAvatar,
  'comeHours': instance.comeHours,
  'assignStatus': instance.assignStatus,
  'customerCode': instance.customerCode,
  'customerId': instance.customerId,
  'noVisit': instance.noVisit,
  'groupRevenue': instance.groupRevenue,
  'isNew': instance.isNew,
  'assignId': instance.assignId,
  'serviceName': instance.serviceName,
  'serviceId': instance.serviceId,
  'workId': instance.workId,
  'workName': instance.workName,
  'totalMinutes': instance.totalMinutes,
  'startTime': instance.startTime,
  'dealType': instance.dealType,
  'currentMinutes': instance.currentMinutes,
  'bedCode': instance.bedCode,
  'roomName': instance.roomName,
  'roomCode': instance.roomCode,
  'empList': instance.empList,
  'status': instance.status,
  'statusText': instance.statusText,
  'fullCustomerCode': instance.fullCustomerCode,
  'lsLevel1ID': instance.lsLevel1ID,
};
