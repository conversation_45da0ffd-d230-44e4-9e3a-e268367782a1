// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'medical_log_detail_doctor_list_get_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class MedicalLogDetailDoctorListGetModelAdapter
    extends TypeAdapter<MedicalLogDetailDoctorListGetModel> {
  @override
  final int typeId = 139;

  @override
  MedicalLogDetailDoctorListGetModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return MedicalLogDetailDoctorListGetModel()
      ..items = (fields[0] as List?)?.cast<EmployeeModel?>();
  }

  @override
  void write(BinaryWriter writer, MedicalLogDetailDoctorListGetModel obj) {
    writer
      ..writeByte(1)
      ..writeByte(0)
      ..write(obj.items);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MedicalLogDetailDoctorListGetModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MedicalLogDetailDoctorListGetModel _$MedicalLogDetailDoctorListGetModelFromJson(
  Map<String, dynamic> json,
) => MedicalLogDetailDoctorListGetModel()
  ..items = (json['items'] is List)
      ? (json['items'] as List<dynamic>?)
            ?.map(
              (e) => e == null || e is! Map
                  ? null
                  : EmployeeModel.fromJson(e as Map<String, dynamic>),
            )
            .toList()
      : [];

Map<String, dynamic> _$MedicalLogDetailDoctorListGetModelToJson(
  MedicalLogDetailDoctorListGetModel instance,
) => <String, dynamic>{'items': instance.items};

DoctorModel _$DoctorModelFromJson(Map<String, dynamic> json) => DoctorModel(
  id: json['id']?.toString(),
  code: json['code']?.toString(),
  name: json['name']?.toString(),
);

Map<String, dynamic> _$DoctorModelToJson(DoctorModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'code': instance.code,
      'name': instance.name,
    };
