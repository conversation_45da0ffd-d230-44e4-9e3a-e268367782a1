// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ticket_created_type_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TicketCreatedTypeModel _$TicketCreatedTypeModelFromJson(
  Map<String, dynamic> json,
) => TicketCreatedTypeModel(
  docs: (json['docs'] is List)
      ? (json['docs'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : TicketCreatedTypeItemsModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
);

Map<String, dynamic> _$TicketCreatedTypeModelToJson(
  TicketCreatedTypeModel instance,
) => <String, dynamic>{'docs': instance.docs};

TicketCreatedTypeItemsModel _$TicketCreatedTypeItemsModelFromJson(
  Map<String, dynamic> json,
) => TicketCreatedTypeItemsModel(
  id: json['id']?.toString(),
  title: json['title']?.toString(),
  organizationId: json['organizationId']?.toString(),
  isSpecial: bool.tryParse(json['isSpecial'].toString()),
);

Map<String, dynamic> _$TicketCreatedTypeItemsModelToJson(
  TicketCreatedTypeItemsModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'title': instance.title,
  'organizationId': instance.organizationId,
  'isSpecial': instance.isSpecial,
};
