// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'medical_product_create_products_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class MedicalProductCreationProductsModelAdapter
    extends TypeAdapter<MedicalProductCreationProductsModel> {
  @override
  final int typeId = 141;

  @override
  MedicalProductCreationProductsModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return MedicalProductCreationProductsModel()
      ..items = (fields[0] as List?)?.cast<MedicalProductModel?>();
  }

  @override
  void write(BinaryWriter writer, MedicalProductCreationProductsModel obj) {
    writer
      ..writeByte(1)
      ..writeByte(0)
      ..write(obj.items);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MedicalProductCreationProductsModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MedicalProductCreationProductsModel
_$MedicalProductCreationProductsModelFromJson(Map<String, dynamic> json) =>
    MedicalProductCreationProductsModel()
      ..items = (json['items'] is List)
          ? (json['items'] as List<dynamic>?)
                ?.map(
                  (e) => e == null || e is! Map
                      ? null
                      : MedicalProductModel.fromJson(e as Map<String, dynamic>),
                )
                .toList()
          : [];

Map<String, dynamic> _$MedicalProductCreationProductsModelToJson(
  MedicalProductCreationProductsModel instance,
) => <String, dynamic>{'items': instance.items};

MedicalProductModel _$MedicalProductModelFromJson(Map<String, dynamic> json) =>
    MedicalProductModel(
      product: json['product']?.toString(),
      lsProductId: json['ls_product_id']?.toString(),
      lsProductCode: json['ls_product_code']?.toString(),
      unit: json['unit']?.toString(),
    );

Map<String, dynamic> _$MedicalProductModelToJson(
  MedicalProductModel instance,
) => <String, dynamic>{
  'product': instance.product,
  'ls_product_id': instance.lsProductId,
  'ls_product_code': instance.lsProductCode,
  'unit': instance.unit,
};
