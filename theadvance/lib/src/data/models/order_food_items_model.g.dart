// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'order_food_items_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

OrderFoodItemsModel _$OrderFoodItemsModelFromJson(Map<String, dynamic> json) =>
    OrderFoodItemsModel(
      bookingTime: json['bookingTime']?.toString(),
      mealType: json['mealType']?.toString(),
      status: json['status']?.toString(),
      id: json['id']?.toString(),
      qrCode: json['qrCode'] == null || json['qrCode'] is! Map
          ? null
          : OrderFoodItemsQrCodeModel.fromJson(
              json['qrCode'] as Map<String, dynamic>,
            ),
      cancelTime: json['cancelTime']?.toString(),
      mealTypeText: json['mealTypeText']?.toString(),
      bookingDate: json['bookingDate']?.toString(),
      statusText: json['statusText']?.toString(),
      address: json['address']?.toString(),
      feedback: json['feedback'] == null || json['feedback'] is! Map
          ? null
          : OrderFoodItemsFeedbackModel.fromJson(
              json['feedback'] as Map<String, dynamic>,
            ),
    );

Map<String, dynamic> _$OrderFoodItemsModelToJson(
  OrderFoodItemsModel instance,
) => <String, dynamic>{
  'bookingTime': instance.bookingTime,
  'mealType': instance.mealType,
  'status': instance.status,
  'id': instance.id,
  'qrCode': instance.qrCode,
  'cancelTime': instance.cancelTime,
  'mealTypeText': instance.mealTypeText,
  'bookingDate': instance.bookingDate,
  'statusText': instance.statusText,
  'address': instance.address,
  'feedback': instance.feedback,
};

OrderFoodItemsQrCodeModel _$OrderFoodItemsQrCodeModelFromJson(
  Map<String, dynamic> json,
) => OrderFoodItemsQrCodeModel(
  data: json['data'] == null || json['data'] is! Map
      ? null
      : OrderFoodItemsQrCodeDataModel.fromJson(
          json['data'] as Map<String, dynamic>,
        ),
  type: json['type']?.toString(),
);

Map<String, dynamic> _$OrderFoodItemsQrCodeModelToJson(
  OrderFoodItemsQrCodeModel instance,
) => <String, dynamic>{'data': instance.data, 'type': instance.type};

OrderFoodItemsQrCodeDataModel _$OrderFoodItemsQrCodeDataModelFromJson(
  Map<String, dynamic> json,
) => OrderFoodItemsQrCodeDataModel(
  code: json['code']?.toString(),
  date: json['date']?.toString(),
);

Map<String, dynamic> _$OrderFoodItemsQrCodeDataModelToJson(
  OrderFoodItemsQrCodeDataModel instance,
) => <String, dynamic>{'code': instance.code, 'date': instance.date};

OrderFoodItemsFeedbackModel _$OrderFoodItemsFeedbackModelFromJson(
  Map<String, dynamic> json,
) => OrderFoodItemsFeedbackModel(
  starCount: double.tryParse(json['starCount'].toString())?.toInt(),
  comment: json['comment']?.toString(),
  attachment: (json['attachment'] is List)
      ? (json['attachment'] as List<dynamic>?)
            ?.map((e) => AttachmentModel.fromJson(e as Map<String, dynamic>))
            .toList()
      : [],
);

Map<String, dynamic> _$OrderFoodItemsFeedbackModelToJson(
  OrderFoodItemsFeedbackModel instance,
) => <String, dynamic>{
  'starCount': instance.starCount,
  'comment': instance.comment,
  'attachment': instance.attachment,
};
