// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'list_note_category_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ListNoteCategoryModel _$ListNoteCategoryModelFromJson(
  Map<String, dynamic> json,
) => ListNoteCategoryModel()
  ..listNoteCategory = (json['items'] is List)
      ? (json['items'] as List<dynamic>?)
            ?.map(
              (e) => e == null || e is! Map
                  ? null
                  : NoteCategoryModel.fromJson(e as Map<String, dynamic>),
            )
            .toList()
      : [];

Map<String, dynamic> _$ListNoteCategoryModelToJson(
  ListNoteCategoryModel instance,
) => <String, dynamic>{'items': instance.listNoteCategory};

NoteCategoryModel _$NoteCategoryModelFromJson(Map<String, dynamic> json) =>
    NoteCategoryModel(
      title: json['title']?.toString(),
      id: json['id']?.toString(),
    );

Map<String, dynamic> _$NoteCategoryModelToJson(NoteCategoryModel instance) =>
    <String, dynamic>{'title': instance.title, 'id': instance.id};
