// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'chat_get_conversation_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ChatGetConversationModel _$ChatGetConversationModelFromJson(
  Map<String, dynamic> json,
) => ChatGetConversationModel(
  id: json['id']?.toString(),
  avatar: json['avatar']?.toString(),
  name: json['name']?.toString(),
  description: json['description']?.toString(),
  unreadCount: double.tryParse(json['unreadCount'].toString())?.toInt(),
  members: (json['members'] is List)
      ? (json['members'] as List<dynamic>).map((e) => e?.toString()).toList()
      : [],
  membersInfo: (json['membersInfo'] is List)
      ? (json['membersInfo'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : ChatGetConversationMembersInfoModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
  lastMessageInfo:
      json['lastMessageInfo'] == null || json['lastMessageInfo'] is! Map
      ? null
      : ChatItemsModel.fromJson(
          json['lastMessageInfo'] as Map<String, dynamic>,
        ),
  updatedAt: json['updatedAt']?.toString(),
  isGroup: bool.tryParse(json['isGroup'].toString()),
  type: json['type']?.toString(),
  conversationDetails:
      json['conversationDetails'] == null || json['conversationDetails'] is! Map
      ? null
      : ChatGetConversationConversationDetailsModel.fromJson(
          json['conversationDetails'] as Map<String, dynamic>,
        ),
);

Map<String, dynamic> _$ChatGetConversationModelToJson(
  ChatGetConversationModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'avatar': instance.avatar,
  'name': instance.name,
  'description': instance.description,
  'unreadCount': instance.unreadCount,
  'members': instance.members,
  'membersInfo': instance.membersInfo,
  'lastMessageInfo': instance.lastMessageInfo,
  'updatedAt': instance.updatedAt,
  'isGroup': instance.isGroup,
  'type': instance.type,
  'conversationDetails': instance.conversationDetails,
};

ChatGetConversationMembersInfoModel
_$ChatGetConversationMembersInfoModelFromJson(Map<String, dynamic> json) =>
    ChatGetConversationMembersInfoModel(
      username: json['username']?.toString(),
      avatar: json['avatar']?.toString(),
      id: json['id']?.toString(),
      name: json['name']?.toString(),
      departmentName: json['departmentName']?.toString(),
      phone: json['phone']?.toString(),
    );

Map<String, dynamic> _$ChatGetConversationMembersInfoModelToJson(
  ChatGetConversationMembersInfoModel instance,
) => <String, dynamic>{
  'username': instance.username,
  'avatar': instance.avatar,
  'id': instance.id,
  'name': instance.name,
  'departmentName': instance.departmentName,
  'phone': instance.phone,
};

ChatGetConversationConversationDetailsModel
_$ChatGetConversationConversationDetailsModelFromJson(
  Map<String, dynamic> json,
) => ChatGetConversationConversationDetailsModel(
  username: json['username']?.toString(),
  lastSeenAt: json['lastSeenAt']?.toString(),
  lastSeenMessageId: json['lastSeenMessageId']?.toString(),
  isMute: bool.tryParse(json['isMute'].toString()),
  isPin: bool.tryParse(json['isPin'].toString()),
  role: json['role']?.toString(),
);

Map<String, dynamic> _$ChatGetConversationConversationDetailsModelToJson(
  ChatGetConversationConversationDetailsModel instance,
) => <String, dynamic>{
  'username': instance.username,
  'lastSeenAt': instance.lastSeenAt,
  'lastSeenMessageId': instance.lastSeenMessageId,
  'isMute': instance.isMute,
  'isPin': instance.isPin,
  'role': instance.role,
};

ChatGetConversationLastMessageInfoReplyMarkupModel
_$ChatGetConversationLastMessageInfoReplyMarkupModelFromJson(
  Map<String, dynamic> json,
) => ChatGetConversationLastMessageInfoReplyMarkupModel(
  inlineKeyboard: json['inlineKeyboard'] as List<dynamic>,
);

Map<String, dynamic> _$ChatGetConversationLastMessageInfoReplyMarkupModelToJson(
  ChatGetConversationLastMessageInfoReplyMarkupModel instance,
) => <String, dynamic>{'inlineKeyboard': instance.inlineKeyboard};

ChatGetConversationLastMessageInfoForwardModel
_$ChatGetConversationLastMessageInfoForwardModelFromJson(
  Map<String, dynamic> json,
) => ChatGetConversationLastMessageInfoForwardModel(
  attachment: (json['attachment'] is List)
      ? (json['attachment'] as List<dynamic>)
            .map(
              (e) =>
                  ChatItemsAttachmentModel.fromJson(e as Map<String, dynamic>),
            )
            .toList()
      : [],
);

Map<String, dynamic> _$ChatGetConversationLastMessageInfoForwardModelToJson(
  ChatGetConversationLastMessageInfoForwardModel instance,
) => <String, dynamic>{'attachment': instance.attachment};

ChatGetConversationLastMessageInfoCreatedByInfoModel
_$ChatGetConversationLastMessageInfoCreatedByInfoModelFromJson(
  Map<String, dynamic> json,
) => ChatGetConversationLastMessageInfoCreatedByInfoModel(
  username: json['username']?.toString(),
  avatar: json['avatar']?.toString(),
  id: json['id']?.toString(),
  name: json['name']?.toString(),
);

Map<String, dynamic>
_$ChatGetConversationLastMessageInfoCreatedByInfoModelToJson(
  ChatGetConversationLastMessageInfoCreatedByInfoModel instance,
) => <String, dynamic>{
  'username': instance.username,
  'avatar': instance.avatar,
  'id': instance.id,
  'name': instance.name,
};
