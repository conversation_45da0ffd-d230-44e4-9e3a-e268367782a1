// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'dev_mini_app_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DevMiniAppModel _$DevMiniAppModelFromJson(Map<String, dynamic> json) =>
    DevMiniAppModel(
      docs: (json['docs'] is List)
          ? (json['docs'] as List<dynamic>)
                .map(
                  (e) => e == null || e is! Map
                      ? null
                      : DevMiniAppDocsModel.fromJson(e as Map<String, dynamic>),
                )
                .toList()
          : [],
      totalDocs: double.tryParse(json['totalDocs'].toString())?.toInt(),
      limit: double.tryParse(json['limit'].toString())?.toInt(),
      totalPages: double.tryParse(json['totalPages'].toString())?.toInt(),
      page: double.tryParse(json['page'].toString())?.toInt(),
      pagingCounter: double.tryParse(json['pagingCounter'].toString())?.toInt(),
      hasPrevPage: bool.tryParse(json['hasPrevPage'].toString()),
      hasNextPage: bool.tryParse(json['hasNextPage'].toString()),
      prevPage: json['prevPage']?.toString(),
      nextPage: json['nextPage']?.toString(),
    );

Map<String, dynamic> _$DevMiniAppModelToJson(DevMiniAppModel instance) =>
    <String, dynamic>{
      'docs': instance.docs,
      'totalDocs': instance.totalDocs,
      'limit': instance.limit,
      'totalPages': instance.totalPages,
      'page': instance.page,
      'pagingCounter': instance.pagingCounter,
      'hasPrevPage': instance.hasPrevPage,
      'hasNextPage': instance.hasNextPage,
      'prevPage': instance.prevPage,
      'nextPage': instance.nextPage,
    };

DevMiniAppDocsModel _$DevMiniAppDocsModelFromJson(Map<String, dynamic> json) =>
    DevMiniAppDocsModel(
      id: json['id']?.toString(),
      name: json['name']?.toString(),
      shortName: json['shortName']?.toString(),
      description: json['description']?.toString(),
      tags: (json['tags'] is List)
          ? (json['tags'] as List<dynamic>)
                .map(
                  (e) => e == null || e is! Map
                      ? null
                      : DevMiniAppDocsTagsModel.fromJson(
                          e as Map<String, dynamic>,
                        ),
                )
                .toList()
          : [],
      icon: json['icon']?.toString(),
      photo: json['photo']?.toString(),
      status: json['status']?.toString(),
      createdBy: json['createdBy']?.toString(),
      createdAt: json['createdAt']?.toString(),
      updatedAt: json['updatedAt']?.toString(),
      activeTag: json['activeTag']?.toString(),
      updatedBy: json['updatedBy']?.toString(),
      mode: json['mode']?.toString(),
      webAppUrl: json['webAppUrl']?.toString(),
      public: bool.tryParse(json['public'].toString()),
    );

Map<String, dynamic> _$DevMiniAppDocsModelToJson(
  DevMiniAppDocsModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'shortName': instance.shortName,
  'description': instance.description,
  'tags': instance.tags,
  'icon': instance.icon,
  'photo': instance.photo,
  'status': instance.status,
  'createdBy': instance.createdBy,
  'createdAt': instance.createdAt,
  'updatedAt': instance.updatedAt,
  'activeTag': instance.activeTag,
  'updatedBy': instance.updatedBy,
  'mode': instance.mode,
  'webAppUrl': instance.webAppUrl,
  'public': instance.public,
};

DevMiniAppDocsTagsModel _$DevMiniAppDocsTagsModelFromJson(
  Map<String, dynamic> json,
) => DevMiniAppDocsTagsModel(
  name: json['name']?.toString(),
  webAppUrl: json['webAppUrl']?.toString(),
);

Map<String, dynamic> _$DevMiniAppDocsTagsModelToJson(
  DevMiniAppDocsTagsModel instance,
) => <String, dynamic>{'name': instance.name, 'webAppUrl': instance.webAppUrl};
