// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'customer_profile_get_consultation_history_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class CustomerProfileGetConsultationHistoryModelAdapter
    extends TypeAdapter<CustomerProfileGetConsultationHistoryModel> {
  @override
  final int typeId = 128;

  @override
  CustomerProfileGetConsultationHistoryModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return CustomerProfileGetConsultationHistoryModel()
      ..items = (fields[0] as List?)?.cast<CustomerConsultationHistoryModel?>();
  }

  @override
  void write(
    BinaryWriter writer,
    CustomerProfileGetConsultationHistoryModel obj,
  ) {
    writer
      ..writeByte(1)
      ..writeByte(0)
      ..write(obj.items);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CustomerProfileGetConsultationHistoryModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CustomerProfileGetConsultationHistoryModel
_$CustomerProfileGetConsultationHistoryModelFromJson(
  Map<String, dynamic> json,
) =>
    CustomerProfileGetConsultationHistoryModel()
      ..items = (json['items'] is List)
          ? (json['items'] as List<dynamic>?)
                ?.map(
                  (e) => e == null || e is! Map
                      ? null
                      : CustomerConsultationHistoryModel.fromJson(
                          e as Map<String, dynamic>,
                        ),
                )
                .toList()
          : [];

Map<String, dynamic> _$CustomerProfileGetConsultationHistoryModelToJson(
  CustomerProfileGetConsultationHistoryModel instance,
) => <String, dynamic>{'items': instance.items};

CustomerConsultationHistoryModel _$CustomerConsultationHistoryModelFromJson(
  Map<String, dynamic> json,
) => CustomerConsultationHistoryModel(
  id: json['id']?.toString(),
  date: json['date']?.toString(),
  takeEmployeeName: json['take_employee_name']?.toString(),
  consultationMoney: json['consultation_money']?.toString(),
  doctorMoney: json['doctor_money']?.toString(),
  consultationContent: json['consultation_content']?.toString(),
  plan: json['plan']?.toString(),
  isSendBot: bool.tryParse(json['is_send_bot'].toString()),
);

Map<String, dynamic> _$CustomerConsultationHistoryModelToJson(
  CustomerConsultationHistoryModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'date': instance.date,
  'take_employee_name': instance.takeEmployeeName,
  'consultation_money': instance.consultationMoney,
  'doctor_money': instance.doctorMoney,
  'consultation_content': instance.consultationContent,
  'plan': instance.plan,
  'is_send_bot': instance.isSendBot,
};
