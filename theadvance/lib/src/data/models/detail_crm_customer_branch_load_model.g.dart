// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'detail_crm_customer_branch_load_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DetailCrmCustomerBranchLoadModel _$DetailCrmCustomerBranchLoadModelFromJson(
  Map<String, dynamic> json,
) => DetailCrmCustomerBranchLoadModel(
  items: (json['items'] is List)
      ? (json['items'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : DetailCrmCustomerBranchLoadItemsModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
);

Map<String, dynamic> _$DetailCrmCustomerBranchLoadModelToJson(
  DetailCrmCustomerBranchLoadModel instance,
) => <String, dynamic>{'items': instance.items};

DetailCrmCustomerBranchLoadItemsModel
_$DetailCrmCustomerBranchLoadItemsModelFromJson(Map<String, dynamic> json) =>
    DetailCrmCustomerBranchLoadItemsModel(
      id: double.tryParse(json['id'].toString())?.toInt(),
      name: json['name']?.toString(),
    );

Map<String, dynamic> _$DetailCrmCustomerBranchLoadItemsModelToJson(
  DetailCrmCustomerBranchLoadItemsModel instance,
) => <String, dynamic>{'id': instance.id, 'name': instance.name};
