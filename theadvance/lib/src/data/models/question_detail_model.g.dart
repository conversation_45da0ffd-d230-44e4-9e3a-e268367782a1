// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'question_detail_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

QuestionDetailModel _$QuestionDetailModelFromJson(Map<String, dynamic> json) =>
    QuestionDetailModel(
      questionSeq: double.tryParse(json['QuestionSeq'].toString())?.toInt(),
      rowKey: double.tryParse(json['RowKey'].toString())?.toInt(),
      appraisalPeriodId: double.tryParse(
        json['AppraisalPeriodID'].toString(),
      )?.toInt(),
      employeeId: json['EmployeeID']?.toString(),
      employeeAppraisalId: json['EmployeeAppraisalID']?.toString(),
      objectAppraisalId: json['ObjectAppraisalID']?.toString(),
      jobTitlePeriodId: double.tryParse(
        json['JobTitlePeriodID'].toString(),
      )?.toInt(),
      positionId: json['PositionID']?.toString(),
      questionName: json['QuestionName']?.toString(),
      criteriaName: json['CriteriaName']?.toString(),
      groupCriteriaName: json['GroupCriteriaName']?.toString(),
      status: bool.tryParse(json['Status'].toString()),
      answers: (json['Answers'] is List)
          ? (json['Answers'] as List<dynamic>?)
                ?.map(
                  (e) => QuestionDetailItemModel.fromJson(
                    e as Map<String, dynamic>,
                  ),
                )
                .toList()
          : [],
    );

QuestionDetailItemModel _$QuestionDetailItemModelFromJson(
  Map<String, dynamic> json,
) => QuestionDetailItemModel(
  criteriaLevelId: double.tryParse(json['CriteriaLevelID'].toString())?.toInt(),
  descriptions: json['Descriptions']?.toString(),
  levelId: double.tryParse(json['LevelID'].toString())?.toInt(),
  selected: double.tryParse(json['Selected'].toString())?.toInt(),
);
