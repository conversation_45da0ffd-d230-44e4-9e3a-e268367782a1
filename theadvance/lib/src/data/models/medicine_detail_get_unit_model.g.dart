// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'medicine_detail_get_unit_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class MedicineDetailGetUnitModelAdapter
    extends TypeAdapter<MedicineDetailGetUnitModel> {
  @override
  final int typeId = 133;

  @override
  MedicineDetailGetUnitModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return MedicineDetailGetUnitModel()
      ..items = (fields[0] as List?)?.cast<MedicineUnitModel?>();
  }

  @override
  void write(BinaryWriter writer, MedicineDetailGetUnitModel obj) {
    writer
      ..writeByte(1)
      ..writeByte(0)
      ..write(obj.items);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MedicineDetailGetUnitModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MedicineDetailGetUnitModel _$MedicineDetailGetUnitModelFromJson(
  Map<String, dynamic> json,
) => MedicineDetailGetUnitModel()
  ..items = (json['items'] is List)
      ? (json['items'] as List<dynamic>?)
            ?.map(
              (e) => e == null || e is! Map
                  ? null
                  : MedicineUnitModel.fromJson(e as Map<String, dynamic>),
            )
            .toList()
      : [];

Map<String, dynamic> _$MedicineDetailGetUnitModelToJson(
  MedicineDetailGetUnitModel instance,
) => <String, dynamic>{'items': instance.items};

MedicineUnitModel _$MedicineUnitModelFromJson(Map<String, dynamic> json) =>
    MedicineUnitModel(
      id: json['id']?.toString(),
      text: json['text']?.toString(),
    );

Map<String, dynamic> _$MedicineUnitModelToJson(MedicineUnitModel instance) =>
    <String, dynamic>{'id': instance.id, 'text': instance.text};
