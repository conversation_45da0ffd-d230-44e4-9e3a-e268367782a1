// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'comment_list_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CommentListModel _$CommentListModelFromJson(Map<String, dynamic> json) =>
    CommentListModel(
      total: double.tryParse(json['total'].toString())?.toInt(),
      limit: double.tryParse(json['limit'].toString())?.toInt(),
      page: double.tryParse(json['page'].toString())?.toInt(),
      items: (json['items'] is List)
          ? (json['items'] as List<dynamic>)
                .map(
                  (e) => e == null || e is! Map
                      ? null
                      : CommentListItemsModel.fromJson(
                          e as Map<String, dynamic>,
                        ),
                )
                .toList()
          : [],
    );

Map<String, dynamic> _$CommentListModelToJson(CommentListModel instance) =>
    <String, dynamic>{
      'total': instance.total,
      'limit': instance.limit,
      'page': instance.page,
      'items': instance.items,
    };

CommentListItemsModel _$CommentListItemsModelFromJson(
  Map<String, dynamic> json,
) => CommentListItemsModel(
  id: json['id']?.toString(),
  postId: json['postId']?.toString(),
  content: json['content']?.toString(),
  attachment: (json['attachment'] is List)
      ? (json['attachment'] as List<dynamic>?)
            ?.map((e) => AttachmentModel.fromJson(e as Map<String, dynamic>))
            .toList()
      : [],
  status: json['status']?.toString(),
  createdBy: json['createdBy']?.toString(),
  createdAt: json['createdAt']?.toString(),
  updatedAt: json['updatedAt']?.toString(),
  updatedBy: json['updatedBy']?.toString(),
  createdByInfo: json['createdByInfo'] == null || json['createdByInfo'] is! Map
      ? null
      : CreatedByInfoModel.fromJson(
          json['createdByInfo'] as Map<String, dynamic>,
        ),
  reactCount: double.tryParse(json['reactCount'].toString())?.toInt(),
  commentCount: double.tryParse(json['commentCount'].toString())?.toInt(),
  reactInfo: json['reactInfo'] == null || json['reactInfo'] is! Map
      ? null
      : ReactInfoModel.fromJson(json['reactInfo'] as Map<String, dynamic>),
  mention: (json['mention'] is List)
      ? (json['mention'] as List<dynamic>?)
            ?.map((e) => MentionModel.fromJson(e as Map<String, dynamic>))
            .toList()
      : [],
);

Map<String, dynamic> _$CommentListItemsModelToJson(
  CommentListItemsModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'postId': instance.postId,
  'content': instance.content,
  'attachment': instance.attachment,
  'status': instance.status,
  'createdBy': instance.createdBy,
  'createdAt': instance.createdAt,
  'updatedAt': instance.updatedAt,
  'updatedBy': instance.updatedBy,
  'createdByInfo': instance.createdByInfo,
  'reactCount': instance.reactCount,
  'commentCount': instance.commentCount,
  'reactInfo': instance.reactInfo,
  'mention': instance.mention,
};
