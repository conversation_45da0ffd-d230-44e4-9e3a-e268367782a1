// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'group_chat_detail_update_member_rule_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GroupChatDetailUpdateMemberRuleModel
_$GroupChatDetailUpdateMemberRuleModelFromJson(Map<String, dynamic> json) =>
    GroupChatDetailUpdateMemberRuleModel(
      conversationId: json['conversationId']?.toString(),
      rules: (json['rules'] is List)
          ? (json['rules'] as List<dynamic>)
                .map(
                  (e) => e == null || e is! Map
                      ? null
                      : GroupChatDetailUpdateMemberRuleRulesModel.fromJson(
                          e as Map<String, dynamic>,
                        ),
                )
                .toList()
          : [],
      role: json['role']?.toString(),
      username: json['username']?.toString(),
      roleText: json['roleText']?.toString(),
    );

Map<String, dynamic> _$GroupChatDetailUpdateMemberRuleModelToJson(
  GroupChatDetailUpdateMemberRuleModel instance,
) => <String, dynamic>{
  'conversationId': instance.conversationId,
  'rules': instance.rules,
  'role': instance.role,
  'username': instance.username,
  'roleText': instance.roleText,
};

GroupChatDetailUpdateMemberRuleRulesModel
_$GroupChatDetailUpdateMemberRuleRulesModelFromJson(
  Map<String, dynamic> json,
) => GroupChatDetailUpdateMemberRuleRulesModel(
  id: json['id']?.toString(),
  name: json['name']?.toString(),
  actions: json['actions']?.toString(),
  subject: json['subject']?.toString(),
  createdAt: json['createdAt']?.toString(),
  updatedAt: json['updatedAt']?.toString(),
);

Map<String, dynamic> _$GroupChatDetailUpdateMemberRuleRulesModelToJson(
  GroupChatDetailUpdateMemberRuleRulesModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'actions': instance.actions,
  'subject': instance.subject,
  'createdAt': instance.createdAt,
  'updatedAt': instance.updatedAt,
};
