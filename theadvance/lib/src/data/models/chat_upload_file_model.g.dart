// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'chat_upload_file_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ChatUploadFileModel _$ChatUploadFileModelFromJson(Map<String, dynamic> json) =>
    ChatUploadFileModel(
      filename: json['filename']?.toString(),
      originalname: json['originalname']?.toString(),
      id: json['id']?.toString(),
      size: double.tryParse(json['size'].toString())?.toInt(),
      mimetype: json['mimetype']?.toString(),
      host: json['host']?.toString(),
      link: json['link']?.toString(),
      thumbnail: json['thumbnail']?.toString(),
      path: json['path']?.toString(),
      duration: double.tryParse(json['duration'].toString())?.toInt(),
      waveform: (json['waveform'] is List)
          ? (json['waveform'] as List<dynamic>?)
                ?.map((e) => double.tryParse(e.toString())?.toInt())
                .toList()
          : [],
      checksum: json['checksum']?.toString(),
    );

Map<String, dynamic> _$ChatUploadFileModelToJson(
  ChatUploadFileModel instance,
) => <String, dynamic>{
  'filename': instance.filename,
  'originalname': instance.originalname,
  'id': instance.id,
  'size': instance.size,
  'duration': instance.duration,
  'mimetype': instance.mimetype,
  'host': instance.host,
  'link': instance.link,
  'thumbnail': instance.thumbnail,
  'path': instance.path,
  'waveform': instance.waveform,
  'checksum': instance.checksum,
};
