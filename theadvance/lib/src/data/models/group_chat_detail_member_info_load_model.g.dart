// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'group_chat_detail_member_info_load_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GroupChatDetailMemberInfoLoadModel _$GroupChatDetailMemberInfoLoadModelFromJson(
  Map<String, dynamic> json,
) => GroupChatDetailMemberInfoLoadModel(
  total: double.tryParse(json['total'].toString())?.toInt(),
  limit: double.tryParse(json['limit'].toString())?.toInt(),
  page: double.tryParse(json['page'].toString())?.toInt(),
  items: (json['items'] is List)
      ? (json['items'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : GroupChatDetailMemberInfoLoadItemsModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
);

Map<String, dynamic> _$GroupChatDetailMemberInfoLoadModelToJson(
  GroupChatDetailMemberInfoLoadModel instance,
) => <String, dynamic>{
  'total': instance.total,
  'limit': instance.limit,
  'page': instance.page,
  'items': instance.items,
};

GroupChatDetailMemberInfoLoadItemsModel
_$GroupChatDetailMemberInfoLoadItemsModelFromJson(Map<String, dynamic> json) =>
    GroupChatDetailMemberInfoLoadItemsModel(
      username: json['username']?.toString(),
      avatar: json['avatar']?.toString(),
      createdAt: json['createdAt']?.toString(),
      departmentName: json['departmentName']?.toString(),
      id: json['id']?.toString(),
      isOnline: bool.tryParse(json['isOnline'].toString()),
      name: json['name']?.toString(),
      org: json['org']?.toString(),
      phone: json['phone']?.toString(),
      product: json['product']?.toString(),
      status: json['status']?.toString(),
      type: json['type']?.toString(),
      updatedAt: json['updatedAt']?.toString(),
      role: json['role']?.toString(),
      roleText: json['roleText']?.toString(),
    );

Map<String, dynamic> _$GroupChatDetailMemberInfoLoadItemsModelToJson(
  GroupChatDetailMemberInfoLoadItemsModel instance,
) => <String, dynamic>{
  'username': instance.username,
  'avatar': instance.avatar,
  'createdAt': instance.createdAt,
  'departmentName': instance.departmentName,
  'id': instance.id,
  'isOnline': instance.isOnline,
  'name': instance.name,
  'org': instance.org,
  'phone': instance.phone,
  'product': instance.product,
  'status': instance.status,
  'type': instance.type,
  'updatedAt': instance.updatedAt,
  'role': instance.role,
  'roleText': instance.roleText,
};
