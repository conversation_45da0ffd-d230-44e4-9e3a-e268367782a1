// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'service_detail_doctor_fetch_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ServiceDetailDoctorFetchModel _$ServiceDetailDoctorFetchModelFromJson(
  Map<String, dynamic> json,
) => ServiceDetailDoctorFetchModel(
  items: (json['items'] is List)
      ? (json['items'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : ServiceDetailDoctorFetchItemsModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
);

Map<String, dynamic> _$ServiceDetailDoctorFetchModelToJson(
  ServiceDetailDoctorFetchModel instance,
) => <String, dynamic>{'items': instance.items};

ServiceDetailDoctorFetchItemsModel _$ServiceDetailDoctorFetchItemsModelFromJson(
  Map<String, dynamic> json,
) => ServiceDetailDoctorFetchItemsModel(
  doctorCode: json['DoctorCode']?.toString(),
  doctorName: json['DoctorName']?.toString(),
);

Map<String, dynamic> _$ServiceDetailDoctorFetchItemsModelToJson(
  ServiceDetailDoctorFetchItemsModel instance,
) => <String, dynamic>{
  'DoctorCode': instance.doctorCode,
  'DoctorName': instance.doctorName,
};
