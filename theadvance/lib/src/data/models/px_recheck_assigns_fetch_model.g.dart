// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'px_recheck_assigns_fetch_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PxRecheckAssignsFetchModel _$PxRecheckAssignsFetchModelFromJson(
  Map<String, dynamic> json,
) => PxRecheckAssignsFetchModel(
  masterInfo: json['MasterInfo'] == null || json['MasterInfo'] is! Map
      ? null
      : PxRecheckAssignsFetchMasterInfoModel.fromJson(
          json['MasterInfo'] as Map<String, dynamic>,
        ),
  detailInfo: (json['DetailInfo'] is List)
      ? (json['DetailInfo'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : PxRecheckAssignsFetchDetailInfoModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
);

Map<String, dynamic> _$PxRecheckAssignsFetchModelToJson(
  PxRecheckAssignsFetchModel instance,
) => <String, dynamic>{
  'MasterInfo': instance.masterInfo,
  'DetailInfo': instance.detailInfo,
};

PxRecheckAssignsFetchMasterInfoModel
_$PxRecheckAssignsFetchMasterInfoModelFromJson(Map<String, dynamic> json) =>
    PxRecheckAssignsFetchMasterInfoModel(
      rowkey: double.tryParse(json['Rowkey'].toString())?.toInt(),
      transactionDate: json['TransactionDate']?.toString(),
      status: double.tryParse(json['Status'].toString())?.toInt(),
      usageID: json['UsageID'],
      startTime: json['StartTime']?.toString(),
      finishTime: json['FinishTime']?.toString(),
      itemID: json['ItemID']?.toString(),
      itemName: json['ItemName']?.toString(),
      itemGroupID: json['ItemGroupID']?.toString(),
      departmentID: json['DepartmentID']?.toString(),
      partnerID: json['PartnerID']?.toString(),
      customerID: json['CustomerID']?.toString(),
      duration: double.tryParse(json['Duration'].toString())?.toInt(),
      createdDate: json['CreatedDate']?.toString(),
      approveDate: json['ApproveDate']?.toString(),
      approveBy: json['ApproveBy']?.toString(),
      rejectedDate: json['RejectedDate'],
      rejectedBy: json['RejectedBy'],
      isAllowedSupport: bool.tryParse(json['IsAllowedSupport'].toString()),
    );

Map<String, dynamic> _$PxRecheckAssignsFetchMasterInfoModelToJson(
  PxRecheckAssignsFetchMasterInfoModel instance,
) => <String, dynamic>{
  'Rowkey': instance.rowkey,
  'TransactionDate': instance.transactionDate,
  'Status': instance.status,
  'UsageID': instance.usageID,
  'StartTime': instance.startTime,
  'FinishTime': instance.finishTime,
  'ItemID': instance.itemID,
  'ItemName': instance.itemName,
  'ItemGroupID': instance.itemGroupID,
  'DepartmentID': instance.departmentID,
  'PartnerID': instance.partnerID,
  'CustomerID': instance.customerID,
  'Duration': instance.duration,
  'CreatedDate': instance.createdDate,
  'ApproveDate': instance.approveDate,
  'ApproveBy': instance.approveBy,
  'RejectedDate': instance.rejectedDate,
  'RejectedBy': instance.rejectedBy,
  'IsAllowedSupport': instance.isAllowedSupport,
};

PxRecheckAssignsFetchDetailInfoModel
_$PxRecheckAssignsFetchDetailInfoModelFromJson(
  Map<String, dynamic> json,
) => PxRecheckAssignsFetchDetailInfoModel(
  detailRowkey: double.tryParse(json['DetailRowkey'].toString())?.toInt(),
  stepName: json['StepName']?.toString(),
  startTime: json['StartTime']?.toString(),
  finishTime: json['FinishTime']?.toString(),
  specifiedTime: double.tryParse(json['SpecifiedTime'].toString())?.toInt(),
  actualTime: double.tryParse(json['ActualTime'].toString())?.toInt(),
  status: double.tryParse(json['Status'].toString())?.toInt(),
  pauseStatus: bool.tryParse(json['PauseStatus'].toString()),
  notes: json['Notes']?.toString(),
  stepListDetail: (json['StepListDetail'] is List)
      ? (json['StepListDetail'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : PxRecheckAssignsFetchDetailInfoStepListDetailModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
  employees: (json['Employees'] is List)
      ? (json['Employees'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : PxRecheckAssignsFetchDetailInfoEmployeesModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
  images: (json['Images'] is List)
      ? (json['Images'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : PxRecheckAssignsFetchDetailInfoImagesModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
  pausesTracking: (json['PausesTracking'] is List)
      ? (json['PausesTracking'] as List<dynamic>?)
            ?.map(
              (e) => PausesTrackingModel.fromJson(e as Map<String, dynamic>),
            )
            .toList()
      : [],
);

Map<String, dynamic> _$PxRecheckAssignsFetchDetailInfoModelToJson(
  PxRecheckAssignsFetchDetailInfoModel instance,
) => <String, dynamic>{
  'DetailRowkey': instance.detailRowkey,
  'StepName': instance.stepName,
  'StartTime': instance.startTime,
  'FinishTime': instance.finishTime,
  'SpecifiedTime': instance.specifiedTime,
  'ActualTime': instance.actualTime,
  'Status': instance.status,
  'PauseStatus': instance.pauseStatus,
  'Notes': instance.notes,
  'StepListDetail': instance.stepListDetail,
  'Employees': instance.employees,
  'Images': instance.images,
  'PausesTracking': instance.pausesTracking,
};

PausesTrackingModel _$PausesTrackingModelFromJson(Map<String, dynamic> json) =>
    PausesTrackingModel(
      pauseTimeStart: json['PauseTimeStart']?.toString(),
      pauseTimeEnd: json['PauseTimeEnd']?.toString(),
      totalPauseTime: double.tryParse(
        json['TotalPauseTime'].toString(),
      )?.toInt(),
    );

Map<String, dynamic> _$PausesTrackingModelToJson(
  PausesTrackingModel instance,
) => <String, dynamic>{
  'PauseTimeStart': instance.pauseTimeStart,
  'PauseTimeEnd': instance.pauseTimeEnd,
  'TotalPauseTime': instance.totalPauseTime,
};

PxRecheckAssignsFetchDetailInfoStepListDetailModel
_$PxRecheckAssignsFetchDetailInfoStepListDetailModelFromJson(
  Map<String, dynamic> json,
) => PxRecheckAssignsFetchDetailInfoStepListDetailModel(
  stepDetailID: double.tryParse(json['StepDetailID'].toString())?.toInt(),
  stepDetailName: json['StepDetailName']?.toString(),
  detailTime: double.tryParse(json['DetailTime'].toString())?.toInt(),
);

Map<String, dynamic> _$PxRecheckAssignsFetchDetailInfoStepListDetailModelToJson(
  PxRecheckAssignsFetchDetailInfoStepListDetailModel instance,
) => <String, dynamic>{
  'StepDetailID': instance.stepDetailID,
  'StepDetailName': instance.stepDetailName,
  'DetailTime': instance.detailTime,
};

PxRecheckAssignsFetchDetailInfoEmployeesModel
_$PxRecheckAssignsFetchDetailInfoEmployeesModelFromJson(
  Map<String, dynamic> json,
) => PxRecheckAssignsFetchDetailInfoEmployeesModel(
  employeeID: json['EmployeeID']?.toString(),
  employeeName: json['EmployeeName']?.toString(),
  isSupportEmployee: bool.tryParse(json['IsSupportEmployee'].toString()),
);

Map<String, dynamic> _$PxRecheckAssignsFetchDetailInfoEmployeesModelToJson(
  PxRecheckAssignsFetchDetailInfoEmployeesModel instance,
) => <String, dynamic>{
  'EmployeeID': instance.employeeID,
  'EmployeeName': instance.employeeName,
  'IsSupportEmployee': instance.isSupportEmployee,
};

PxRecheckAssignsFetchDetailInfoImagesModel
_$PxRecheckAssignsFetchDetailInfoImagesModelFromJson(
  Map<String, dynamic> json,
) => PxRecheckAssignsFetchDetailInfoImagesModel(
  imageURL: json['ImageURL']?.toString(),
  isAfterPhoto: bool.tryParse(json['IsAfterPhoto'].toString()),
);

Map<String, dynamic> _$PxRecheckAssignsFetchDetailInfoImagesModelToJson(
  PxRecheckAssignsFetchDetailInfoImagesModel instance,
) => <String, dynamic>{
  'ImageURL': instance.imageURL,
  'IsAfterPhoto': instance.isAfterPhoto,
};
