// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'create_chat_group_user_load_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CreateChatGroupUserLoadModel _$CreateChatGroupUserLoadModelFromJson(
  Map<String, dynamic> json,
) => CreateChatGroupUserLoadModel(
  limit: double.tryParse(json['limit'].toString())?.toInt(),
  items: (json['items'] is List)
      ? (json['items'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : CreateChatGroupUserLoadItemsModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
);

Map<String, dynamic> _$CreateChatGroupUserLoadModelToJson(
  CreateChatGroupUserLoadModel instance,
) => <String, dynamic>{'limit': instance.limit, 'items': instance.items};

CreateChatGroupUserLoadItemsModel _$CreateChatGroupUserLoadItemsModelFromJson(
  Map<String, dynamic> json,
) => CreateChatGroupUserLoadItemsModel(
  avatar: json['avatar']?.toString(),
  username: json['username']?.toString(),
  name: json['name']?.toString(),
  phone: json['phone']?.toString(),
  departmentName: json['departmentName']?.toString(),
);

Map<String, dynamic> _$CreateChatGroupUserLoadItemsModelToJson(
  CreateChatGroupUserLoadItemsModel instance,
) => <String, dynamic>{
  'avatar': instance.avatar,
  'username': instance.username,
  'name': instance.name,
  'phone': instance.phone,
  'departmentName': instance.departmentName,
};
