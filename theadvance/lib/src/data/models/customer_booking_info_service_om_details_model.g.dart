// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'customer_booking_info_service_om_details_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CustomerBookingInfoServiceOmDetailsModel
_$CustomerBookingInfoServiceOmDetailsModelFromJson(Map<String, dynamic> json) =>
    CustomerBookingInfoServiceOmDetailsModel(
      items: (json['items'] is List)
          ? (json['items'] as List<dynamic>)
                .map(
                  (e) => e == null || e is! Map
                      ? null
                      : CustomerBookingInfoServiceOmDetailsItemsModel.fromJson(
                          e as Map<String, dynamic>,
                        ),
                )
                .toList()
          : [],
    );

Map<String, dynamic> _$CustomerBookingInfoServiceOmDetailsModelToJson(
  CustomerBookingInfoServiceOmDetailsModel instance,
) => <String, dynamic>{'items': instance.items};

CustomerBookingInfoServiceOmDetailsItemsModel
_$CustomerBookingInfoServiceOmDetailsItemsModelFromJson(
  Map<String, dynamic> json,
) => CustomerBookingInfoServiceOmDetailsItemsModel(
  rowID: double.tryParse(json['RowID'].toString())?.toInt(),
  customerID: double.tryParse(json['CustomerID'].toString())?.toInt(),
  lSServiceID: double.tryParse(json['LSServiceID'].toString())?.toInt(),
  treatmentRecordID: double.tryParse(
    json['TreatmentRecordID'].toString(),
  )?.toInt(),
  treatmentDate: json['TreatmentDate']?.toString(),
  treatmentZone: json['TreatmentZone']?.toString(),
  totalMoney: double.tryParse(json['TotalMoney'].toString())?.toInt(),
  treatmentTime: json['TreatmentTime']?.toString(),
  treatmentProduct: json['TreatmentProduct']?.toString(),
  weightOfCustomer: json['WeightOfCustomer']?.toString(),
  heightOfCustomer: json['HeightOfCustomer']?.toString(),
  measureOfCustomer: json['SizeOfCustomer']?.toString(),
  assureWeight: json['AssureWeight']?.toString(),
  assureSize: json['AssureSize']?.toString(),
  loseWeight: json['LoseWeight']?.toString(),
  loseSize: json['LoseSize']?.toString(),
  takeCareByEmpID: double.tryParse(json['TakeCareByEmpID'].toString())?.toInt(),
  takeCareByEmpName: json['TakeCareByEmpName']?.toString(),
  treatmentEnd: bool.tryParse(json['TreatmentEnd'].toString()),
);

Map<String, dynamic> _$CustomerBookingInfoServiceOmDetailsItemsModelToJson(
  CustomerBookingInfoServiceOmDetailsItemsModel instance,
) => <String, dynamic>{
  'RowID': instance.rowID,
  'CustomerID': instance.customerID,
  'LSServiceID': instance.lSServiceID,
  'TreatmentRecordID': instance.treatmentRecordID,
  'TreatmentDate': instance.treatmentDate,
  'TreatmentZone': instance.treatmentZone,
  'TotalMoney': instance.totalMoney,
  'TreatmentTime': instance.treatmentTime,
  'TreatmentProduct': instance.treatmentProduct,
  'WeightOfCustomer': instance.weightOfCustomer,
  'HeightOfCustomer': instance.heightOfCustomer,
  'SizeOfCustomer': instance.measureOfCustomer,
  'AssureWeight': instance.assureWeight,
  'AssureSize': instance.assureSize,
  'LoseWeight': instance.loseWeight,
  'LoseSize': instance.loseSize,
  'TakeCareByEmpID': instance.takeCareByEmpID,
  'TakeCareByEmpName': instance.takeCareByEmpName,
  'TreatmentEnd': instance.treatmentEnd,
};
