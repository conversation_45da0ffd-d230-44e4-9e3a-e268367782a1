// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'chat_list_search_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ChatListSearchModel _$ChatListSearchModelFromJson(Map<String, dynamic> json) =>
    ChatListSearchModel(
      items: (json['items'] is List)
          ? (json['items'] as List<dynamic>)
                .map(
                  (e) => e == null || e is! Map
                      ? null
                      : ChatListSearchItemsModel.fromJson(
                          e as Map<String, dynamic>,
                        ),
                )
                .toList()
          : [],
    );

Map<String, dynamic> _$ChatListSearchModelToJson(
  ChatListSearchModel instance,
) => <String, dynamic>{'items': instance.items};

ChatListSearchItemsModel _$ChatListSearchItemsModelFromJson(
  Map<String, dynamic> json,
) => ChatListSearchItemsModel(
  id: json['id']?.toString(),
  avatar: json['avatar']?.toString(),
  name: json['name']?.toString(),
  isGroup: bool.tryParse(json['isGroup'].toString()),
  username: json['username']?.toString(),
  type: json['type']?.toString(),
  unreadCount: double.tryParse(json['unreadCount'].toString())?.toInt(),
);

Map<String, dynamic> _$ChatListSearchItemsModelToJson(
  ChatListSearchItemsModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'avatar': instance.avatar,
  'name': instance.name,
  'isGroup': instance.isGroup,
  'username': instance.username,
  'type': instance.type,
  'unreadCount': instance.unreadCount,
};
