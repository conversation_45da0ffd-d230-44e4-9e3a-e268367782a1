// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'detail_crm_customer_service_fetch_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DetailCrmCustomerServiceFetchModel _$DetailCrmCustomerServiceFetchModelFromJson(
  Map<String, dynamic> json,
) => DetailCrmCustomerServiceFetchModel(
  items: (json['items'] is List)
      ? (json['items'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : DetailCrmServiceItemModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
);

Map<String, dynamic> _$DetailCrmCustomerServiceFetchModelToJson(
  DetailCrmCustomerServiceFetchModel instance,
) => <String, dynamic>{'items': instance.items};

DetailCrmServiceItemModel _$DetailCrmServiceItemModelFromJson(
  Map<String, dynamic> json,
) => DetailCrmServiceItemModel(
  description: json['description']?.toString(),
  dealTypeName: json['deal_type_name']?.toString(),
  deparmentCode: json['deparment_code']?.toString(),
  qtyBuy: double.tryParse(json['qty_buy'].toString())?.toInt(),
  qtyRemain: double.tryParse(json['qty_remain'].toString())?.toInt(),
  dealDate: json['deal_date']?.toString(),
  dealDetails: (json['deal_details'] is List)
      ? (json['deal_details'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : CrmServiceDealModel.fromJson(e as Map<String, dynamic>),
            )
            .toList()
      : [],
);

Map<String, dynamic> _$DetailCrmServiceItemModelToJson(
  DetailCrmServiceItemModel instance,
) => <String, dynamic>{
  'description': instance.description,
  'deal_type_name': instance.dealTypeName,
  'deparment_code': instance.deparmentCode,
  'qty_buy': instance.qtyBuy,
  'qty_remain': instance.qtyRemain,
  'deal_date': instance.dealDate,
  'deal_details': instance.dealDetails,
};

CrmServiceDealModel _$CrmServiceDealModelFromJson(Map<String, dynamic> json) =>
    CrmServiceDealModel(
      actionName: json['action_name']?.toString(),
      qty: double.tryParse(json['qty'].toString())?.toInt(),
      note: json['note']?.toString(),
      dealDate: json['deal_date']?.toString(),
    );

Map<String, dynamic> _$CrmServiceDealModelToJson(
  CrmServiceDealModel instance,
) => <String, dynamic>{
  'action_name': instance.actionName,
  'qty': instance.qty,
  'note': instance.note,
  'deal_date': instance.dealDate,
};
