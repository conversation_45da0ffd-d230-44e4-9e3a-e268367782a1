// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'fit_customer_info_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FitCustomerInfoModel _$FitCustomerInfoModelFromJson(
  Map<String, dynamic> json,
) => FitCustomerInfoModel(
  items: (json['items'] is List)
      ? (json['items'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : FitCustomerInfoItemsModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
);

Map<String, dynamic> _$FitCustomerInfoModelToJson(
  FitCustomerInfoModel instance,
) => <String, dynamic>{'items': instance.items};

FitCustomerInfoItemsModel _$FitCustomerInfoItemsModelFromJson(
  Map<String, dynamic> json,
) => FitCustomerInfoItemsModel(
  rowID: double.tryParse(json['RowID'].toString())?.toInt(),
  customerID: double.tryParse(json['CustomerID'].toString())?.toInt(),
  age: double.tryParse(json['Age'].toString())?.toInt(),
  occupation: json['Occupation']?.toString(),
  height: json['Height']?.toString(),
  weight: json['Weight']?.toString(),
  measure: json['Measure']?.toString(),
  overWeight: json['OverWeight']?.toString(),
  typeOfBones: json['TypeOfBones']?.toString(),
  typeOfBirth: json['TypeOfBirth']?.toString(),
  treatment: json['Treatment']?.toString(),
  meal: json['Meal']?.toString(),
  doExercise: json['DoExercise']?.toString(),
  housework: json['Housework']?.toString(),
  historyFit: json['HistoryFit']?.toString(),
);

Map<String, dynamic> _$FitCustomerInfoItemsModelToJson(
  FitCustomerInfoItemsModel instance,
) => <String, dynamic>{
  'RowID': instance.rowID,
  'CustomerID': instance.customerID,
  'Age': instance.age,
  'Occupation': instance.occupation,
  'Height': instance.height,
  'Weight': instance.weight,
  'Measure': instance.measure,
  'OverWeight': instance.overWeight,
  'TypeOfBones': instance.typeOfBones,
  'TypeOfBirth': instance.typeOfBirth,
  'Treatment': instance.treatment,
  'Meal': instance.meal,
  'DoExercise': instance.doExercise,
  'Housework': instance.housework,
  'HistoryFit': instance.historyFit,
};
