// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'medical_log_detail_get_origin_status_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MedicalLogDetailGetOriginStatusModel
_$MedicalLogDetailGetOriginStatusModelFromJson(Map<String, dynamic> json) =>
    MedicalLogDetailGetOriginStatusModel()
      ..listOriginStatus = (json['items'] is List)
          ? (json['items'] as List<dynamic>?)
                ?.map(
                  (e) => e == null || e is! Map
                      ? null
                      : CatalogModel.fromJson(e as Map<String, dynamic>),
                )
                .toList()
          : [];

Map<String, dynamic> _$MedicalLogDetailGetOriginStatusModelToJson(
  MedicalLogDetailGetOriginStatusModel instance,
) => <String, dynamic>{'items': instance.listOriginStatus};
