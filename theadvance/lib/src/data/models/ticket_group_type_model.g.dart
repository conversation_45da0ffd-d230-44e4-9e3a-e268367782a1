// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ticket_group_type_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TicketGroupTypeModel _$TicketGroupTypeModelFromJson(
  Map<String, dynamic> json,
) => TicketGroupTypeModel(
  docs: (json['docs'] is List)
      ? (json['docs'] as List<dynamic>)
            .map(
              (e) => e == null || e is! Map
                  ? null
                  : TicketGroupTypeItemsModel.fromJson(
                      e as Map<String, dynamic>,
                    ),
            )
            .toList()
      : [],
);

Map<String, dynamic> _$TicketGroupTypeModelToJson(
  TicketGroupTypeModel instance,
) => <String, dynamic>{'docs': instance.docs};

TicketGroupTypeItemsModel _$TicketGroupTypeItemsModelFromJson(
  Map<String, dynamic> json,
) => TicketGroupTypeItemsModel(
  id: json['id']?.toString(),
  title: json['title']?.toString(),
  organizationId: json['organizationId']?.toString(),
);

Map<String, dynamic> _$TicketGroupTypeItemsModelToJson(
  TicketGroupTypeItemsModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'title': instance.title,
  'organizationId': instance.organizationId,
};
