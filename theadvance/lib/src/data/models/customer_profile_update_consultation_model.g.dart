// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'customer_profile_update_consultation_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class CustomerProfileUpdateConsultationModelAdapter
    extends TypeAdapter<CustomerProfileUpdateConsultationModel> {
  @override
  final int typeId = 129;

  @override
  CustomerProfileUpdateConsultationModel read(BinaryReader reader) {
    return CustomerProfileUpdateConsultationModel();
  }

  @override
  void write(BinaryWriter writer, CustomerProfileUpdateConsultationModel obj) {
    writer.writeByte(0);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CustomerProfileUpdateConsultationModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CustomerProfileUpdateConsultationModel
_$CustomerProfileUpdateConsultationModelFromJson(Map<String, dynamic> json) =>
    CustomerProfileUpdateConsultationModel();

Map<String, dynamic> _$CustomerProfileUpdateConsultationModelToJson(
  CustomerProfileUpdateConsultationModel instance,
) => <String, dynamic>{};
