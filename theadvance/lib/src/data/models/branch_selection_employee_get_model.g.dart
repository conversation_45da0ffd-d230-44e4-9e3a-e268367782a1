// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'branch_selection_employee_get_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class BranchSelectionEmployeeGetModelAdapter
    extends TypeAdapter<BranchSelectionEmployeeGetModel> {
  @override
  final int typeId = 135;

  @override
  BranchSelectionEmployeeGetModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return BranchSelectionEmployeeGetModel()
      ..items = (fields[0] as List?)?.cast<EmployeeModel?>();
  }

  @override
  void write(BinaryWriter writer, BranchSelectionEmployeeGetModel obj) {
    writer
      ..writeByte(1)
      ..writeByte(0)
      ..write(obj.items);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BranchSelectionEmployeeGetModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BranchSelectionEmployeeGetModel _$BranchSelectionEmployeeGetModelFromJson(
  Map<String, dynamic> json,
) => BranchSelectionEmployeeGetModel()
  ..items = (json['items'] is List)
      ? (json['items'] as List<dynamic>?)
            ?.map(
              (e) => e == null || e is! Map
                  ? null
                  : EmployeeModel.fromJson(e as Map<String, dynamic>),
            )
            .toList()
      : [];

Map<String, dynamic> _$BranchSelectionEmployeeGetModelToJson(
  BranchSelectionEmployeeGetModel instance,
) => <String, dynamic>{'items': instance.items};

EmployeeModel _$EmployeeModelFromJson(Map<String, dynamic> json) =>
    EmployeeModel(
      id: json['id']?.toString(),
      name: json['name']?.toString(),
      code: json['code']?.toString(),
    );

Map<String, dynamic> _$EmployeeModelToJson(EmployeeModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'code': instance.code,
      'name': instance.name,
    };
