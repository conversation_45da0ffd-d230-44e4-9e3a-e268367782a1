// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'group_chat_detail_get_rule_by_role_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GroupChatDetailGetRuleByRoleModel _$GroupChatDetailGetRuleByRoleModelFromJson(
  Map<String, dynamic> json,
) => GroupChatDetailGetRuleByRoleModel(
  role: json['role'] == null || json['role'] is! Map
      ? null
      : GroupChatDetailGetRuleByRoleRoleModel.fromJson(
          json['role'] as Map<String, dynamic>,
        ),
);

Map<String, dynamic> _$GroupChatDetailGetRuleByRoleModelToJson(
  GroupChatDetailGetRuleByRoleModel instance,
) => <String, dynamic>{'role': instance.role};

GroupChatDetailGetRuleByRoleRoleModel
_$GroupChatDetailGetRuleByRoleRoleModelFromJson(Map<String, dynamic> json) =>
    GroupChatDetailGetRuleByRoleRoleModel(
      name: json['name']?.toString(),
      rules: (json['rules'] is List)
          ? (json['rules'] as List<dynamic>)
                .map(
                  (e) => e == null || e is! Map
                      ? null
                      : GroupChatDetailGetRuleByRoleRoleRulesModel.fromJson(
                          e as Map<String, dynamic>,
                        ),
                )
                .toList()
          : [],
    );

Map<String, dynamic> _$GroupChatDetailGetRuleByRoleRoleModelToJson(
  GroupChatDetailGetRuleByRoleRoleModel instance,
) => <String, dynamic>{'name': instance.name, 'rules': instance.rules};

GroupChatDetailGetRuleByRoleRoleRulesModel
_$GroupChatDetailGetRuleByRoleRoleRulesModelFromJson(
  Map<String, dynamic> json,
) => GroupChatDetailGetRuleByRoleRoleRulesModel(
  id: json['id']?.toString(),
  name: json['name']?.toString(),
  actions: json['actions']?.toString(),
  subject: json['subject']?.toString(),
  createdAt: json['createdAt']?.toString(),
  updatedAt: json['updatedAt']?.toString(),
);

Map<String, dynamic> _$GroupChatDetailGetRuleByRoleRoleRulesModelToJson(
  GroupChatDetailGetRuleByRoleRoleRulesModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'actions': instance.actions,
  'subject': instance.subject,
  'createdAt': instance.createdAt,
  'updatedAt': instance.updatedAt,
};
