// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ticket_detail_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TicketDetailModel _$TicketDetailModelFromJson(
  Map<String, dynamic> json,
) => TicketDetailModel(
  id: json['id']?.toString(),
  content: json['content']?.toString(),
  type: json['type']?.toString(),
  typeDisplay: json['typeDisplay']?.toString(),
  customerCode: json['customerCode']?.toString(),
  serviceCode: json['serviceCode']?.toString(),
  groupId: json['groupId']?.toString(),
  groupDisplay: json['groupDisplay']?.toString(),
  state: json['state']?.toString(),
  stateDisplay: json['stateDisplay']?.toString(),
  createdAt: json['createdAt']?.toString(),
  createdBy: json['createdBy']?.toString(),
  operateAt: json['operateAt']?.toString(),
  operateBy: json['operateBy']?.toString(),
  avatar: json['avatar']?.toString(),
  owner: json['owner']?.toString(),
  operator: json['operator']?.toString(),
  customer: json['customer'] == null || json['customer'] is! Map
      ? null
      : CommonPartModel.fromJson(json['customer'] as Map<String, dynamic>),
  service: json['service'] == null || json['service'] is! Map
      ? null
      : CommonPartModel.fromJson(json['service'] as Map<String, dynamic>),
  attachments: (json['attachments'] is List)
      ? (json['attachments'] as List<dynamic>?)
            ?.map((e) => AttachmentModel.fromJson(e as Map<String, dynamic>))
            .toList()
      : [],
  deadline: json['deadline']?.toString(),
  cc: (json['cc'] is List)
      ? (json['cc'] as List<dynamic>?)
            ?.map((e) => CommonPartModel.fromJson(e as Map<String, dynamic>))
            .toList()
      : [],
  requestors: (json['requestors'] is List)
      ? (json['requestors'] as List<dynamic>?)
            ?.map((e) => CommonPartModel.fromJson(e as Map<String, dynamic>))
            .toList()
      : [],
  assignee: json['assignee'] == null || json['assignee'] is! Map
      ? null
      : CommonPartModel.fromJson(json['assignee'] as Map<String, dynamic>),
  assigneeDisplay: json['assigneeDisplay']?.toString(),
  assignType: double.tryParse(json['assignType'].toString())?.toInt(),
  ccDisplay: json['ccDisplay']?.toString(),
  requestorsDisplay: json['requestorsDisplay']?.toString(),
  deadlineDisplay: json['deadlineDisplay']?.toString(),
);
