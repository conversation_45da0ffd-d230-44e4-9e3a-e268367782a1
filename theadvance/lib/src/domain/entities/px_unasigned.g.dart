// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'px_unasigned.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PxCustomer _$PxCustomerFromJson(Map<String, dynamic> json) => PxCustomer(
  customerName: json['customerName']?.toString(),
  customerAvatar: json['customerAvatar']?.toString(),
  customerId: json['customerId']?.toString(),
  customerCode: json['customerCode']?.toString(),
  comeHours: json['comeHours']?.toString(),
  noVisit: json['noVisit']?.toString(),
  groupRevenue: json['groupRevenue']?.toString(),
  lsLevel1ID: json['lsLevel1ID']?.toString(),
  serviceName: json['serviceName']?.toString(),
  serviceCode: json['serviceCode']?.toString(),
  workName: json['workName']?.toString(),
  totalMinutes: json['totalMinutes']?.toString(),
  currentMinutes: json['currentMinutes']?.toString(),
  workId: json['workId']?.toString(),
  assignId: json['assignId']?.toString(),
  dealType: json['dealType']?.toString(),
  status: json['status']?.toString(),
  statusText: json['statusText']?.toString(),
  empList:
      ((json['empList'] is List)
          ? (json['empList'] as List<dynamic>?)
                ?.map(
                  (e) => e == null || e is! Map
                      ? null
                      : PxEmployee.fromJson(e as Map<String, dynamic>),
                )
                .toList()
          : []) ??
      const [],
  roomCode: json['roomCode']?.toString(),
  roomName: json['roomName']?.toString(),
  departmentId: json['departmentId']?.toString(),
  bedCode: json['bedCode']?.toString(),
  assignStatus: json['assignStatus']?.toString(),
  isNew: json['isNew']?.toString(),
  serviceId: json['serviceId']?.toString(),
  startTime: json['startTime']?.toString(),
  fullCustomerCode: json['fullCustomerCode']?.toString(),
  branchName: json['branchName']?.toString(),
  checkInTime: json['checkInTime']?.toString(),
  checkOutTime: json['checkOutTime']?.toString(),
  employeeStepInprogress: json['employeeStepInprogress']?.toString(),
);
