// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'px_unasigned_update_employees_fetch.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PxEmployee _$PxEmployeeFromJson(Map<String, dynamic> json) => PxEmployee(
  avatar: json['avatar']?.toString(),
  employeeName: json['employeeName']?.toString(),
  employeeId: json['employeeId']?.toString(),
  employeeCode: json['employeeCode']?.toString(),
  isWaiting: bool.tryParse(json['isWaiting'].toString()),
  isSupportEmployee: bool.tryParse(json['isSupportEmployee'].toString()),
);

Map<String, dynamic> _$PxEmployeeToJson(PxEmployee instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('avatar', instance.avatar);
  writeNotNull('employeeName', instance.employeeName);
  writeNotNull('employeeId', instance.employeeId);
  writeNotNull('employeeCode', instance.employeeCode);
  writeNotNull('isWaiting', instance.isWaiting);
  writeNotNull('isSupportEmployee', instance.isSupportEmployee);
  return val;
}
