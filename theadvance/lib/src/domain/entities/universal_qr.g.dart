// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'universal_qr.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UniversalQr _$UniversalQrFromJson(Map<String, dynamic> json) => UniversalQr(
  type: json['type']?.toString(),
  data: json['data'] == null || json['data'] is! Map
      ? null
      : UniversalQrValue.fromJson(json['data'] as Map<String, dynamic>),
);

Map<String, dynamic> _$UniversalQrToJson(UniversalQr instance) =>
    <String, dynamic>{'type': instance.type, 'data': instance.data};

UniversalQrValue _$UniversalQrValueFromJson(Map<String, dynamic> json) =>
    UniversalQrValue(
      code: json['code']?.toString(),
      date: json['date']?.toString(),
    );

Map<String, dynamic> _$UniversalQrValueToJson(UniversalQrValue instance) =>
    <String, dynamic>{'code': instance.code, 'date': instance.date};
