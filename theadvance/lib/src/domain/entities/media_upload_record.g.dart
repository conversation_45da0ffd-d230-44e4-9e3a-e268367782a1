// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'media_upload_record.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MediaUpload _$MediaUploadFromJson(Map<String, dynamic> json) => MediaUpload(
  filename: json['filename']?.toString(),
  originalname: json['originalname']?.toString(),
  id: json['id']?.toString(),
  host: json['host']?.toString(),
  link: json['link']?.toString(),
  path: json['path']?.toString(),
  size: double.tryParse(json['size'].toString())?.toInt(),
  name: json['name']?.toString(),
  mimetype: json['mimetype']?.toString(),
  thumbnail: json['thumbnail']?.toString(),
  height: double.tryParse(json['height'].toString())?.toInt(),
  width: double.tryParse(json['width'].toString())?.toInt(),
);

Map<String, dynamic> _$MediaUploadToJson(MediaUpload instance) =>
    <String, dynamic>{
      'filename': instance.filename,
      'originalname': instance.originalname,
      'id': instance.id,
      'host': instance.host,
      'link': instance.link,
      'path': instance.path,
      'size': instance.size,
      'name': instance.name,
      'mimetype': instance.mimetype,
      'thumbnail': instance.thumbnail,
      'height': instance.height,
      'width': instance.width,
    };
