// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'login_socket_access_token_get.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class LoginSocketAccessTokenGetAdapter
    extends TypeAdapter<LoginSocketAccessTokenGet> {
  @override
  final int typeId = 160;

  @override
  LoginSocketAccessTokenGet read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return LoginSocketAccessTokenGet(
      accessToken: fields[0] as String?,
      user: fields[1] as LoginSocketAccessTokenGetUser?,
    );
  }

  @override
  void write(BinaryWriter writer, LoginSocketAccessTokenGet obj) {
    writer
      ..writeByte(2)
      ..writeByte(0)
      ..write(obj.accessToken)
      ..writeByte(1)
      ..write(obj.user);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LoginSocketAccessTokenGetAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class LoginSocketAccessTokenGetUserAdapter
    extends TypeAdapter<LoginSocketAccessTokenGetUser> {
  @override
  final int typeId = 161;

  @override
  LoginSocketAccessTokenGetUser read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return LoginSocketAccessTokenGetUser(
      username: fields[0] as String?,
      avatar: fields[1] as String?,
      createdAt: fields[2] as String?,
      departmentName: fields[3] as String?,
      id: fields[4] as String?,
      isOnline: fields[5] as bool?,
      name: fields[6] as String?,
      phone: fields[7] as String?,
      product: fields[8] as String?,
      takeCareGroupId: fields[9] as String?,
      type: fields[10] as String?,
      updatedAt: fields[11] as String?,
      botMessagePermission:
          fields[12] as LoginSocketAccessTokenGetUserBotMessagePermission?,
      permission: fields[13] as ChatPermission?,
    );
  }

  @override
  void write(BinaryWriter writer, LoginSocketAccessTokenGetUser obj) {
    writer
      ..writeByte(14)
      ..writeByte(0)
      ..write(obj.username)
      ..writeByte(1)
      ..write(obj.avatar)
      ..writeByte(2)
      ..write(obj.createdAt)
      ..writeByte(3)
      ..write(obj.departmentName)
      ..writeByte(4)
      ..write(obj.id)
      ..writeByte(5)
      ..write(obj.isOnline)
      ..writeByte(6)
      ..write(obj.name)
      ..writeByte(7)
      ..write(obj.phone)
      ..writeByte(8)
      ..write(obj.product)
      ..writeByte(9)
      ..write(obj.takeCareGroupId)
      ..writeByte(10)
      ..write(obj.type)
      ..writeByte(11)
      ..write(obj.updatedAt)
      ..writeByte(12)
      ..write(obj.botMessagePermission)
      ..writeByte(13)
      ..write(obj.permission);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LoginSocketAccessTokenGetUserAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ChatPermissionAdapter extends TypeAdapter<ChatPermission> {
  @override
  final int typeId = 175;

  @override
  ChatPermission read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ChatPermission(
      record: fields[0] as RecordPermission?,
      botMessage:
          fields[1] as LoginSocketAccessTokenGetUserBotMessagePermission?,
      message: fields[2] as LoginSocketAccessTokenGetUserMessagePermission?,
    );
  }

  @override
  void write(BinaryWriter writer, ChatPermission obj) {
    writer
      ..writeByte(3)
      ..writeByte(0)
      ..write(obj.record)
      ..writeByte(1)
      ..write(obj.botMessage)
      ..writeByte(2)
      ..write(obj.message);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ChatPermissionAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class RecordPermissionAdapter extends TypeAdapter<RecordPermission> {
  @override
  final int typeId = 176;

  @override
  RecordPermission read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return RecordPermission(download: fields[0] as bool?);
  }

  @override
  void write(BinaryWriter writer, RecordPermission obj) {
    writer
      ..writeByte(1)
      ..writeByte(0)
      ..write(obj.download);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RecordPermissionAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class LoginSocketAccessTokenGetUserBotMessagePermissionAdapter
    extends TypeAdapter<LoginSocketAccessTokenGetUserBotMessagePermission> {
  @override
  final int typeId = 162;

  @override
  LoginSocketAccessTokenGetUserBotMessagePermission read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return LoginSocketAccessTokenGetUserBotMessagePermission(
      edit: fields[0] as bool?,
      copy: fields[1] as bool?,
      forward: fields[2] as bool?,
      speechToText: fields[3] as bool?,
    );
  }

  @override
  void write(
    BinaryWriter writer,
    LoginSocketAccessTokenGetUserBotMessagePermission obj,
  ) {
    writer
      ..writeByte(4)
      ..writeByte(0)
      ..write(obj.edit)
      ..writeByte(1)
      ..write(obj.copy)
      ..writeByte(2)
      ..write(obj.forward)
      ..writeByte(3)
      ..write(obj.speechToText);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LoginSocketAccessTokenGetUserBotMessagePermissionAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class LoginSocketAccessTokenGetUserMessagePermissionAdapter
    extends TypeAdapter<LoginSocketAccessTokenGetUserMessagePermission> {
  @override
  final int typeId = 202;

  @override
  LoginSocketAccessTokenGetUserMessagePermission read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return LoginSocketAccessTokenGetUserMessagePermission(
      speechToText: fields[0] as bool?,
    );
  }

  @override
  void write(
    BinaryWriter writer,
    LoginSocketAccessTokenGetUserMessagePermission obj,
  ) {
    writer
      ..writeByte(1)
      ..writeByte(0)
      ..write(obj.speechToText);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LoginSocketAccessTokenGetUserMessagePermissionAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
